import { CheckboxGroupValue } from './type';
declare const _default: import("vue").DefineComponent<{
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    lazyLoad: BooleanConstructor;
    max: {
        type: NumberConstructor;
        default: any;
    };
    name: {
        type: StringConstructor;
        default: string;
    };
    options: {
        type: import("vue").PropType<import("./type").TdCheckboxGroupProps["options"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    value: {
        type: import("vue").PropType<import("./type").TdCheckboxGroupProps["value"]>;
        default: import("./type").TdCheckboxGroupProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdCheckboxGroupProps["value"]>;
        default: import("./type").TdCheckboxGroupProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdCheckboxGroupProps["defaultValue"]>;
        default: () => import("./type").TdCheckboxGroupProps["defaultValue"];
    };
    onChange: import("vue").PropType<import("./type").TdCheckboxGroupProps["onChange"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    lazyLoad: BooleanConstructor;
    max: {
        type: NumberConstructor;
        default: any;
    };
    name: {
        type: StringConstructor;
        default: string;
    };
    options: {
        type: import("vue").PropType<import("./type").TdCheckboxGroupProps["options"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    value: {
        type: import("vue").PropType<import("./type").TdCheckboxGroupProps["value"]>;
        default: import("./type").TdCheckboxGroupProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdCheckboxGroupProps["value"]>;
        default: import("./type").TdCheckboxGroupProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdCheckboxGroupProps["defaultValue"]>;
        default: () => import("./type").TdCheckboxGroupProps["defaultValue"];
    };
    onChange: import("vue").PropType<import("./type").TdCheckboxGroupProps["onChange"]>;
}>>, {
    disabled: boolean;
    name: string;
    value: CheckboxGroupValue;
    max: number;
    defaultValue: CheckboxGroupValue;
    modelValue: CheckboxGroupValue;
    readonly: boolean;
    lazyLoad: boolean;
}, {}>;
export default _default;
