/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var Vue = require('vue');
require('@babel/runtime/helpers/toConsumableArray');
require('@babel/runtime/helpers/typeof');
require('../_chunks/dep-086ab407.js');
var index = require('../_chunks/dep-059461d7.js');
var index$2 = require('../_chunks/dep-6666de7f.js');
var index$1 = require('../_chunks/dep-e2298443.js');
require('@babel/runtime/helpers/slicedToArray');
require('../_chunks/dep-b15ee9eb.js');
require('@babel/runtime/helpers/defineProperty');
var loading_index = require('../loading/index.js');
var card_props = require('./props.js');
var isString = require('../_chunks/dep-914897c8.js');
require('../_chunks/dep-d5654a4e.js');
require('../_chunks/dep-2748f688.js');
require('../_chunks/dep-94424a57.js');
require('../_chunks/dep-febae5f4.js');
require('../_chunks/dep-ca39ce6d.js');
require('../_chunks/dep-6ae67bab.js');
require('../_chunks/dep-12e0aded.js');
require('../_chunks/dep-324af0df.js');
require('../_chunks/dep-cabb6240.js');
require('../_chunks/dep-306b2e72.js');
require('../_chunks/dep-3f3d49e4.js');
require('../_chunks/dep-76847e8b.js');
require('../_chunks/dep-f8224a9c.js');
require('../_chunks/dep-3bb82c67.js');
require('../_chunks/dep-7c14108f.js');
require('../config-provider/hooks/useConfig.js');
require('../_chunks/dep-f9e26c60.js');
require('../_chunks/dep-714d992c.js');
require('dayjs');
require('../_chunks/dep-94ff6543.js');
require('../_chunks/dep-fa8a400f.js');
require('../_chunks/dep-6b54b4a5.js');
require('../_chunks/dep-eac47ca0.js');
require('../_chunks/dep-df581fcb.js');
require('../_chunks/dep-6a71c082.js');
require('../_chunks/dep-ef3df7aa.js');
require('../_chunks/dep-6c0887f7.js');
require('../_chunks/dep-8c0a3845.js');
require('../_chunks/dep-4c75812c.js');
require('../_chunks/dep-ca4c3e97.js');
require('../_chunks/dep-6183bb4a.js');
require('../_chunks/dep-53dbb954.js');
require('../_chunks/dep-d1c7139a.js');
require('../_chunks/dep-ba035735.js');
require('@babel/runtime/helpers/createClass');
require('@babel/runtime/helpers/classCallCheck');
require('../_chunks/dep-24c31f97.js');
require('../loading/plugin.js');
require('../_chunks/dep-faade8bd.js');
require('../loading/icon/gradient.js');
require('../_chunks/dep-076bd726.js');
require('../_chunks/dep-fd4183e8.js');
require('@babel/runtime/helpers/objectWithoutProperties');
require('../_chunks/dep-2105b21f.js');
require('../_chunks/dep-f20572a3.js');
require('../_chunks/dep-8c00290f.js');
require('../loading/props.js');
require('../_chunks/dep-605cf2e8.js');
require('../_chunks/dep-925e8207.js');
require('../_chunks/dep-59fa52ec.js');
require('../_chunks/dep-66442631.js');
require('../_chunks/dep-156361a6.js');
require('../_chunks/dep-6d27c874.js');
require('../_chunks/dep-31eb9b48.js');
require('../_chunks/dep-ce7457dd.js');
require('../_chunks/dep-4023d837.js');
require('../_chunks/dep-ac7dea19.js');

function _isSlot(s) {
  return typeof s === 'function' || Object.prototype.toString.call(s) === '[object Object]' && !Vue.isVNode(s);
}
var _Card = Vue.defineComponent({
  name: "TCard",
  props: card_props["default"],
  setup: function setup(props2, _ref) {
    var slots = _ref.slots;
    var renderTNodeJSX = index.useTNodeJSX();
    var COMPONENT_NAME = index$1.usePrefixClass("card");
    var _useCommonClassName = index$2.useCommonClassName(),
      SIZE = _useCommonClassName.SIZE;
    var baseCls = Vue.computed(function () {
      var defaultClass = [COMPONENT_NAME.value];
      if (props2.size === "small") defaultClass.push("".concat(SIZE.value[props2.size]));
      if (props2.bordered) defaultClass.push("".concat(COMPONENT_NAME.value, "--bordered"));
      if (props2.shadow) defaultClass.push("".concat(COMPONENT_NAME.value, "--shadow"));
      if (props2.hoverShadow) defaultClass.push("".concat(COMPONENT_NAME.value, "--shadow-hover"));
      return defaultClass;
    });
    var headerCls = Vue.computed(function () {
      return ["".concat(COMPONENT_NAME.value, "__header"), props2.headerBordered && "".concat(COMPONENT_NAME.value, "__title--bordered")];
    });
    var headerWrapperCls = index$1.usePrefixClass("card__header-wrapper");
    var headerAvatarCls = index$1.usePrefixClass("card__avatar");
    var headerTitleCls = index$1.usePrefixClass("card__title");
    var headerSubTitleCls = index$1.usePrefixClass("card__subtitle");
    var headerDescriptionCls = index$1.usePrefixClass("card__description");
    var actionsCls = index$1.usePrefixClass("card__actions");
    var bodyCls = index$1.usePrefixClass("card__body");
    var coverCls = index$1.usePrefixClass("card__cover");
    var footerCls = index$1.usePrefixClass("card__footer");
    var footerWrapperCls = index$1.usePrefixClass("card__footer-wrapper");
    var isPoster2 = Vue.computed(function () {
      return props2.theme === "poster2";
    });
    var showTitle = Vue.computed(function () {
      return props2.title || slots.title;
    });
    var showHeader = Vue.computed(function () {
      return props2.header || slots.header;
    });
    var showSubtitle = Vue.computed(function () {
      return props2.subtitle || slots.subtitle;
    });
    var showAvatar = Vue.computed(function () {
      return props2.avatar || slots.avatar;
    });
    var showDescription = Vue.computed(function () {
      return props2.description || slots.description;
    });
    var showStatus = Vue.computed(function () {
      return props2.status || slots.status;
    });
    var showActions = Vue.computed(function () {
      return props2.actions || slots.actions;
    });
    var showFooter = Vue.computed(function () {
      return props2.footer || slots.footer;
    });
    var showCover = Vue.computed(function () {
      return props2.cover || slots.cover;
    });
    var showLoading = Vue.computed(function () {
      return props2.loading || slots.loading;
    });
    var showContent = Vue.computed(function () {
      return props2.content || slots.content || props2["default"] || slots["default"];
    });
    var isHeaderRender = Vue.computed(function () {
      return showHeader.value || showTitle.value || showSubtitle.value || showDescription.value || showAvatar.value || showStatus.value && isPoster2.value || showActions.value && !isPoster2.value;
    });
    var isFooterRender = Vue.computed(function () {
      return showFooter.value || showActions.value && isPoster2.value;
    });
    var renderHeader = function renderHeader() {
      if (showHeader.value) return Vue.createVNode("div", {
        "class": [headerCls.value, props2.headerClassName],
        "style": props2.headerStyle
      }, [renderTNodeJSX("header")]);
      return Vue.createVNode("div", {
        "class": [headerCls.value, props2.headerClassName],
        "style": props2.headerStyle
      }, [Vue.createVNode("div", {
        "class": headerWrapperCls.value
      }, [showAvatar.value && Vue.createVNode("div", {
        "class": headerAvatarCls.value
      }, [renderTNodeJSX("avatar")]), Vue.createVNode("div", null, [showTitle.value && Vue.createVNode("div", {
        "class": headerTitleCls.value
      }, [renderTNodeJSX("title")]), showSubtitle.value && Vue.createVNode("div", {
        "class": headerSubTitleCls.value
      }, [renderTNodeJSX("subtitle")]), showDescription.value && Vue.createVNode("p", {
        "class": headerDescriptionCls.value
      }, [renderTNodeJSX("description")])])]), showActions.value && !isPoster2.value && Vue.createVNode("div", {
        "class": actionsCls.value
      }, [renderTNodeJSX("actions")]), showStatus.value && Vue.createVNode("div", {
        "class": actionsCls.value
      }, [renderTNodeJSX("status")])]);
    };
    var renderCover = function renderCover() {
      var textCover = isString.isString(props2.cover);
      return Vue.createVNode("div", {
        "class": coverCls.value
      }, [textCover ? Vue.createVNode("img", {
        "src": props2.cover
      }, null) : renderTNodeJSX("cover")]);
    };
    return function () {
      var content = Vue.createVNode("div", {
        "class": baseCls.value
      }, [isHeaderRender.value ? renderHeader() : null, showCover.value ? renderCover() : null, showContent.value && Vue.createVNode("div", {
        "class": [bodyCls.value, props2.bodyClassName],
        "style": props2.bodyStyle
      }, [renderTNodeJSX("default") || renderTNodeJSX("content")]), isFooterRender.value && Vue.createVNode("div", {
        "class": [footerCls.value, props2.footerClassName],
        "style": props2.footerStyle
      }, [Vue.createVNode("div", {
        "class": footerWrapperCls.value
      }, [renderTNodeJSX("footer")]), showActions.value && isPoster2.value && Vue.createVNode("div", {
        "class": actionsCls.value
      }, [renderTNodeJSX("actions")])])]);
      if (showLoading.value) {
        return renderTNodeJSX("loading") || Vue.createVNode(loading_index.Loading, props2.loadingProps, _isSlot(content) ? content : {
          "default": function _default() {
            return [content];
          }
        });
      }
      return content;
    };
  }
});

exports["default"] = _Card;
//# sourceMappingURL=card.js.map
