{"version": 3, "file": "index.js", "sources": ["../../../components/card/index.ts"], "sourcesContent": ["import _Card from './card';\nimport { withInstall } from '@tdesign/shared-utils';\nimport { TdCardProps } from './type';\n\nimport './style';\n\nexport * from './type';\nexport type CardProps = TdCardProps;\n\nexport const Card = withInstall(_Card);\nexport default Card;\n"], "names": ["Card", "withInstall", "_Card"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASaA,IAAA,GAAOC,wBAAYC,oBAAK;;;;;"}