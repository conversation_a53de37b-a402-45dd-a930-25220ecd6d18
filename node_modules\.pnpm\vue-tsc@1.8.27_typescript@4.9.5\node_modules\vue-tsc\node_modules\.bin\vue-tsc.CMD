@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\JTNYProject\configuration-project-1\node_modules\.pnpm\vue-tsc@1.8.27_typescript@4.9.5\node_modules\vue-tsc\bin\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\vue-tsc@1.8.27_typescript@4.9.5\node_modules\vue-tsc\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\vue-tsc@1.8.27_typescript@4.9.5\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\JTNYProject\configuration-project-1\node_modules\.pnpm\vue-tsc@1.8.27_typescript@4.9.5\node_modules\vue-tsc\bin\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\vue-tsc@1.8.27_typescript@4.9.5\node_modules\vue-tsc\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\vue-tsc@1.8.27_typescript@4.9.5\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\vue-tsc.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\vue-tsc.js" %*
)
