/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var Vue = require('vue');
var _typeof = require('@babel/runtime/helpers/typeof');
var _toConsumableArray = require('@babel/runtime/helpers/toConsumableArray');
var _slicedToArray = require('@babel/runtime/helpers/slicedToArray');
var checkbox_checkbox = require('./checkbox.js');
var checkbox_checkboxGroupProps = require('./checkbox-group-props.js');
var checkbox_consts_index = require('./consts/index.js');
var index$3 = require('../_chunks/dep-872d0152.js');
var index$1 = require('../_chunks/dep-059461d7.js');
var index = require('../_chunks/dep-e2298443.js');
require('../_chunks/dep-b15ee9eb.js');
require('../_chunks/dep-086ab407.js');
var index$2 = require('../_chunks/dep-baa72039.js');
require('@babel/runtime/helpers/defineProperty');
var intersection = require('../_chunks/dep-cbee8f46.js');
var isUndefined = require('../_chunks/dep-2105b21f.js');
var isObject = require('../_chunks/dep-ca39ce6d.js');
require('./props.js');
require('../_chunks/dep-6666de7f.js');
require('../config-provider/hooks/useConfig.js');
require('../_chunks/dep-f9e26c60.js');
require('../_chunks/dep-714d992c.js');
require('../_chunks/dep-d5654a4e.js');
require('dayjs');
require('../_chunks/dep-94ff6543.js');
require('../_chunks/dep-fa8a400f.js');
require('../_chunks/dep-6b54b4a5.js');
require('../_chunks/dep-94424a57.js');
require('../_chunks/dep-febae5f4.js');
require('../_chunks/dep-eac47ca0.js');
require('../_chunks/dep-df581fcb.js');
require('../_chunks/dep-3f3d49e4.js');
require('../_chunks/dep-12e0aded.js');
require('../_chunks/dep-6a71c082.js');
require('../_chunks/dep-ef3df7aa.js');
require('../_chunks/dep-6c0887f7.js');
require('../_chunks/dep-8c0a3845.js');
require('../_chunks/dep-4c75812c.js');
require('../_chunks/dep-ca4c3e97.js');
require('../_chunks/dep-914897c8.js');
require('../_chunks/dep-6183bb4a.js');
require('../_chunks/dep-53dbb954.js');
require('../_chunks/dep-d1c7139a.js');
require('../_chunks/dep-ba035735.js');
require('../_chunks/dep-b09565a1.js');
require('../_chunks/dep-756628ef.js');
require('../_chunks/dep-7e5fc00e.js');
require('../_chunks/dep-988704d0.js');
require('../_chunks/dep-076bd726.js');
require('./hooks/useCheckboxLazyLoad.js');
require('../_chunks/dep-c9fd0e5d.js');
require('./hooks/useKeyboardEvent.js');
require('../_chunks/dep-01642c17.js');
require('../_chunks/dep-2748f688.js');
require('../_chunks/dep-6ae67bab.js');
require('../_chunks/dep-324af0df.js');
require('../_chunks/dep-cabb6240.js');
require('../_chunks/dep-306b2e72.js');
require('../_chunks/dep-76847e8b.js');
require('../_chunks/dep-f8224a9c.js');
require('../_chunks/dep-3bb82c67.js');
require('../_chunks/dep-7c14108f.js');
require('@babel/runtime/helpers/createClass');
require('@babel/runtime/helpers/classCallCheck');
require('../_chunks/dep-59fa52ec.js');
require('../_chunks/dep-a271f384.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _typeof__default = /*#__PURE__*/_interopDefaultLegacy(_typeof);
var _toConsumableArray__default = /*#__PURE__*/_interopDefaultLegacy(_toConsumableArray);
var _slicedToArray__default = /*#__PURE__*/_interopDefaultLegacy(_slicedToArray);

var _Group = Vue.defineComponent({
  name: "TCheckboxGroup",
  props: checkbox_checkboxGroupProps["default"],
  setup: function setup(props2) {
    var COMPONENT_NAME = index.usePrefixClass("checkbox-group");
    var renderTNodeJSX = index$1.useTNodeJSX();
    var isArray = Array.isArray;
    var _toRefs = Vue.toRefs(props2),
      value = _toRefs.value,
      modelValue = _toRefs.modelValue;
    var _useVModel = index$2.useVModel(value, modelValue, props2.defaultValue, props2.onChange),
      _useVModel2 = _slicedToArray__default["default"](_useVModel, 2),
      innerValue = _useVModel2[0],
      setInnerValue = _useVModel2[1];
    var optionList = Vue.ref([]);
    var intersectionLen = Vue.computed(function () {
      if (!isArray(innerValue.value)) return 0;
      var values = optionList.value.map(function (item) {
        return item.value;
      });
      var n = intersection.intersection(innerValue.value, values);
      return n.length;
    });
    var isCheckAll = Vue.computed(function () {
      var optionItems = optionList.value.filter(function (item) {
        return !item.disabled && !item.readonly && !item.checkAll;
      }).map(function (t) {
        return t.value;
      });
      var intersectionValues = intersection.intersection(optionItems, innerValue.value);
      return intersectionValues.length === optionItems.length;
    });
    var indeterminate = Vue.computed(function () {
      return !isCheckAll.value && intersectionLen.value < optionList.value.length && intersectionLen.value !== 0;
    });
    var maxExceeded = Vue.computed(function () {
      return !isUndefined.isUndefined(props2.max) && innerValue.value.length === props2.max;
    });
    Vue.watchEffect(function () {
      if (!props2.options) return [];
      optionList.value = props2.options.map(function (item) {
        return isObject.isObject(item) ? item : {
          label: String(item),
          value: item
        };
      });
    });
    var getAllCheckboxValue = function getAllCheckboxValue() {
      var checkAllVal = /* @__PURE__ */new Set();
      var uncheckAllVal = /* @__PURE__ */new Set();
      for (var i = 0, len = optionList.value.length; i < len; i++) {
        var item = optionList.value[i];
        if (item.checkAll) continue;
        if (item.disabled) {
          if (!innerValue.value.includes(item.value)) continue;else uncheckAllVal.add(item.value);
        }
        if (item.readonly) {
          if (!innerValue.value.includes(item.value)) continue;else uncheckAllVal.add(item.value);
        }
        checkAllVal.add(item.value);
        if (maxExceeded.value) break;
      }
      return {
        checkAllVal: _toConsumableArray__default["default"](checkAllVal),
        uncheckAllVal: _toConsumableArray__default["default"](uncheckAllVal)
      };
    };
    var onCheckAllChange = function onCheckAllChange(checked, context) {
      var _getAllCheckboxValue = getAllCheckboxValue(),
        checkAllVal = _getAllCheckboxValue.checkAllVal,
        uncheckAllVal = _getAllCheckboxValue.uncheckAllVal;
      var value2 = checked ? checkAllVal : uncheckAllVal;
      setInnerValue(value2, {
        e: context.e,
        type: checked ? "check" : "uncheck",
        current: void 0,
        option: void 0
      });
    };
    var handleCheckboxChange = function handleCheckboxChange(data) {
      var currentValue = data.option.value;
      if (!isArray(innerValue.value)) {
        console.warn("TDesign CheckboxGroup Warn: `value` must be an array, instead of ".concat(_typeof__default["default"](innerValue.value)));
        return;
      }
      var val = _toConsumableArray__default["default"](innerValue.value);
      if (data.checked) {
        val.push(currentValue);
      } else {
        var i = val.indexOf(currentValue);
        val.splice(i, 1);
      }
      setInnerValue(val, {
        e: data.e,
        current: data.option.value,
        option: data.option,
        type: data.checked ? "check" : "uncheck"
      });
    };
    var onCheckedChange = function onCheckedChange(p) {
      var checked = p.checked,
        checkAll = p.checkAll,
        e = p.e;
      if (checkAll) {
        onCheckAllChange(checked, {
          e: e
        });
      } else {
        handleCheckboxChange(p);
      }
    };
    var getChildComponentSlots = index$3.useChildComponentSlots();
    var getOptionListBySlots = function getOptionListBySlots() {
      var nodes = getChildComponentSlots("Checkbox");
      var arr = [];
      nodes === null || nodes === void 0 || nodes.forEach(function (node) {
        var option = node.props;
        if (!option) return;
        if (option["check-all"] === "" || option["check-all"] === true) {
          option.checkAll = true;
        }
        arr.push(option);
      });
      return arr;
    };
    Vue.provide(checkbox_consts_index.CheckboxGroupInjectionKey, Vue.computed(function () {
      return {
        name: props2.name,
        isCheckAll: isCheckAll.value,
        checkedValues: innerValue.value || [],
        maxExceeded: maxExceeded.value,
        disabled: props2.disabled,
        readonly: props2.readonly,
        indeterminate: indeterminate.value,
        handleCheckboxChange: handleCheckboxChange,
        onCheckedChange: onCheckedChange
      };
    }));
    return function () {
      var _props2$options;
      var children = null;
      if ((_props2$options = props2.options) !== null && _props2$options !== void 0 && _props2$options.length) {
        var _optionList$value;
        children = (_optionList$value = optionList.value) === null || _optionList$value === void 0 ? void 0 : _optionList$value.map(function (option, index) {
          var _innerValue$value;
          return Vue.createVNode(checkbox_checkbox["default"], Vue.mergeProps({
            "key": "".concat(option.value || "").concat(index),
            "lazyLoad": props2.lazyLoad
          }, option, {
            "index": index,
            "checked": (_innerValue$value = innerValue.value) === null || _innerValue$value === void 0 ? void 0 : _innerValue$value.includes(option.value),
            "data": option
          }), null);
        });
      } else {
        var nodes = renderTNodeJSX("default");
        optionList.value = getOptionListBySlots();
        children = nodes;
      }
      return Vue.createVNode("div", {
        "class": COMPONENT_NAME.value,
        "role": "group",
        "aria-label": "checkbox-group"
      }, [children]);
    };
  }
});

exports["default"] = _Group;
//# sourceMappingURL=group.js.map
