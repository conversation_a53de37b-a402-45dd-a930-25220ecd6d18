{"version": 3, "file": "card.js", "sources": ["../../../components/card/card.tsx"], "sourcesContent": ["import { defineComponent, computed } from 'vue';\n\nimport { useTNodeJSX, usePrefixClass, useCommonClassName } from '@tdesign/shared-hooks';\n\nimport TLoading from '../loading';\nimport props from './props';\nimport { isString } from 'lodash-es';\nimport { TdCardProps } from './type';\n\nexport default defineComponent({\n  name: 'TCard',\n  props,\n  setup(props, { slots }) {\n    const renderTNodeJSX = useTNodeJSX();\n    const COMPONENT_NAME = usePrefixClass('card');\n    const { SIZE } = useCommonClassName();\n\n    const baseCls = computed(() => {\n      const defaultClass = [COMPONENT_NAME.value];\n\n      if (props.size === 'small') defaultClass.push(`${SIZE.value[props.size]}`);\n      if (props.bordered) defaultClass.push(`${COMPONENT_NAME.value}--bordered`);\n      if (props.shadow) defaultClass.push(`${COMPONENT_NAME.value}--shadow`);\n      if (props.hoverShadow) defaultClass.push(`${COMPONENT_NAME.value}--shadow-hover`);\n\n      return defaultClass;\n    });\n\n    const headerCls = computed(() => [\n      `${COMPONENT_NAME.value}__header`,\n      props.headerBordered && `${COMPONENT_NAME.value}__title--bordered`,\n    ]);\n\n    const headerWrapperCls = usePrefixClass('card__header-wrapper');\n    const headerAvatarCls = usePrefixClass('card__avatar');\n    const headerTitleCls = usePrefixClass('card__title');\n    const headerSubTitleCls = usePrefixClass('card__subtitle');\n    const headerDescriptionCls = usePrefixClass('card__description');\n    const actionsCls = usePrefixClass('card__actions');\n\n    const bodyCls = usePrefixClass('card__body');\n    const coverCls = usePrefixClass('card__cover');\n    const footerCls = usePrefixClass('card__footer');\n    const footerWrapperCls = usePrefixClass('card__footer-wrapper');\n\n    // 卡片风格：普通风格、海报风格1（操作区域在顶部）、海报风格2（操作区域在底部）。\n    // 可选项：normal/poster1/poster2\n    const isPoster2 = computed(() => props.theme === 'poster2');\n\n    const showTitle = computed(() => props.title || slots.title);\n    const showHeader = computed(() => props.header || slots.header);\n    const showSubtitle = computed(() => props.subtitle || slots.subtitle);\n    const showAvatar = computed(() => props.avatar || slots.avatar);\n    const showDescription = computed(() => props.description || slots.description);\n    const showStatus = computed(() => props.status || slots.status);\n    const showActions = computed(() => props.actions || slots.actions);\n    const showFooter = computed(() => props.footer || slots.footer);\n    const showCover = computed(() => props.cover || slots.cover);\n    const showLoading = computed(() => props.loading || slots.loading);\n    const showContent = computed(() => props.content || slots.content || props.default || slots.default);\n\n    // 是否展示头部区域\n    const isHeaderRender = computed(\n      () =>\n        showHeader.value ||\n        showTitle.value ||\n        showSubtitle.value ||\n        showDescription.value ||\n        showAvatar.value ||\n        (showStatus.value && isPoster2.value) ||\n        (showActions.value && !isPoster2.value),\n    );\n\n    // 是否展示底部区域\n    const isFooterRender = computed(() => showFooter.value || (showActions.value && isPoster2.value));\n\n    // 头部区域渲染逻辑\n    const renderHeader = () => {\n      if (showHeader.value)\n        return (\n          <div class={[headerCls.value, props.headerClassName]} style={props.headerStyle}>\n            {renderTNodeJSX('header')}\n          </div>\n        );\n      return (\n        <div class={[headerCls.value, props.headerClassName]} style={props.headerStyle}>\n          <div class={headerWrapperCls.value}>\n            {showAvatar.value && <div class={headerAvatarCls.value}>{renderTNodeJSX('avatar')}</div>}\n            <div>\n              {showTitle.value && <div class={headerTitleCls.value}>{renderTNodeJSX('title')}</div>}\n              {showSubtitle.value && <div class={headerSubTitleCls.value}>{renderTNodeJSX('subtitle')}</div>}\n              {showDescription.value && <p class={headerDescriptionCls.value}>{renderTNodeJSX('description')}</p>}\n            </div>\n          </div>\n          {showActions.value && !isPoster2.value && <div class={actionsCls.value}>{renderTNodeJSX('actions')}</div>}\n          {showStatus.value && <div class={actionsCls.value}>{renderTNodeJSX('status')}</div>}\n        </div>\n      );\n    };\n\n    // 封面区域渲染逻辑\n    const renderCover = () => {\n      const textCover = isString(props.cover);\n      return <div class={coverCls.value}>{textCover ? <img src={props.cover}></img> : renderTNodeJSX('cover')}</div>;\n    };\n\n    return () => {\n      const content = (\n        <div class={baseCls.value}>\n          {isHeaderRender.value ? renderHeader() : null}\n          {showCover.value ? renderCover() : null}\n          {showContent.value && (\n            <div class={[bodyCls.value, props.bodyClassName]} style={props.bodyStyle}>\n              {renderTNodeJSX('default') || renderTNodeJSX('content')}\n            </div>\n          )}\n          {isFooterRender.value && (\n            <div class={[footerCls.value, props.footerClassName]} style={props.footerStyle}>\n              <div class={footerWrapperCls.value}>{renderTNodeJSX('footer')}</div>\n              {showActions.value && isPoster2.value && <div class={actionsCls.value}>{renderTNodeJSX('actions')}</div>}\n            </div>\n          )}\n        </div>\n      );\n\n      if (showLoading.value) {\n        return (\n          renderTNodeJSX('loading') || (\n            <TLoading {...(props.loadingProps as TdCardProps['loadingProps'])}>{content}</TLoading>\n          )\n        );\n      }\n      return content;\n    };\n  },\n});\n"], "names": ["_isSlot", "s", "Object", "prototype", "toString", "call", "_isVNode", "defineComponent", "name", "props", "setup", "slots", "_ref", "renderTNodeJSX", "useTNodeJSX", "COMPONENT_NAME", "usePrefixClass", "_useCommonClassName", "useCommonClassName", "SIZE", "baseCls", "computed", "defaultClass", "value", "size", "push", "concat", "bordered", "shadow", "hoverShadow", "headerCls", "headerBordered", "headerWrapperCls", "headerAvatarCls", "headerTitleCls", "headerSubTitleCls", "headerDescriptionCls", "actionsCls", "bodyCls", "coverCls", "footerCls", "footerWrapperCls", "isPoster2", "theme", "showTitle", "title", "showHeader", "header", "showSubtitle", "subtitle", "showAvatar", "avatar", "showDescription", "description", "showStatus", "status", "showActions", "actions", "showFooter", "footer", "showCover", "cover", "showLoading", "loading", "showContent", "content", "is<PERSON><PERSON>er<PERSON>ender", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderHeader", "_createVNode", "headerClassName", "headerStyle", "renderCover", "textCover", "isString", "bodyClassName", "bodyStyle", "footerClassName", "footerStyle", "TLoading", "loadingProps"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMyB,SAAAA,QAAAC,CAAA,EAAA;AAAA,EAAA,OAAA,OAAAA,CAAA,KAAA,UAAA,IAAAC,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAC,IAAA,CAAAJ,CAAA,CAAAK,KAAAA,iBAAAA,IAAAA,CAAAA,WAAA,CAAAL,CAAA,CAAA,CAAA;AAAA,CAAA;AAGzB,YAAeM,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,OAAA;AACNC,EAAAA,KAAA,EAAAA,qBAAA;AACAC,EAAAA,KAAMD,WAANC,KAAMD,CAAAA,MAAAA,EAAAA,IAAAA,EAAkB;AAAA,IAAA,IAATE,KAAA,GAAAC,IAAA,CAAAD,KAAA,CAAA;AACb,IAAA,IAAME,iBAAiBC,iBAAY,EAAA,CAAA;AAC7B,IAAA,IAAAC,cAAA,GAAiBC,uBAAe,MAAM,CAAA,CAAA;AACtC,IAAA,IAAAC,mBAAA,GAAWC,0BAAmB,EAAA;MAA5BC,IAAK,GAAAF,mBAAA,CAALE,IAAK,CAAA;AAEP,IAAA,IAAAC,OAAA,GAAUC,aAAS,YAAM;AACvB,MAAA,IAAAC,YAAA,GAAe,CAACP,cAAA,CAAeQ,KAAK,CAAA,CAAA;MAE1C,IAAId,OAAMe,IAAS,KAAA,OAAA,EAASF,YAAA,CAAaG,IAAK,CAAA,EAAA,CAAAC,MAAA,CAAGP,IAAK,CAAAI,KAAA,CAAMd,OAAMe,IAAO,CAAA,CAAA,CAAA,CAAA;AACzE,MAAA,IAAIf,MAAM,CAAAkB,QAAA,EAAuBL,YAAA,CAAAG,IAAA,CAAAC,EAAAA,CAAAA,MAAA,CAAQX,cAAA,CAAeQ,KAAiB,eAAA,CAAA,CAAA;AACzE,MAAA,IAAId,MAAM,CAAAmB,MAAA,EAAqBN,YAAA,CAAAG,IAAA,CAAAC,EAAAA,CAAAA,MAAA,CAAQX,cAAA,CAAeQ,KAAe,aAAA,CAAA,CAAA;AACrE,MAAA,IAAId,MAAM,CAAAoB,WAAA,EAA0BP,YAAA,CAAAG,IAAA,CAAAC,EAAAA,CAAAA,MAAA,CAAQX,cAAA,CAAeQ,KAAqB,mBAAA,CAAA,CAAA;AAEzE,MAAA,OAAAD,YAAA,CAAA;AACT,KAAC,CAAA,CAAA;IAEK,IAAAQ,SAAA,GAAYT,aAAS,YAAA;AAAA,MAAA,OAAM,IAAAK,MAAA,CAC5BX,cAAe,CAAAQ,KAAA,EAClBd,UAAAA,CAAAA,EAAAA,MAAAA,CAAMsB,cAAkB,OAAAL,MAAA,CAAGX,cAAe,CAAAQ,KAAA,sBAAA,CAC3C,CAAA;KAAA,CAAA,CAAA;AAEK,IAAA,IAAAS,gBAAA,GAAmBhB,uBAAe,sBAAsB,CAAA,CAAA;AACxD,IAAA,IAAAiB,eAAA,GAAkBjB,uBAAe,cAAc,CAAA,CAAA;AAC/C,IAAA,IAAAkB,cAAA,GAAiBlB,uBAAe,aAAa,CAAA,CAAA;AAC7C,IAAA,IAAAmB,iBAAA,GAAoBnB,uBAAe,gBAAgB,CAAA,CAAA;AACnD,IAAA,IAAAoB,oBAAA,GAAuBpB,uBAAe,mBAAmB,CAAA,CAAA;AACzD,IAAA,IAAAqB,UAAA,GAAarB,uBAAe,eAAe,CAAA,CAAA;AAE3C,IAAA,IAAAsB,OAAA,GAAUtB,uBAAe,YAAY,CAAA,CAAA;AACrC,IAAA,IAAAuB,QAAA,GAAWvB,uBAAe,aAAa,CAAA,CAAA;AACvC,IAAA,IAAAwB,SAAA,GAAYxB,uBAAe,cAAc,CAAA,CAAA;AACzC,IAAA,IAAAyB,gBAAA,GAAmBzB,uBAAe,sBAAsB,CAAA,CAAA;IAI9D,IAAM0B,SAAY,GAAArB,YAAA,CAAS,YAAA;AAAA,MAAA,OAAMZ,MAAAA,CAAMkC,UAAU,SAAS,CAAA;KAAA,CAAA,CAAA;IAE1D,IAAMC,YAAYvB,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAAoC,KAAA,IAASlC,MAAMkC,KAAK,CAAA;KAAA,CAAA,CAAA;IAC3D,IAAMC,aAAazB,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAAsC,MAAA,IAAUpC,MAAMoC,MAAM,CAAA;KAAA,CAAA,CAAA;IAC9D,IAAMC,eAAe3B,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAAwC,QAAA,IAAYtC,MAAMsC,QAAQ,CAAA;KAAA,CAAA,CAAA;IACpE,IAAMC,aAAa7B,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAA0C,MAAA,IAAUxC,MAAMwC,MAAM,CAAA;KAAA,CAAA,CAAA;IAC9D,IAAMC,kBAAkB/B,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAA4C,WAAA,IAAe1C,MAAM0C,WAAW,CAAA;KAAA,CAAA,CAAA;IAC7E,IAAMC,aAAajC,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAA8C,MAAA,IAAU5C,MAAM4C,MAAM,CAAA;KAAA,CAAA,CAAA;IAC9D,IAAMC,cAAcnC,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAAgD,OAAA,IAAW9C,MAAM8C,OAAO,CAAA;KAAA,CAAA,CAAA;IACjE,IAAMC,aAAarC,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAAkD,MAAA,IAAUhD,MAAMgD,MAAM,CAAA;KAAA,CAAA,CAAA;IAC9D,IAAMC,YAAYvC,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAAoD,KAAA,IAASlD,MAAMkD,KAAK,CAAA;KAAA,CAAA,CAAA;IAC3D,IAAMC,cAAczC,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAAsD,OAAA,IAAWpD,MAAMoD,OAAO,CAAA;KAAA,CAAA,CAAA;IAC3D,IAAAC,WAAA,GAAc3C,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMZ,MAAM,CAAAwD,OAAA,IAAWtD,MAAMsD,OAAWxD,IAAAA,MAAAA,CAAAA,SAAAA,CAAiB,IAAAE,KAAA,CAAa,SAAA,CAAA,CAAA;KAAA,CAAA,CAAA;IAGnG,IAAMuD,cAAiB,GAAA7C,YAAA,CACrB,YAAA;AAAA,MAAA,OACEyB,UAAW,CAAAvB,KAAA,IACXqB,UAAUrB,KACV,IAAAyB,YAAA,CAAazB,SACb6B,eAAgB,CAAA7B,KAAA,IAChB2B,UAAW,CAAA3B,KAAA,IACV+B,WAAW/B,KAAS,IAAAmB,SAAA,CAAUnB,SAC9BiC,WAAY,CAAAjC,KAAA,IAAS,CAACmB,SAAU,CAAAnB,KAAA,CAAA;AAAA,KACrC,CAAA,CAAA;IAGM,IAAA4C,cAAA,GAAiB9C,aAAS,YAAA;MAAA,OAAMqC,UAAA,CAAWnC,SAAUiC,WAAY,CAAAjC,KAAA,IAASmB,UAAUnB,KAAM,CAAA;KAAA,CAAA,CAAA;AAGhG,IAAA,IAAM6C,eAAe,SAAfA,eAAqB;AACzB,MAAA,IAAItB,UAAW,CAAAvB,KAAA,EACb,OAAA8C,eAAA,CAAA,KAAA,EAAA;QAAA,OACc,EAAA,CAACvC,UAAUP,KAAOd,EAAAA,MAAAA,CAAM6D,eAAe,CAAA;AAAA,QAAA,OAAA,EAAU7D,MAAM,CAAA8D,WAAAA;OAChE1D,EAAAA,CAAAA,cAAe,CAAA,QAAQ;AAI5B,MAAA,OAAAwD,eAAA,CAAA,KAAA,EAAA;QAAA,OAAY,EAAA,CAACvC,SAAA,CAAUP,OAAOd,MAAM,CAAA6D,eAAe,CAAG;AAAA,QAAA,OAAA,EAAO7D,MAAM,CAAA8D,WAAAA;AAAA,OAAA,EAAA,CAAAF,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EACrDrC,gBAAiB,CAAAT,KAAAA;AAAA,OAAA,EAAA,CAC1B2B,UAAA,CAAW3B,KAAS,IAAA8C,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAYpC,gBAAgBV,KAAAA;AAAQ,OAAA,EAAA,CAAAV,cAAA,CAAe,QAAQ,CAAA,CAA1D,CAAA,EAAAwD,eAAA,CAAA,KAAA,EAAA,IAAA,EAAA,CAEnBzB,SAAA,CAAUrB,KAAS,IAAA8C,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAYnC,eAAeX,KAAAA;OAAQV,EAAAA,CAAAA,cAAA,CAAe,OAAO,CAAA,CAAA,CAAxD,EACpBmC,YAAA,CAAazB,KAAS,IAAA8C,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAYlC,kBAAkBZ,KAAAA;OAAQV,EAAAA,CAAAA,cAAA,CAAe,UAAU,CAAA,CAAA,CAA9D,EACvBuC,eAAA,CAAgB7B,KAAS,IAAA8C,eAAA,CAAA,GAAA,EAAA;AAAA,QAAA,OAAA,EAAUjC,qBAAqBb,KAAAA;AAAQ,OAAA,EAAA,CAAAV,cAAA,CAAe,aAAa,CAAA,EAAlE,CAG9B2C,CAAAA,CAAAA,CAAAA,EAAAA,WAAY,CAAAjC,KAAA,IAAS,CAACmB,SAAA,CAAUnB,KAAS,IAAA8C,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAYhC,UAAW,CAAAd,KAAAA;OAAQV,EAAAA,CAAAA,cAAe,CAAA,SAAS,GAAtD,EAC1CyC,UAAA,CAAW/B,KAAS,IAAA8C,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAYhC,WAAWd,KAAAA;AAAQ,OAAA,EAAA,CAAAV,cAAA,CAAe,QAAQ,CAAA,CAArD,CAAA,CAAA,CAAA,CAAA;KAG5B,CAAA;AAGA,IAAA,IAAM2D,cAAc,SAAdA,cAAoB;AAClB,MAAA,IAAAC,SAAA,GAAYC,iBAASjE,CAAAA,MAAAA,CAAMoD,KAAK,CAAA,CAAA;AACtC,MAAA,OAAAQ,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAmB9B,QAAA,CAAShB,KAAAA;OAAQkD,EAAAA,CAAAA,SAAY,GAAAJ,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,KAAA,EAAU5D,OAAMoD,KAAAA;OAAgBhD,EAAAA,IAAAA,CAAAA,GAAAA,cAAe,CAAA,OAAO;KACxG,CAAA;AAEA,IAAA,OAAO,YAAM;MACX,IAAMoD,OACJ,GAAAI,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAYjD,OAAQ,CAAAG,KAAAA;OACjB2C,EAAAA,CAAAA,cAAA,CAAe3C,KAAQ,GAAA6C,YAAA,EAAiB,GAAA,IAAA,EACxCR,SAAA,CAAUrC,KAAQ,GAAAiD,WAAA,EAAgB,GAAA,IAAA,EAClCR,WAAA,CAAYzC;eACC,EAAA,CAACe,OAAA,CAAQf,OAAOd,MAAM,CAAAkE,aAAa;iBAAUlE,MAAAA,CAAMmE,SAAAA;UAC5D/D,cAAe,CAAA,SAAS,KAAKA,cAAe,CAAA,SAAS,GADvD,EAIFsD,cAAe,CAAA5C,KAAA,IAAA8C,eAAA,CAAA,KAAA,EAAA;QAAA,OACF,EAAA,CAAC7B,SAAU,CAAAjB,KAAA,EAAOd,MAAM,CAAAoE,eAAe,CAAG;AAAA,QAAA,OAAA,EAAOpE,MAAM,CAAAqE,WAAAA;AAAA,OAAA,EAAA,CAAAT,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EACrD5B,gBAAA,CAAiBlB,KAAAA;UAAQV,cAAe,CAAA,QAAQ,EAC3D2C,CAAAA,EAAAA,WAAY,CAAAjC,KAAA,IAASmB,SAAU,CAAAnB,KAAA,IAAA8C,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAqBhC,UAAA,CAAWd,KAAAA;AAAQ,OAAA,EAAA,CAAAV,cAAA,CAAe,SAAS,CAAA,CAAA,CAAtD,EAF3C,CATJ,CAAA,CAAA;MAiBH,IAAIiD,YAAYvC,KAAO,EAAA;AAEnB,QAAA,OAAAV,cAAA,CAAe,SAAS,CACtB,IAAAwD,eAAA,CAAAU,qBAAA,EAAetE,MAAM,CAAAuE,YAAA,EAAAhF,OAAA,CAA+CiE,WAAAA;;oBAAAA;;SAAnE,CAAA,CAAA;AAGP,OAAA;AACO,MAAA,OAAAA,OAAA,CAAA;KACT,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}