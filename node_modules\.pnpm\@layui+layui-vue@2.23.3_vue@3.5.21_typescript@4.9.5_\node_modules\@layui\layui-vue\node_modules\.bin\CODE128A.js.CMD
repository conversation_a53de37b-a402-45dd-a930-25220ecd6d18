@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\JTNYProject\configuration-project-1\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\barcodes\CODE128\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\barcodes\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\jsbarcode@3.11.5\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\JTNYProject\configuration-project-1\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\barcodes\CODE128\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\barcodes\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\bin\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\jsbarcode@3.11.5\node_modules\jsbarcode\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\jsbarcode@3.11.5\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\jsbarcode@3.11.5\node_modules\jsbarcode\bin\barcodes\CODE128\CODE128A.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\jsbarcode@3.11.5\node_modules\jsbarcode\bin\barcodes\CODE128\CODE128A.js" %*
)
