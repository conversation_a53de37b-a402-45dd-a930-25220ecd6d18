import type { TreeNode, CascaderContextType, TdCascaderProps, TreeNodeValue } from '../types';
export declare function expendClickEffect(propsTrigger: TdCascaderProps['trigger'], trigger: TdCascaderProps['trigger'], node: TreeNode, cascaderContext: CascaderContextType): void;
export declare function valueChangeEffect(node: TreeNode, cascaderContext: CascaderContextType): void;
export declare function closeIconClickEffect(cascaderContext: CascaderContextType): void;
export declare function handleRemoveTagEffect(cascaderContext: CascaderContextType, index: number, onRemove: TdCascaderProps['onRemove']): void;
export declare const treeNodesEffect: (inputVal: CascaderContextType["inputVal"], treeStore: CascaderContextType["treeStore"], setTreeNodes: CascaderContextType["setTreeNodes"], filter: CascaderContextType["filter"]) => void;
export declare const treeStoreExpendEffect: (treeStore: CascaderContextType["treeStore"], value: CascaderContextType["value"], expend: TreeNodeValue[]) => void;
