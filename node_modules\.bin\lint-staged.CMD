@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\JTNYProject\configuration-project-1\node_modules\.pnpm\lint-staged@13.3.0\node_modules\lint-staged\bin\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\lint-staged@13.3.0\node_modules\lint-staged\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\lint-staged@13.3.0\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\JTNYProject\configuration-project-1\node_modules\.pnpm\lint-staged@13.3.0\node_modules\lint-staged\bin\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\lint-staged@13.3.0\node_modules\lint-staged\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\lint-staged@13.3.0\node_modules;E:\JTNYProject\configuration-project-1\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\lint-staged\bin\lint-staged.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\lint-staged\bin\lint-staged.js" %*
)
