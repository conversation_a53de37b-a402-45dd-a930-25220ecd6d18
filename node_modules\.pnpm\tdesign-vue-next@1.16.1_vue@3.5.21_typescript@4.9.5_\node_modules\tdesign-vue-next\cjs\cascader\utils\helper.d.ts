import { TreeNode, CascaderContextType, TdCascaderProps, CascaderValue, TreeNodeValue } from '../types';
export declare function getSingleContent(cascaderContext: CascaderContextType): string;
export declare function getMultipleContent(cascaderContext: CascaderContextType): string[];
export declare function getPanels(treeNodes: CascaderContextType['treeNodes']): TreeNode[][];
export declare function getFullPathLabel(node: TreeNode, separator?: string): string;
export declare const getTreeValue: (value: CascaderContextType["value"]) => TreeNodeValue[];
export declare const getCascaderValue: (value: CascaderValue, valueType: TdCascaderProps["valueType"], multiple: boolean) => CascaderValue;
export declare function isEmptyValues(value: unknown): boolean;
export declare function isValueInvalid(value: CascaderValue, cascaderContext: CascaderContextType): boolean;
