{"version": 3, "file": "helper.js", "sources": ["../../../../components/cascader/utils/helper.ts"], "sourcesContent": ["import { isArray, isEmpty, isNumber, isObject } from 'lodash-es';\n\nimport { TreeNode, CascaderContextType, TdCascaderProps, CascaderValue, TreeNodeValue, TreeOptionData } from '../types';\n\n/**\n * 单选状态下内容\n * @param isHover\n * @param cascaderContext\n * @returns\n */\nexport function getSingleContent(cascaderContext: CascaderContextType): string {\n  const { value, multiple, treeStore, showAllLevels } = cascaderContext;\n  if (multiple || (value !== 0 && !value)) return '';\n\n  if (isArray(value)) return '';\n  const node = treeStore && treeStore.getNodes(value as TreeNodeValue | TreeNode);\n  if (!(node && node.length)) {\n    return value as string;\n  }\n  const path = node && node[0].getPath();\n  if (path && path.length) {\n    return showAllLevels ? path.map((node: TreeNode) => node.label).join(' / ') : path.at(-1).label;\n  }\n  return value as string;\n}\n\n/**\n * 多选状态下选中内容\n * @param cascaderContext\n * @returns\n */\nexport function getMultipleContent(cascaderContext: CascaderContextType) {\n  const { value, multiple, treeStore, showAllLevels } = cascaderContext;\n\n  if (!multiple) return [];\n  if (multiple && !isArray(value)) return [];\n\n  const node = treeStore && treeStore.getNodes(value as TreeNodeValue | TreeNode);\n  if (!node) return [];\n\n  return (value as TreeNodeValue[])\n    .map((item: TreeNodeValue) => {\n      const node = treeStore.getNodes(item);\n      return showAllLevels ? getFullPathLabel(node[0]) : node[0]?.label;\n    })\n    .filter((item) => !!item);\n}\n\n/**\n * 面板数据计算方法\n * @param treeNodes\n * @returns\n */\nexport function getPanels(treeNodes: CascaderContextType['treeNodes']) {\n  const panels: TreeNode[][] = [];\n  treeNodes.forEach((node: TreeNode) => {\n    if (panels[node.level]) {\n      panels[node.level].push(node);\n    } else {\n      panels[node.level] = [node];\n    }\n  });\n  return panels;\n}\n\n/**\n * 获取node的全部路径\n * @param node\n * @returns\n */\nexport function getFullPathLabel(node: TreeNode, separator = '/') {\n  return node\n    ?.getPath()\n    .map((node: TreeNode) => node.label)\n    .join(separator);\n}\n\n/**\n * treeValue计算方法\n * @param value\n * @returns\n */\nexport const getTreeValue = (value: CascaderContextType['value']) => {\n  let treeValue: TreeNodeValue[] = [];\n  if (isArray(value)) {\n    if (value.length > 0 && isObject(value[0])) {\n      treeValue = (value as TreeOptionData[]).map((val) => val.value);\n    } else if (value.length) {\n      treeValue = value as TreeNodeValue[];\n    }\n  } else if (!isEmptyValues(value)) {\n    if (isObject(value)) {\n      treeValue = [(value as TreeOptionData).value];\n    } else {\n      treeValue = [value];\n    }\n  }\n  return treeValue;\n};\n\n/**\n * 按数据类型计算通用数值\n * @param value\n * @param showAllLevels\n * @param multiple\n * @returns\n */\nexport const getCascaderValue = (value: CascaderValue, valueType: TdCascaderProps['valueType'], multiple: boolean) => {\n  if (valueType === 'single') {\n    return value;\n  }\n  const val = value as Array<CascaderValue>;\n  if (multiple) {\n    return val.map((item: TreeNodeValue[]) => item.at(-1));\n  }\n  return val.at(-1);\n};\n\n/**\n * 空值校验\n * 补充value为Number时的空值校验逻辑，排除NaN\n * @param value\n * @returns\n */\nexport function isEmptyValues(value: unknown): boolean {\n  if (isNumber(value) && !isNaN(value)) return false;\n  return isEmpty(value);\n}\n\n/**\n * 初始化数据校验\n * @param value\n * @param cascaderContext\n * @returns boolean\n */\nexport function isValueInvalid(value: CascaderValue, cascaderContext: CascaderContextType) {\n  const { multiple, showAllLevels, valueType } = cascaderContext;\n  return (multiple && !isArray(value)) || (!multiple && isArray(value) && valueType === 'single' && !showAllLevels);\n}\n"], "names": ["getSingleContent", "cascaderContext", "value", "multiple", "treeStore", "showAllLevels", "isArray", "node", "getNodes", "length", "path", "<PERSON><PERSON><PERSON>", "map", "label", "join", "at", "getMultipleContent", "item", "_node2$", "getFullPathLabel", "filter", "getPanels", "treeNodes", "panels", "for<PERSON>ach", "level", "push", "separator", "arguments", "undefined", "getTreeValue", "treeValue", "isObject", "val", "isEmptyValues", "getCascaderValue", "valueType", "isNumber", "isNaN", "isEmpty", "isValueInvalid"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAUO,SAASA,iBAAiBC,eAA8C,EAAA;AAC7E,EAAA,IAAQC,KAAA,GAA8CD,eAAA,CAA9CC,KAAA;IAAOC,QAAU,GAA6BF,eAAA,CAAvCE,QAAU;IAAAC,SAAA,GAA6BH,eAAA,CAA7BG,SAAA;IAAWC,gBAAkBJ,eAAA,CAAlBI;EAChC,IAAAF,QAAA,IAAaD,KAAU,KAAA,CAAA,IAAK,CAACA,KAAA,EAAe,OAAA,EAAA,CAAA;AAEhD,EAAA,IAAII,gBAAQJ,KAAK,CAAA,EAAU,OAAA,EAAA,CAAA;EAC3B,IAAMK,IAAO,GAAAH,SAAA,IAAaA,SAAU,CAAAI,QAAA,CAASN,KAAiC,CAAA,CAAA;AAC1E,EAAA,IAAA,EAAEK,IAAQ,IAAAA,IAAA,CAAKE,MAAS,CAAA,EAAA;AACnB,IAAA,OAAAP,KAAA,CAAA;AACT,GAAA;EACA,IAAMQ,IAAO,GAAAH,IAAA,IAAQA,IAAK,CAAA,CAAA,CAAA,CAAGI,OAAQ,EAAA,CAAA;AACjC,EAAA,IAAAD,IAAA,IAAQA,KAAKD,MAAQ,EAAA;AACvB,IAAA,OAAOJ,aAAgB,GAAAK,IAAA,CAAKE,GAAI,CAAA,UAACL;aAAmBA,KAAK,CAAAM,KAAK,CAAA;AAAA,KAAA,CAAE,CAAAC,IAAA,CAAK,KAAK,CAAA,GAAIJ,IAAK,CAAAK,EAAA,CAAG,EAAE,CAAE,CAAAF,KAAA,CAAA;AAC5F,GAAA;AACO,EAAA,OAAAX,KAAA,CAAA;AACT,CAAA;AAOO,SAASc,mBAAmBf,eAAsC,EAAA;AACvE,EAAA,IAAQC,KAAA,GAA8CD,eAAA,CAA9CC,KAAA;IAAOC,QAAU,GAA6BF,eAAA,CAAvCE,QAAU;IAAAC,SAAA,GAA6BH,eAAA,CAA7BG,SAAA;IAAWC,gBAAkBJ,eAAA,CAAlBI;AAEpC,EAAA,IAAI,CAACF,QAAA,EAAU,OAAO,EAAC,CAAA;EACnB,IAAAA,QAAA,IAAY,CAACG,eAAA,CAAQJ,KAAK,CAAA,EAAG,OAAO,EAAC,CAAA;EAEzC,IAAMK,IAAO,GAAAH,SAAA,IAAaA,SAAU,CAAAI,QAAA,CAASN,KAAiC,CAAA,CAAA;AAC9E,EAAA,IAAI,CAACK,IAAA,EAAM,OAAO,EAAC,CAAA;AAEX,EAAA,OAAAL,KAAA,CACLU,GAAI,CAAA,UAACK,IAAwB,EAAA;AAAA,IAAA,IAAAC,OAAA,CAAA;AACtBX,IAAAA,IAAAA,KAAAA,GAAOH,SAAU,CAAAI,QAAA,CAASS,IAAI,CAAA,CAAA;IACpC,OAAOZ,gBAAgBc,gBAAiBZ,CAAAA,KAAAA,CAAK,CAAE,CAAA,CAAA,GAAA,CAAAW,OAAA,GAAIX,MAAK,CAAI,CAAA,cAAAW,OAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAATX,OAAAA,CAASM,KAAA,CAAA;AAC9D,GAAC,CACA,CAAAO,MAAA,CAAO,UAACH,IAAS,EAAA;IAAA,OAAA,CAAC,CAACA,IAAI,CAAA;GAAA,CAAA,CAAA;AAC5B,CAAA;AAOO,SAASI,UAAUC,SAA6C,EAAA;EACrE,IAAMC,SAAuB,EAAC,CAAA;AACpBD,EAAAA,SAAA,CAAAE,OAAA,CAAQ,UAACjB,IAAmB,EAAA;AAChC,IAAA,IAAAgB,MAAA,CAAOhB,KAAKkB,KAAQ,CAAA,EAAA;MACfF,MAAA,CAAAhB,IAAA,CAAKkB,KAAO,CAAA,CAAAC,IAAA,CAAKnB,IAAI,CAAA,CAAA;AAC9B,KAAO,MAAA;MACEgB,MAAA,CAAAhB,IAAA,CAAKkB,KAAS,CAAA,GAAA,CAAClB,IAAI,CAAA,CAAA;AAC5B,KAAA;AACF,GAAC,CAAA,CAAA;AACM,EAAA,OAAAgB,MAAA,CAAA;AACT,CAAA;AAOgB,SAAAJ,gBAAAA,CAAiBZ,IAAgB,EAAiB;AAAA,EAAA,IAAjBoB,SAAA,GAAAC,SAAA,CAAAnB,MAAA,GAAA,CAAA,IAAAmB,SAAA,CAAA,CAAA,CAAA,KAAAC,SAAA,GAAAD,SAAA,CAAA,CAAA,CAAA,GAAY,GAAK,CAAA;AACzD,EAAA,OAAArB,IAAA,KAAA,IAAA,IAAAA,IAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAAA,IAAA,CACHI,OAAQ,EAAA,CACTC,GAAI,CAAA,UAACL;WAAmBA,KAAK,CAAAM,KAAK,CAAA;AAAA,GAAA,CAClC,CAAAC,IAAA,CAAKa,SAAS,CAAA,CAAA;AACnB,CAAA;IAOaG,YAAA,GAAe,SAAfA,YAAAA,CAAgB5B,KAAwC,EAAA;EACnE,IAAI6B,YAA6B,EAAC,CAAA;AAC9B,EAAA,IAAAzB,eAAA,CAAQJ,KAAK,CAAG,EAAA;AAClB,IAAA,IAAIA,MAAMO,MAAS,GAAA,CAAA,IAAKuB,iBAAS,CAAA9B,KAAA,CAAM,EAAE,CAAG,EAAA;AAC1C6B,MAAAA,SAAA,GAAa7B,KAA2B,CAAAU,GAAA,CAAI,UAACqB,GAAA,EAAA;QAAA,OAAQA,IAAI/B,KAAK,CAAA;OAAA,CAAA,CAAA;AAChE,KAAA,MAAA,IAAWA,MAAMO,MAAQ,EAAA;AACXsB,MAAAA,SAAA,GAAA7B,KAAA,CAAA;AACd,KAAA;AACF,GAAW,MAAA,IAAA,CAACgC,aAAc,CAAAhC,KAAK,CAAG,EAAA;AAC5B,IAAA,IAAA8B,iBAAA,CAAS9B,KAAK,CAAG,EAAA;AACP6B,MAAAA,SAAA,GAAA,CAAE7B,MAAyBA,KAAK,CAAA,CAAA;AAC9C,KAAO,MAAA;MACL6B,SAAA,GAAY,CAAC7B,KAAK,CAAA,CAAA;AACpB,KAAA;AACF,GAAA;AACO,EAAA,OAAA6B,SAAA,CAAA;AACT,EAAA;AASO,IAAMI,gBAAmB,GAAA,SAAnBA,gBAAmBA,CAACjC,KAAsB,EAAAkC,SAAA,EAAyCjC,QAAsB,EAAA;EACpH,IAAIiC,cAAc,QAAU,EAAA;AACnB,IAAA,OAAAlC,KAAA,CAAA;AACT,GAAA;EACA,IAAM+B,GAAM,GAAA/B,KAAA,CAAA;AACZ,EAAA,IAAIC,QAAU,EAAA;AACZ,IAAA,OAAO8B,IAAIrB,GAAI,CAAA,UAACK;aAA0BA,IAAK,CAAAF,EAAA,CAAG,EAAE,CAAC,CAAA;KAAA,CAAA,CAAA;AACvD,GAAA;AACO,EAAA,OAAAkB,GAAA,CAAIlB,GAAG,CAAE,CAAA,CAAA,CAAA;AAClB,EAAA;AAQO,SAASmB,cAAchC,KAAyB,EAAA;AACrD,EAAA,IAAImC,iBAAS,CAAAnC,KAAK,CAAK,IAAA,CAACoC,MAAMpC,KAAK,CAAA,EAAU,OAAA,KAAA,CAAA;EAC7C,OAAOqC,gBAAQrC,KAAK,CAAA,CAAA;AACtB,CAAA;AAQgB,SAAAsC,cAAAA,CAAetC,OAAsBD,eAAsC,EAAA;AACzF,EAAA,IAAQE,QAAA,GAAuCF,eAAA,CAAvCE,QAAA;IAAUE,aAAe,GAAcJ,eAAA,CAA7BI,aAAe;IAAA+B,SAAA,GAAcnC,eAAA,CAAdmC,SAAA,CAAA;EACjC,OAAQjC,QAAY,IAAA,CAACG,eAAQ,CAAAJ,KAAK,CAAO,IAAA,CAACC,QAAY,IAAAG,eAAA,CAAQJ,KAAK,CAAA,IAAKkC,SAAc,KAAA,QAAA,IAAY,CAAC/B,aAAA,CAAA;AACrG;;;;;;;;;;;"}