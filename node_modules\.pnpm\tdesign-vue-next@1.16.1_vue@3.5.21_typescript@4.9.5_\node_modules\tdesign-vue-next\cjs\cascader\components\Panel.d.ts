import { PropType } from 'vue';
import { CascaderContextType } from '../types';
declare const _default: import("vue").DefineComponent<{
    option: {
        type: PropType<import("..").TdCascaderProps["option"]>;
    };
    empty: {
        type: PropType<import("..").TdCascaderProps["empty"]>;
    };
    trigger: {
        type: PropType<import("..").TdCascaderProps["trigger"]>;
        default: import("..").TdCascaderProps["trigger"];
        validator(val: import("..").TdCascaderProps["trigger"]): boolean;
    };
    onChange: PropType<(value: import("..").CascaderValue<import("../..").TreeOptionData>, context: import("..").CascaderChangeContext<import("../..").TreeOptionData>) => void>;
    loading: BooleanConstructor;
    loadingText: {
        type: PropType<import("..").TdCascaderProps["loadingText"]>;
    };
    cascaderContext: {
        type: PropType<CascaderContextType>;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    option: {
        type: PropType<import("..").TdCascaderProps["option"]>;
    };
    empty: {
        type: PropType<import("..").TdCascaderProps["empty"]>;
    };
    trigger: {
        type: PropType<import("..").TdCascaderProps["trigger"]>;
        default: import("..").TdCascaderProps["trigger"];
        validator(val: import("..").TdCascaderProps["trigger"]): boolean;
    };
    onChange: PropType<(value: import("..").CascaderValue<import("../..").TreeOptionData>, context: import("..").CascaderChangeContext<import("../..").TreeOptionData>) => void>;
    loading: BooleanConstructor;
    loadingText: {
        type: PropType<import("..").TdCascaderProps["loadingText"]>;
    };
    cascaderContext: {
        type: PropType<CascaderContextType>;
    };
}>>, {
    loading: boolean;
    trigger: "click" | "hover";
}, {}>;
export default _default;
