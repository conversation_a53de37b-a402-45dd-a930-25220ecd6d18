{"version": 3, "file": "index.js", "sources": ["../../../components/cascader/index.ts"], "sourcesContent": ["import _Cascader from './cascader';\nimport _CascaderPanel from './cascader-panel';\nimport { withInstall } from '@tdesign/shared-utils';\nimport { TdCascaderProps } from './type';\nimport { TreeOptionData } from '../common';\n\nimport './style';\n\nexport * from './type';\n\nexport type CascaderProps<T extends TreeOptionData = TreeOptionData> = TdCascaderProps<T>;\nexport type CascaderPanelProps<T extends TreeOptionData = TreeOptionData> = TdCascaderProps<T>;\n\nexport const Cascader = withInstall(_Cascader);\nexport const CascaderPanel = withInstall(_CascaderPanel);\n\nexport default Cascader;\n"], "names": ["<PERSON>r", "withInstall", "_Cascader", "CascaderPanel", "_CascaderPanel"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAaaA,QAAA,GAAWC,wBAAYC,4BAAS,EAAA;IAChCC,aAAA,GAAgBF,wBAAYG,iCAAc;;;;;;"}