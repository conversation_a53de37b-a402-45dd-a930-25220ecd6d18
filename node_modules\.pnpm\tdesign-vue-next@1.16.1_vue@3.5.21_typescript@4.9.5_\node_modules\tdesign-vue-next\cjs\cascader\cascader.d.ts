import { CascaderValue, TdCascaderProps } from './types';
declare const _default: import("vue").DefineComponent<{
    autofocus: BooleanConstructor;
    borderless: BooleanConstructor;
    checkProps: {
        type: import("vue").PropType<TdCascaderProps["checkProps"]>;
    };
    checkStrictly: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: import("vue").PropType<TdCascaderProps["collapsedItems"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    empty: {
        type: import("vue").PropType<TdCascaderProps["empty"]>;
    };
    filter: {
        type: import("vue").PropType<TdCascaderProps["filter"]>;
    };
    filterable: BooleanConstructor;
    inputProps: {
        type: import("vue").PropType<TdCascaderProps["inputProps"]>;
    };
    keys: {
        type: import("vue").PropType<TdCascaderProps["keys"]>;
    };
    label: {
        type: import("vue").PropType<TdCascaderProps["label"]>;
    };
    lazy: {
        type: BooleanConstructor;
        default: boolean;
    };
    load: {
        type: import("vue").PropType<TdCascaderProps["load"]>;
    };
    loading: BooleanConstructor;
    loadingText: {
        type: import("vue").PropType<TdCascaderProps["loadingText"]>;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    option: {
        type: import("vue").PropType<TdCascaderProps["option"]>;
    };
    options: {
        type: import("vue").PropType<TdCascaderProps["options"]>;
        default: () => TdCascaderProps["options"];
    };
    panelBottomContent: {
        type: import("vue").PropType<TdCascaderProps["panelBottomContent"]>;
    };
    panelTopContent: {
        type: import("vue").PropType<TdCascaderProps["panelTopContent"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: import("vue").PropType<TdCascaderProps["popupProps"]>;
    };
    popupVisible: BooleanConstructor;
    prefixIcon: {
        type: import("vue").PropType<TdCascaderProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    reserveKeyword: BooleanConstructor;
    selectInputProps: {
        type: import("vue").PropType<TdCascaderProps["selectInputProps"]>;
    };
    showAllLevels: {
        type: BooleanConstructor;
        default: boolean;
    };
    size: {
        type: import("vue").PropType<TdCascaderProps["size"]>;
        default: TdCascaderProps["size"];
        validator(val: TdCascaderProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<TdCascaderProps["status"]>;
        default: TdCascaderProps["status"];
        validator(val: TdCascaderProps["status"]): boolean;
    };
    suffix: {
        type: import("vue").PropType<TdCascaderProps["suffix"]>;
    };
    suffixIcon: {
        type: import("vue").PropType<TdCascaderProps["suffixIcon"]>;
    };
    tagInputProps: {
        type: import("vue").PropType<TdCascaderProps["tagInputProps"]>;
    };
    tagProps: {
        type: import("vue").PropType<TdCascaderProps["tagProps"]>;
    };
    tips: {
        type: import("vue").PropType<TdCascaderProps["tips"]>;
    };
    trigger: {
        type: import("vue").PropType<TdCascaderProps["trigger"]>;
        default: TdCascaderProps["trigger"];
        validator(val: TdCascaderProps["trigger"]): boolean;
    };
    value: {
        type: import("vue").PropType<TdCascaderProps["value"]>;
        default: TdCascaderProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdCascaderProps["value"]>;
        default: TdCascaderProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdCascaderProps["defaultValue"]>;
        default: () => TdCascaderProps["defaultValue"];
    };
    valueDisplay: {
        type: import("vue").PropType<TdCascaderProps["valueDisplay"]>;
    };
    valueMode: {
        type: import("vue").PropType<TdCascaderProps["valueMode"]>;
        default: TdCascaderProps["valueMode"];
        validator(val: TdCascaderProps["valueMode"]): boolean;
    };
    valueType: {
        type: import("vue").PropType<TdCascaderProps["valueType"]>;
        default: TdCascaderProps["valueType"];
        validator(val: TdCascaderProps["valueType"]): boolean;
    };
    onBlur: import("vue").PropType<TdCascaderProps["onBlur"]>;
    onChange: import("vue").PropType<TdCascaderProps["onChange"]>;
    onFocus: import("vue").PropType<TdCascaderProps["onFocus"]>;
    onPopupVisibleChange: import("vue").PropType<TdCascaderProps["onPopupVisibleChange"]>;
    onRemove: import("vue").PropType<TdCascaderProps["onRemove"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    autofocus: BooleanConstructor;
    borderless: BooleanConstructor;
    checkProps: {
        type: import("vue").PropType<TdCascaderProps["checkProps"]>;
    };
    checkStrictly: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: import("vue").PropType<TdCascaderProps["collapsedItems"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    empty: {
        type: import("vue").PropType<TdCascaderProps["empty"]>;
    };
    filter: {
        type: import("vue").PropType<TdCascaderProps["filter"]>;
    };
    filterable: BooleanConstructor;
    inputProps: {
        type: import("vue").PropType<TdCascaderProps["inputProps"]>;
    };
    keys: {
        type: import("vue").PropType<TdCascaderProps["keys"]>;
    };
    label: {
        type: import("vue").PropType<TdCascaderProps["label"]>;
    };
    lazy: {
        type: BooleanConstructor;
        default: boolean;
    };
    load: {
        type: import("vue").PropType<TdCascaderProps["load"]>;
    };
    loading: BooleanConstructor;
    loadingText: {
        type: import("vue").PropType<TdCascaderProps["loadingText"]>;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    option: {
        type: import("vue").PropType<TdCascaderProps["option"]>;
    };
    options: {
        type: import("vue").PropType<TdCascaderProps["options"]>;
        default: () => TdCascaderProps["options"];
    };
    panelBottomContent: {
        type: import("vue").PropType<TdCascaderProps["panelBottomContent"]>;
    };
    panelTopContent: {
        type: import("vue").PropType<TdCascaderProps["panelTopContent"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: import("vue").PropType<TdCascaderProps["popupProps"]>;
    };
    popupVisible: BooleanConstructor;
    prefixIcon: {
        type: import("vue").PropType<TdCascaderProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    reserveKeyword: BooleanConstructor;
    selectInputProps: {
        type: import("vue").PropType<TdCascaderProps["selectInputProps"]>;
    };
    showAllLevels: {
        type: BooleanConstructor;
        default: boolean;
    };
    size: {
        type: import("vue").PropType<TdCascaderProps["size"]>;
        default: TdCascaderProps["size"];
        validator(val: TdCascaderProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<TdCascaderProps["status"]>;
        default: TdCascaderProps["status"];
        validator(val: TdCascaderProps["status"]): boolean;
    };
    suffix: {
        type: import("vue").PropType<TdCascaderProps["suffix"]>;
    };
    suffixIcon: {
        type: import("vue").PropType<TdCascaderProps["suffixIcon"]>;
    };
    tagInputProps: {
        type: import("vue").PropType<TdCascaderProps["tagInputProps"]>;
    };
    tagProps: {
        type: import("vue").PropType<TdCascaderProps["tagProps"]>;
    };
    tips: {
        type: import("vue").PropType<TdCascaderProps["tips"]>;
    };
    trigger: {
        type: import("vue").PropType<TdCascaderProps["trigger"]>;
        default: TdCascaderProps["trigger"];
        validator(val: TdCascaderProps["trigger"]): boolean;
    };
    value: {
        type: import("vue").PropType<TdCascaderProps["value"]>;
        default: TdCascaderProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdCascaderProps["value"]>;
        default: TdCascaderProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdCascaderProps["defaultValue"]>;
        default: () => TdCascaderProps["defaultValue"];
    };
    valueDisplay: {
        type: import("vue").PropType<TdCascaderProps["valueDisplay"]>;
    };
    valueMode: {
        type: import("vue").PropType<TdCascaderProps["valueMode"]>;
        default: TdCascaderProps["valueMode"];
        validator(val: TdCascaderProps["valueMode"]): boolean;
    };
    valueType: {
        type: import("vue").PropType<TdCascaderProps["valueType"]>;
        default: TdCascaderProps["valueType"];
        validator(val: TdCascaderProps["valueType"]): boolean;
    };
    onBlur: import("vue").PropType<TdCascaderProps["onBlur"]>;
    onChange: import("vue").PropType<TdCascaderProps["onChange"]>;
    onFocus: import("vue").PropType<TdCascaderProps["onFocus"]>;
    onPopupVisibleChange: import("vue").PropType<TdCascaderProps["onPopupVisibleChange"]>;
    onRemove: import("vue").PropType<TdCascaderProps["onRemove"]>;
}>>, {
    disabled: boolean;
    options: import("..").TreeOptionData[];
    value: string | number | import("..").TreeOptionData | CascaderValue<import("..").TreeOptionData>[];
    loading: boolean;
    valueType: "single" | "full";
    multiple: boolean;
    max: number;
    size: import("..").SizeEnum;
    status: "error" | "default" | "success" | "warning";
    checkStrictly: boolean;
    lazy: boolean;
    valueMode: "all" | "parentFirst" | "onlyLeaf";
    defaultValue: CascaderValue<import("..").TreeOptionData>;
    placeholder: string;
    modelValue: string | number | import("..").TreeOptionData | CascaderValue<import("..").TreeOptionData>[];
    readonly: boolean;
    trigger: "click" | "hover";
    autofocus: boolean;
    borderless: boolean;
    clearable: boolean;
    minCollapsedNum: number;
    popupVisible: boolean;
    reserveKeyword: boolean;
    filterable: boolean;
    showAllLevels: boolean;
}, {}>;
export default _default;
