import { TdCheckboxProps } from './type';
import { PropType } from 'vue';
declare const _default: {
    checkAll: BooleanConstructor;
    checked: {
        type: BooleanConstructor;
        default: any;
    };
    modelValue: {
        type: BooleanConstructor;
        default: any;
    };
    defaultChecked: BooleanConstructor;
    default: {
        type: PropType<TdCheckboxProps["default"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    indeterminate: BooleanConstructor;
    label: {
        type: PropType<TdCheckboxProps["label"]>;
    };
    lazyLoad: BooleanConstructor;
    name: {
        type: StringConstructor;
        default: string;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    title: {
        type: StringConstructor;
        default: string;
    };
    value: {
        type: PropType<TdCheckboxProps["value"]>;
    };
    onChange: PropType<TdCheckboxProps["onChange"]>;
};
export default _default;
