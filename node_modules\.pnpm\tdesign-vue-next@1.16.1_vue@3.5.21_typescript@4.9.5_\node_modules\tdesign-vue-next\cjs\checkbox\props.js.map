{"version": 3, "file": "props.js", "sources": ["../../../components/checkbox/props.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * 该文件为脚本自动生成文件，请勿随意修改。如需修改请联系 PMC\n * */\n\nimport { TdCheckboxProps } from './type';\nimport { PropType } from 'vue';\n\nexport default {\n  /** 用于标识是否为「全选选项」。单独使用无效，需在 CheckboxGroup 中使用 */\n  checkAll: Boolean,\n  /** 是否选中 */\n  checked: {\n    type: Boolean,\n    default: undefined,\n  },\n  modelValue: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** 是否选中，非受控属性 */\n  defaultChecked: Boolean,\n  /** 多选框内容，同 label */\n  default: {\n    type: [String, Function] as PropType<TdCheckboxProps['default']>,\n  },\n  /** 是否禁用组件。如果父组件存在 CheckboxGroup，默认值由 CheckboxGroup.disabled 控制。优先级：Checkbox.disabled > CheckboxGroup.disabled > Form.disabled */\n  disabled: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** 是否为半选 */\n  indeterminate: Boolean,\n  /** 主文案 */\n  label: {\n    type: [String, Function] as PropType<TdCheckboxProps['label']>,\n  },\n  /** 是否启用懒加载。数据量大时建议开启；加载复杂内容或大量图片时建议开启 */\n  lazyLoad: Boolean,\n  /** HTML 元素原生属性 */\n  name: {\n    type: String,\n    default: '',\n  },\n  /** 只读状态 */\n  readonly: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** HTML 原生属性 */\n  title: {\n    type: String,\n    default: '',\n  },\n  /** 多选框的值 */\n  value: {\n    type: [String, Number, Boolean] as PropType<TdCheckboxProps['value']>,\n  },\n  /** 值变化时触发 */\n  onChange: Function as PropType<TdCheckboxProps['onChange']>,\n};\n"], "names": ["checkAll", "Boolean", "checked", "type", "modelValue", "defaultChecked", "String", "Function", "disabled", "indeterminate", "label", "lazyLoad", "name", "readonly", "title", "value", "Number", "onChange"], "mappings": ";;;;;;;;;;AASA,YAAe;AAEbA,EAAAA,QAAU,EAAAC,OAAA;AAEVC,EAAAA,OAAS,EAAA;AACPC,IAAAA,IAAM,EAAAF,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AACAG,EAAAA,UAAY,EAAA;AACVD,IAAAA,IAAM,EAAAF,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAI,EAAAA,cAAgB,EAAAJ,OAAA;EAEhB,SAAS,EAAA;AACPE,IAAAA,IAAA,EAAM,CAACG,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAC,EAAAA,QAAU,EAAA;AACRL,IAAAA,IAAM,EAAAF,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAQ,EAAAA,aAAe,EAAAR,OAAA;AAEfS,EAAAA,KAAO,EAAA;AACLP,IAAAA,IAAA,EAAM,CAACG,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAI,EAAAA,QAAU,EAAAV,OAAA;AAEVW,EAAAA,IAAM,EAAA;AACJT,IAAAA,IAAM,EAAAG,MAAA;IACN,SAAS,EAAA,EAAA;GACX;AAEAO,EAAAA,QAAU,EAAA;AACRV,IAAAA,IAAM,EAAAF,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAa,EAAAA,KAAO,EAAA;AACLX,IAAAA,IAAM,EAAAG,MAAA;IACN,SAAS,EAAA,EAAA;GACX;AAEAS,EAAAA,KAAO,EAAA;AACLZ,IAAAA,IAAM,EAAA,CAACG,MAAQ,EAAAU,MAAA,EAAQf,OAAO,CAAA;GAChC;AAEAgB,EAAAA,QAAU,EAAAV,QAAAA;AACZ,CAAA;;;;"}