{"version": 3, "file": "group.js", "sources": ["../../../components/checkbox/group.tsx"], "sourcesContent": ["import { defineComponent, provide, computed, watchEffect, ref, toRefs } from 'vue';\nimport { isObject, isUndefined, intersection } from 'lodash-es';\n\nimport Checkbox from './checkbox';\nimport props from './checkbox-group-props';\nimport { CheckboxOptionObj, TdCheckboxProps, CheckboxGroupValue } from './type';\nimport { CheckboxGroupInjectionKey } from './consts';\nimport { useVModel, useTNodeJSX, usePrefixClass, useChildComponentSlots } from '@tdesign/shared-hooks';\n\nexport default defineComponent({\n  name: 'TCheckboxGroup',\n  props,\n  setup(props) {\n    /** 样式 */\n    const COMPONENT_NAME = usePrefixClass('checkbox-group');\n    const renderTNodeJSX = useTNodeJSX();\n\n    const { isArray } = Array;\n    const { value, modelValue } = toRefs(props);\n    const [innerValue, setInnerValue] = useVModel(value, modelValue, props.defaultValue, props.onChange);\n\n    const optionList = ref<Array<CheckboxOptionObj>>([]);\n\n    const intersectionLen = computed<number>(() => {\n      if (!isArray(innerValue.value)) return 0;\n      const values = optionList.value.map((item) => item.value);\n      const n = intersection(innerValue.value, values);\n      return n.length;\n    });\n\n    /**\n     * 计算是否所有选项都被选中。\n     * 此函数不接受参数，但依赖于外部的 `optionList` 和 `innerValue` 变量。\n     *\n     * @returns {boolean} 如果所有符合条件的选项都被选中，则返回 `true`；否则返回 `false`。\n     */\n    const isCheckAll = computed<boolean>(() => {\n      // 筛选出非禁用、非只读且不设置为“全选”的选项，并提取其值\n      const optionItems = optionList.value\n        .filter((item) => !item.disabled && !item.readonly && !item.checkAll)\n        .map((t) => t.value);\n\n      // 计算当前选中值与筛选后的选项值的交集\n      const intersectionValues = intersection(optionItems, innerValue.value);\n\n      // 判断交集的长度是否等于所有选项值的长度，以确定是否所有选项都被选中\n      return intersectionValues.length === optionItems.length;\n    });\n\n    const indeterminate = computed<boolean>(\n      () => !isCheckAll.value && intersectionLen.value < optionList.value.length && intersectionLen.value !== 0,\n    );\n\n    const maxExceeded = computed<boolean>(() => !isUndefined(props.max) && innerValue.value.length === props.max);\n\n    watchEffect(() => {\n      if (!props.options) return [];\n      optionList.value = props.options.map((item) => {\n        return isObject(item) ? item : { label: String(item), value: item };\n      });\n    });\n\n    /**\n     * 获取所有复选框的值。\n     * 此函数遍历 `optionList` 中的项，忽略被标记为 `checkAll`、`disabled` 或 `readonly` 的项，\n     * 并收集非这些状态的项的值到一个 Set 集合中。如果达到最大限制 `maxExceeded`，则停止遍历。\n     *\n     */\n    const getAllCheckboxValue = () => {\n      const checkAllVal = new Set<TdCheckboxProps['value']>();\n      const uncheckAllVal = new Set<TdCheckboxProps['value']>();\n      // 遍历选项列表，忽略特定状态的项，并收集有效值\n      for (let i = 0, len = optionList.value.length; i < len; i++) {\n        const item = optionList.value[i];\n\n        // 如果项被标记为检查所有、禁用或只读，且未选中，则跳过当前循环迭代\n        if (item.checkAll) continue;\n        if (item.disabled) {\n          if (!innerValue.value.includes(item.value)) continue;\n          else uncheckAllVal.add(item.value); // 添加禁用状态项的值到集合中\n        }\n        if (item.readonly) {\n          if (!innerValue.value.includes(item.value)) continue;\n          else uncheckAllVal.add(item.value); // 添加禁用状态项的值到集合中\n        }\n\n        checkAllVal.add(item.value); // 添加非排除状态项的值到集合中\n\n        // 如果已达到最大限制，则终止循环\n        if (maxExceeded.value) break;\n      }\n\n      return { checkAllVal: [...checkAllVal], uncheckAllVal: [...uncheckAllVal] }; // 从 Set 集合转换为数组并返回\n    };\n\n    const onCheckAllChange = (checked: boolean, context: { e: Event; source?: 't-checkbox' }) => {\n      const { checkAllVal, uncheckAllVal } = getAllCheckboxValue();\n\n      const value: CheckboxGroupValue = checked ? checkAllVal : uncheckAllVal;\n      setInnerValue(value, {\n        e: context.e,\n        type: checked ? 'check' : 'uncheck',\n        current: undefined,\n        option: undefined,\n      });\n    };\n\n    const handleCheckboxChange = (data: { checked: boolean; e: Event; option: TdCheckboxProps }) => {\n      const currentValue = data.option.value;\n      if (!isArray(innerValue.value)) {\n        console.warn(`TDesign CheckboxGroup Warn: \\`value\\` must be an array, instead of ${typeof innerValue.value}`);\n        return;\n      }\n      const val = [...innerValue.value];\n      if (data.checked) {\n        val.push(currentValue);\n      } else {\n        const i = val.indexOf(currentValue);\n        val.splice(i, 1);\n      }\n      setInnerValue(val, {\n        e: data.e,\n        current: data.option.value,\n        option: data.option,\n        type: data.checked ? 'check' : 'uncheck',\n      });\n    };\n\n    const onCheckedChange = (p: { checked: boolean; checkAll: boolean; e: Event; option: TdCheckboxProps }) => {\n      const { checked, checkAll, e } = p;\n      if (checkAll) {\n        onCheckAllChange(checked, { e });\n      } else {\n        handleCheckboxChange(p);\n      }\n    };\n\n    const getChildComponentSlots = useChildComponentSlots();\n\n    const getOptionListBySlots = () => {\n      const nodes = getChildComponentSlots('Checkbox');\n      const arr: Array<CheckboxOptionObj> = [];\n      nodes?.forEach((node) => {\n        const option = node.props as CheckboxOptionObj;\n        if (!option) return;\n        // @ts-ignore types only declare checkAll not declare check-all\n        if (option['check-all'] === '' || option['check-all'] === true) {\n          option.checkAll = true;\n        }\n        arr.push(option);\n      });\n      return arr;\n    };\n\n    provide(\n      CheckboxGroupInjectionKey,\n      computed(() => ({\n        name: props.name,\n        isCheckAll: isCheckAll.value,\n        checkedValues: innerValue.value || [],\n        maxExceeded: maxExceeded.value,\n        disabled: props.disabled,\n        readonly: props.readonly,\n        indeterminate: indeterminate.value,\n        handleCheckboxChange,\n        onCheckedChange,\n      })),\n    );\n\n    return () => {\n      let children = null;\n      if (props.options?.length) {\n        children = optionList.value?.map((option, index) => (\n          <Checkbox\n            key={`${option.value || ''}${index}`}\n            lazyLoad={props.lazyLoad}\n            {...option}\n            index={index}\n            checked={innerValue.value?.includes(option.value)}\n            data={option}\n          ></Checkbox>\n        ));\n      } else {\n        const nodes = renderTNodeJSX('default');\n        optionList.value = getOptionListBySlots();\n        children = nodes;\n      }\n      return (\n        <div class={COMPONENT_NAME.value} role=\"group\" aria-label=\"checkbox-group\">\n          {children}\n        </div>\n      );\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "props", "setup", "COMPONENT_NAME", "usePrefixClass", "renderTNodeJSX", "useTNodeJSX", "isArray", "Array", "_toRefs", "toRefs", "value", "modelValue", "_useVModel", "useVModel", "defaultValue", "onChange", "_useVModel2", "_slicedToArray", "innerValue", "setInnerValue", "optionList", "ref", "intersectionLen", "computed", "values", "map", "item", "n", "intersection", "length", "isCheckAll", "optionItems", "filter", "disabled", "readonly", "checkAll", "t", "intersectionValues", "indeterminate", "maxExceeded", "isUndefined", "max", "watchEffect", "options", "isObject", "label", "String", "getAllCheckboxValue", "checkAllVal", "Set", "uncheckAllVal", "i", "len", "includes", "add", "_toConsumableArray", "onCheckAllChange", "checked", "context", "_getAllCheckboxValue", "e", "type", "current", "option", "handleCheckboxChange", "data", "currentValue", "console", "warn", "concat", "_typeof", "val", "push", "indexOf", "splice", "onCheckedChange", "p", "getChildComponentSlots", "useChildComponentSlots", "getOptionListBySlots", "nodes", "arr", "for<PERSON>ach", "node", "provide", "CheckboxGroupInjectionKey", "checkedValues", "_props2$options", "children", "_optionList$value", "index", "_innerValue$value", "_createVNode", "Checkbox", "_mergeProps", "lazyLoad"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,aAAeA,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,gBAAA;AACNC,EAAAA,KAAA,EAAAA,sCAAA;AACAC,EAAAA,OAAAA,SAAAA,MAAMD,MAAO,EAAA;AAEL,IAAA,IAAAE,cAAA,GAAiBC,qBAAe,gBAAgB,CAAA,CAAA;AACtD,IAAA,IAAMC,iBAAiBC,mBAAY,EAAA,CAAA;AAE7B,IAAA,IAAEC,UAAYC,KAAA,CAAZD;AACR,IAAA,IAAAE,OAAA,GAA8BC,WAAOT,MAAK,CAAA;MAAlCU,KAAA,GAAAF,OAAA,CAAAE,KAAA;MAAOC,UAAW,GAAAH,OAAA,CAAXG,UAAW,CAAA;AACpB,IAAA,IAAAC,UAAA,GAA8BC,iBAAA,CAAUH,OAAOC,UAAYX,EAAAA,MAAAA,CAAMc,YAAcd,EAAAA,MAAAA,CAAMe,QAAQ,CAAA;MAAAC,WAAA,GAAAC,kCAAA,CAAAL,UAAA,EAAA,CAAA,CAAA;AAA5FM,MAAAA,UAAY,GAAAF,WAAA,CAAA,CAAA,CAAA;AAAAG,MAAAA,aAAa,GAAAH,WAAA,CAAA,CAAA,CAAA,CAAA;AAE1B,IAAA,IAAAI,UAAA,GAAaC,OAA8B,CAAA,EAAE,CAAA,CAAA;AAE7C,IAAA,IAAAC,eAAA,GAAkBC,aAAiB,YAAM;MACzC,IAAA,CAACjB,OAAQ,CAAAY,UAAA,CAAWR,KAAK,CAAA,EAAU,OAAA,CAAA,CAAA;MACvC,IAAMc,SAASJ,UAAW,CAAAV,KAAA,CAAMe,IAAI,UAACC,IAAA,EAAA;QAAA,OAASA,KAAKhB,KAAK,CAAA;OAAA,CAAA,CAAA;MACxD,IAAMiB,CAAI,GAAAC,yBAAA,CAAaV,UAAW,CAAAR,KAAA,EAAOc,MAAM,CAAA,CAAA;MAC/C,OAAOG,CAAE,CAAAE,MAAA,CAAA;AACX,KAAC,CAAA,CAAA;AAQK,IAAA,IAAAC,UAAA,GAAaP,aAAkB,YAAM;MAEnC,IAAAQ,WAAA,GAAcX,WAAWV,KAC5B,CAAAsB,MAAA,CAAO,UAACN,IAAS,EAAA;AAAA,QAAA,OAAA,CAACA,KAAKO,QAAY,IAAA,CAACP,KAAKQ,QAAY,IAAA,CAACR,KAAKS,QAAQ,CAAA;AAAA,OAAA,CAAA,CACnEV,IAAI,UAACW,CAAA,EAAA;QAAA,OAAMA,EAAE1B,KAAK,CAAA;OAAA,CAAA,CAAA;MAGrB,IAAM2B,kBAAqB,GAAAT,yBAAA,CAAaG,WAAa,EAAAb,UAAA,CAAWR,KAAK,CAAA,CAAA;AAG9D,MAAA,OAAA2B,kBAAA,CAAmBR,WAAWE,WAAY,CAAAF,MAAA,CAAA;AACnD,KAAC,CAAA,CAAA;IAED,IAAMS,aAAgB,GAAAf,YAAA,CACpB,YAAA;AAAA,MAAA,OAAM,CAACO,UAAA,CAAWpB,KAAS,IAAAY,eAAA,CAAgBZ,QAAQU,UAAW,CAAAV,KAAA,CAAMmB,MAAU,IAAAP,eAAA,CAAgBZ,KAAU,KAAA,CAAA,CAAA;AAAA,KAC1G,CAAA,CAAA;IAEA,IAAM6B,WAAc,GAAAhB,YAAA,CAAkB,YAAA;AAAA,MAAA,OAAM,CAACiB,uBAAYxC,CAAAA,MAAAA,CAAMyC,GAAG,CAAA,IAAKvB,UAAW,CAAAR,KAAA,CAAMmB,MAAW7B,KAAAA,MAAAA,CAAMyC,GAAG,CAAA;KAAA,CAAA,CAAA;AAE5GC,IAAAA,eAAA,CAAY,YAAM;AAChB,MAAA,IAAI,CAAC1C,MAAM,CAAA2C,OAAA,EAAS,OAAO,EAAC,CAAA;MAC5BvB,UAAA,CAAWV,KAAQV,GAAAA,MAAAA,CAAM2C,OAAQ,CAAAlB,GAAA,CAAI,UAACC,IAAS,EAAA;AACtC,QAAA,OAAAkB,iBAAA,CAASlB,IAAI,CAAA,GAAIA,IAAO,GAAA;AAAEmB,UAAAA,OAAOC,MAAO,CAAApB,IAAI,CAAG;AAAAhB,UAAAA,KAAA,EAAOgB,IAAAA;SAAK,CAAA;AACpE,OAAC,CAAA,CAAA;AACH,KAAC,CAAA,CAAA;AAQD,IAAA,IAAMqB,sBAAsB,SAAtBA,sBAA4B;AAC1B,MAAA,IAAAC,WAAA,sBAAkBC,GAA8B,EAAA,CAAA;AAChD,MAAA,IAAAC,aAAA,sBAAoBD,GAA8B,EAAA,CAAA;AAE/C,MAAA,KAAA,IAAAE,CAAA,GAAI,GAAGC,GAAM,GAAAhC,UAAA,CAAWV,MAAMmB,MAAQ,EAAAsB,CAAA,GAAIC,KAAKD,CAAK,EAAA,EAAA;AACrD,QAAA,IAAAzB,IAAA,GAAON,WAAWV,KAAM,CAAAyC,CAAA,CAAA,CAAA;QAG9B,IAAIzB,IAAK,CAAAS,QAAA,EAAU,SAAA;QACnB,IAAIT,KAAKO,QAAU,EAAA;UACjB,IAAI,CAACf,UAAA,CAAWR,KAAM,CAAA2C,QAAA,CAAS3B,KAAKhB,KAAK,CAAA,EAAG,SAAA,KACzBwC,aAAA,CAAAI,GAAA,CAAI5B,KAAKhB,KAAK,CAAA,CAAA;AACnC,SAAA;QACA,IAAIgB,KAAKQ,QAAU,EAAA;UACjB,IAAI,CAAChB,UAAA,CAAWR,KAAM,CAAA2C,QAAA,CAAS3B,KAAKhB,KAAK,CAAA,EAAG,SAAA,KACzBwC,aAAA,CAAAI,GAAA,CAAI5B,KAAKhB,KAAK,CAAA,CAAA;AACnC,SAAA;AAEYsC,QAAAA,WAAA,CAAAM,GAAA,CAAI5B,KAAKhB,KAAK,CAAA,CAAA;QAG1B,IAAI6B,WAAY,CAAA7B,KAAA,EAAO,MAAA;AACzB,OAAA;MAEO,OAAA;AAAEsC,QAAAA,WAAa,EAAAO,sCAAA,CAAIP,WAAW;QAAGE,aAAe,EAAAK,sCAAA,CAAIL,aAAa,CAAA;OAAE,CAAA;KAC5E,CAAA;IAEM,IAAAM,gBAAA,GAAmB,SAAnBA,gBAAAA,CAAoBC,OAAA,EAAkBC,OAAiD,EAAA;AAC3F,MAAA,IAAAC,oBAAA,GAAuCZ,mBAAoB,EAAA;QAAnDC,WAAA,GAAAW,oBAAA,CAAAX,WAAA;QAAaE,aAAc,GAAAS,oBAAA,CAAdT,aAAc,CAAA;AAE7BxC,MAAAA,IAAAA,MAAAA,GAA4B+C,UAAUT,WAAc,GAAAE,aAAA,CAAA;MAC1D/B,aAAA,CAAcT,MAAO,EAAA;QACnBkD,GAAGF,OAAQ,CAAAE,CAAA;AACXC,QAAAA,IAAA,EAAMJ,UAAU,OAAU,GAAA,SAAA;QAC1BK,OAAS,EAAA,KAAA,CAAA;AACTC,QAAAA,MAAQ,EAAA,KAAA,CAAA;AACV,OAAC,CAAA,CAAA;KACH,CAAA;AAEM,IAAA,IAAAC,oBAAA,GAAuB,SAAvBA,oBAAAA,CAAwBC,IAAkE,EAAA;AACxF,MAAA,IAAAC,YAAA,GAAeD,KAAKF,MAAO,CAAArD,KAAA,CAAA;AACjC,MAAA,IAAI,CAACJ,OAAA,CAAQY,UAAW,CAAAR,KAAK,CAAG,EAAA;QAC9ByD,OAAA,CAAQC,IAAK,CAAA,mEAAA,CAAAC,MAAA,CAAAC,2BAAA,CAA6EpD,UAAA,CAAWR,KAAO,CAAA,CAAA,CAAA,CAAA;AAC5G,QAAA,OAAA;AACF,OAAA;AACA,MAAA,IAAM6D,GAAM,GAAAhB,sCAAA,CAAIrC,UAAA,CAAWR,KAAK,CAAA,CAAA;MAChC,IAAIuD,KAAKR,OAAS,EAAA;AAChBc,QAAAA,GAAA,CAAIC,KAAKN,YAAY,CAAA,CAAA;AACvB,OAAO,MAAA;AACC,QAAA,IAAAf,CAAA,GAAIoB,GAAI,CAAAE,OAAA,CAAQP,YAAY,CAAA,CAAA;AAC9BK,QAAAA,GAAA,CAAAG,MAAA,CAAOvB,GAAG,CAAC,CAAA,CAAA;AACjB,OAAA;MACAhC,aAAA,CAAcoD,GAAK,EAAA;QACjBX,GAAGK,IAAK,CAAAL,CAAA;AACRE,QAAAA,OAAA,EAASG,KAAKF,MAAO,CAAArD,KAAA;QACrBqD,QAAQE,IAAK,CAAAF,MAAA;AACbF,QAAAA,IAAA,EAAMI,IAAK,CAAAR,OAAA,GAAU,OAAU,GAAA,SAAA;AACjC,OAAC,CAAA,CAAA;KACH,CAAA;AAEM,IAAA,IAAAkB,eAAA,GAAkB,SAAlBA,eAAAA,CAAmBC,CAAkF,EAAA;AACzG,MAAA,IAAQnB,OAAA,GAAyBmB,CAAA,CAAzBnB,OAAA;QAAStB,QAAU,GAAMyC,CAAA,CAAhBzC,QAAU;QAAAyB,CAAA,GAAMgB,CAAA,CAANhB,CAAA,CAAA;AAC3B,MAAA,IAAIzB,QAAU,EAAA;QACKqB,gBAAA,CAAAC,OAAA,EAAS;AAAEG,UAAAA,CAAA,EAAAA,CAAAA;AAAE,SAAC,CAAA,CAAA;AACjC,OAAO,MAAA;QACLI,oBAAA,CAAqBY,CAAC,CAAA,CAAA;AACxB,OAAA;KACF,CAAA;AAEA,IAAA,IAAMC,yBAAyBC,8BAAuB,EAAA,CAAA;AAEtD,IAAA,IAAMC,uBAAuB,SAAvBA,uBAA6B;AAC3B,MAAA,IAAAC,KAAA,GAAQH,uBAAuB,UAAU,CAAA,CAAA;MAC/C,IAAMI,MAAgC,EAAC,CAAA;MAChCD,KAAA,KAAA,IAAA,IAAAA,KAAA,KAAAA,KAAAA,CAAAA,IAAAA,KAAA,CAAAE,OAAA,CAAQ,UAACC,IAAS,EAAA;AACvB,QAAA,IAAMpB,SAASoB,IAAK,CAAAnF,KAAA,CAAA;QACpB,IAAI,CAAC+D,MAAA,EAAQ,OAAA;AAEb,QAAA,IAAIA,MAAO,CAAA,WAAA,CAAA,KAAiB,EAAM,IAAAA,MAAA,CAAO,iBAAiB,IAAM,EAAA;UAC9DA,MAAA,CAAO5B,QAAW,GAAA,IAAA,CAAA;AACpB,SAAA;AACA8C,QAAAA,GAAA,CAAIT,KAAKT,MAAM,CAAA,CAAA;AACjB,OAAC,CAAA,CAAA;AACM,MAAA,OAAAkB,GAAA,CAAA;KACT,CAAA;AAEAG,IAAAA,WAAA,CACEC,+CAAA,EACA9D,aAAS,YAAA;MAAA,OAAO;QACdxB,MAAMC,MAAM,CAAAD,IAAA;QACZ+B,YAAYA,UAAW,CAAApB,KAAA;AACvB4E,QAAAA,aAAA,EAAepE,UAAW,CAAAR,KAAA,IAAS,EAAC;QACpC6B,aAAaA,WAAY,CAAA7B,KAAA;QACzBuB,UAAUjC,MAAM,CAAAiC,QAAA;QAChBC,UAAUlC,MAAM,CAAAkC,QAAA;QAChBI,eAAeA,aAAc,CAAA5B,KAAA;AAC7BsD,QAAAA,oBAAA,EAAAA,oBAAA;AACAW,QAAAA,eAAA,EAAAA,eAAAA;OACA,CAAA;AAAA,KAAA,CACJ,CAAA,CAAA;AAEA,IAAA,OAAO,YAAM;AAAA,MAAA,IAAAY,eAAA,CAAA;MACX,IAAIC,QAAW,GAAA,IAAA,CAAA;MACXxF,IAAAA,CAAAA,eAAAA,GAAAA,MAAAA,CAAM2C,yCAAN3C,KAAAA,CAAAA,IAAAA,eAAAA,CAAe6B,MAAQ,EAAA;AAAA,QAAA,IAAA4D,iBAAA,CAAA;AACzBD,QAAAA,QAAA,IAAAC,iBAAA,GAAWrE,UAAW,CAAAV,KAAA,MAAA+E,IAAAA,IAAAA,iBAAA,KAAXA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,iBAAA,CAAkBhE,GAAI,CAAA,UAACsC,MAAQ,EAAA2B,KAAA,EAAA;AAAA,UAAA,IAAAC,iBAAA,CAAA;AAAA,UAAA,OAAAC,eAAA,CAAAC,4BAAA,EAAAC,cAAA,CAAA;YAAA,KAAAzB,EAAAA,EAAAA,CAAAA,MAAA,CAE9BN,MAAO,CAAArD,KAAA,IAAS,EAAK,CAAA,CAAA2D,MAAA,CAAAqB,KAAA,CAAA;AAAA,YAAA,UAAA,EACnB1F,MAAAA,CAAM+F,QAAAA;AACZ,WAAA,EAAAhC,MAAA,EAAA;AAAA,YAAA,OAAA,EACG2B,KAAA;AAAA,YAAA,SAAA,EAAA,CAAAC,iBAAA,GACEzE,UAAA,CAAWR,KAAO,MAAAiF,IAAAA,IAAAA,iBAAA,KAAlBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,iBAAA,CAAkBtC,QAAA,CAASU,MAAO,CAAArD,KAAK,CAChD;YAAA,MAAMqD,EAAAA,MAAAA;;SAET,CAAA,CAAA;AACH,OAAO,MAAA;AACC,QAAA,IAAAiB,KAAA,GAAQ5E,eAAe,SAAS,CAAA,CAAA;AACtCgB,QAAAA,UAAA,CAAWV,QAAQqE,oBAAqB,EAAA,CAAA;AAC7BS,QAAAA,QAAA,GAAAR,KAAA,CAAA;AACb,OAAA;AAEE,MAAA,OAAAY,eAAA,CAAA,KAAA,EAAA;QAAA,OAAY1F,EAAAA,cAAe,CAAAQ,KAAA;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,QAAA,YAAA,EAAA,gBAAA;AAAA,OAAA,EAAA,CACxB8E,QAAA,CAAA,CAAA,CAAA;KAGP,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}