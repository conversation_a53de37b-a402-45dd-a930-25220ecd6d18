{"version": 3, "file": "index.js", "sources": ["../../../components/calendar/index.ts"], "sourcesContent": ["import _Calendar from './calendar';\nimport { withInstall } from '@tdesign/shared-utils';\nimport { TdCalendarProps } from './type';\n\nimport './style';\n\nexport * from './type';\nexport type CalendarProps = TdCalendarProps;\n\nexport const Calendar = withInstall(_Calendar);\nexport default Calendar;\n"], "names": ["Calendar", "withInstall", "_Calendar"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASaA,QAAA,GAAWC,wBAAYC,4BAAS;;;;;"}