{"version": 3, "file": "props.js", "sources": ["../../../components/cascader/props.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * 该文件为脚本自动生成文件，请勿随意修改。如需修改请联系 PMC\n * */\n\nimport { TdCascaderProps } from './type';\nimport { PropType } from 'vue';\n\nexport default {\n  /** 自动聚焦 */\n  autofocus: Boolean,\n  /** 无边框模式 */\n  borderless: Boolean,\n  /** 参考 checkbox 组件 API */\n  checkProps: {\n    type: Object as PropType<TdCascaderProps['checkProps']>,\n  },\n  /** 父子节点选中状态不再关联，可各自选中或取消 */\n  checkStrictly: Boolean,\n  /** 是否支持清空选项 */\n  clearable: Boolean,\n  /** 多选情况下，用于设置折叠项内容，默认为 `+N`。如果需要悬浮就显示其他内容，可以使用 collapsedItems 自定义。`value` 表示当前存在的所有标签，`collapsedSelectedItems` 表示折叠的标签，`count` 表示折叠的数量，`onClose` 表示移除标签的事件回调 */\n  collapsedItems: {\n    type: Function as PropType<TdCascaderProps['collapsedItems']>,\n  },\n  /** 是否禁用组件 */\n  disabled: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** 无匹配选项时的内容，默认全局配置为 '暂无数据' */\n  empty: {\n    type: [String, Function] as PropType<TdCascaderProps['empty']>,\n  },\n  /** 自定义过滤方法，用于对现有数据进行搜索过滤，判断是否过滤某一项数据 */\n  filter: {\n    type: Function as PropType<TdCascaderProps['filter']>,\n  },\n  /** 是否可搜索 */\n  filterable: Boolean,\n  /** 透传 Input 输入框组件的全部属性 */\n  inputProps: {\n    type: Object as PropType<TdCascaderProps['inputProps']>,\n  },\n  /** 用来定义 value / label / children / disabled 在 `options` 中对应的字段别名 */\n  keys: {\n    type: Object as PropType<TdCascaderProps['keys']>,\n  },\n  /** 左侧文本 */\n  label: {\n    type: [String, Function] as PropType<TdCascaderProps['label']>,\n  },\n  /** 延迟加载 children 为 true 的子节点，即使 expandAll 被设置为 true，也同样延迟加载 */\n  lazy: {\n    type: Boolean,\n    default: true,\n  },\n  /** 加载子树数据的方法（仅当节点 children 为 true 时生效） */\n  load: {\n    type: Function as PropType<TdCascaderProps['load']>,\n  },\n  /** 是否为加载状态 */\n  loading: Boolean,\n  /** 远程加载时显示的文字，支持自定义。如加上超链接 */\n  loadingText: {\n    type: [String, Function] as PropType<TdCascaderProps['loadingText']>,\n  },\n  /** 用于控制多选数量，值为 0 则不限制 */\n  max: {\n    type: Number,\n    default: 0,\n  },\n  /** 最小折叠数量，用于多选情况下折叠选中项，超出该数值的选中项折叠。值为 0 则表示不折叠 */\n  minCollapsedNum: {\n    type: Number,\n    default: 0,\n  },\n  /** 是否允许多选 */\n  multiple: Boolean,\n  /** 自定义单个级联选项 */\n  option: {\n    type: Function as PropType<TdCascaderProps['option']>,\n  },\n  /** 可选项数据源 */\n  options: {\n    type: Array as PropType<TdCascaderProps['options']>,\n    default: (): TdCascaderProps['options'] => [],\n  },\n  /** 面板内的底部内容 */\n  panelBottomContent: {\n    type: [String, Function] as PropType<TdCascaderProps['panelBottomContent']>,\n  },\n  /** 面板内的顶部内容 */\n  panelTopContent: {\n    type: [String, Function] as PropType<TdCascaderProps['panelTopContent']>,\n  },\n  /** 占位符 */\n  placeholder: {\n    type: String,\n    default: undefined,\n  },\n  /** 参考 popup 组件 API */\n  popupProps: {\n    type: Object as PropType<TdCascaderProps['popupProps']>,\n  },\n  /** 是否显示下拉框 */\n  popupVisible: Boolean,\n  /** 组件前置图标 */\n  prefixIcon: {\n    type: Function as PropType<TdCascaderProps['prefixIcon']>,\n  },\n  /** 只读状态，值为真会隐藏输入框，且无法打开下拉框 */\n  readonly: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** 多选且可搜索时，是否在选中一个选项后保留当前的搜索关键词 */\n  reserveKeyword: Boolean,\n  /** 透传 SelectInput 筛选器输入框组件的全部属性 */\n  selectInputProps: {\n    type: Object as PropType<TdCascaderProps['selectInputProps']>,\n  },\n  /** 选中值使用完整路径，输入框在单选时也显示完整路径 */\n  showAllLevels: {\n    type: Boolean,\n    default: true,\n  },\n  /** 组件尺寸 */\n  size: {\n    type: String as PropType<TdCascaderProps['size']>,\n    default: 'medium' as TdCascaderProps['size'],\n    validator(val: TdCascaderProps['size']): boolean {\n      if (!val) return true;\n      return ['large', 'medium', 'small'].includes(val);\n    },\n  },\n  /** 输入框状态 */\n  status: {\n    type: String as PropType<TdCascaderProps['status']>,\n    default: 'default' as TdCascaderProps['status'],\n    validator(val: TdCascaderProps['status']): boolean {\n      if (!val) return true;\n      return ['default', 'success', 'warning', 'error'].includes(val);\n    },\n  },\n  /** 后置图标前的后置内容 */\n  suffix: {\n    type: [String, Function] as PropType<TdCascaderProps['suffix']>,\n  },\n  /** 组件后置图标 */\n  suffixIcon: {\n    type: Function as PropType<TdCascaderProps['suffixIcon']>,\n  },\n  /** 透传 TagInput 标签输入框组件的全部属性 */\n  tagInputProps: {\n    type: Object as PropType<TdCascaderProps['tagInputProps']>,\n  },\n  /** 透传 Tag 标签组件全部属性 */\n  tagProps: {\n    type: Object as PropType<TdCascaderProps['tagProps']>,\n  },\n  /** 输入框下方提示文本，会根据不同的 `status` 呈现不同的样式 */\n  tips: {\n    type: [String, Function] as PropType<TdCascaderProps['tips']>,\n  },\n  /** 展开下一层级的方式 */\n  trigger: {\n    type: String as PropType<TdCascaderProps['trigger']>,\n    default: 'click' as TdCascaderProps['trigger'],\n    validator(val: TdCascaderProps['trigger']): boolean {\n      if (!val) return true;\n      return ['click', 'hover'].includes(val);\n    },\n  },\n  /** 选中项的值 */\n  value: {\n    type: [String, Number, Array] as PropType<TdCascaderProps['value']>,\n    default: undefined as TdCascaderProps['value'],\n  },\n  modelValue: {\n    type: [String, Number, Array] as PropType<TdCascaderProps['value']>,\n    default: undefined as TdCascaderProps['value'],\n  },\n  /** 选中项的值，非受控属性 */\n  defaultValue: {\n    type: [String, Number, Array] as PropType<TdCascaderProps['defaultValue']>,\n    default: (): TdCascaderProps['defaultValue'] => [] as TdCascaderProps['defaultValue'],\n  },\n  /** 自定义选中项呈现的内容 */\n  valueDisplay: {\n    type: [String, Function] as PropType<TdCascaderProps['valueDisplay']>,\n  },\n  /** 选中值模式。all 表示父节点和子节点全部会出现在选中值里面；parentFirst 表示当子节点全部选中时，仅父节点在选中值里面；onlyLeaf 表示无论什么情况，选中值仅呈现叶子节点 */\n  valueMode: {\n    type: String as PropType<TdCascaderProps['valueMode']>,\n    default: 'onlyLeaf' as TdCascaderProps['valueMode'],\n    validator(val: TdCascaderProps['valueMode']): boolean {\n      if (!val) return true;\n      return ['onlyLeaf', 'parentFirst', 'all'].includes(val);\n    },\n  },\n  /** 用于控制选中值的类型。single 表示输入输出值为 叶子结点值， full 表示输入输出值为全路径 */\n  valueType: {\n    type: String as PropType<TdCascaderProps['valueType']>,\n    default: 'single' as TdCascaderProps['valueType'],\n    validator(val: TdCascaderProps['valueType']): boolean {\n      if (!val) return true;\n      return ['single', 'full'].includes(val);\n    },\n  },\n  /** 当输入框失去焦点时触发 */\n  onBlur: Function as PropType<TdCascaderProps['onBlur']>,\n  /** 选中值发生变化时触发。TreeNodeModel 从树组件中导出。`context.node` 表示触发事件的节点，`context.source` 表示触发事件的来源 */\n  onChange: Function as PropType<TdCascaderProps['onChange']>,\n  /** 获得焦点时触发 */\n  onFocus: Function as PropType<TdCascaderProps['onFocus']>,\n  /** 下拉框显示或隐藏时触发 */\n  onPopupVisibleChange: Function as PropType<TdCascaderProps['onPopupVisibleChange']>,\n  /** 多选模式下，选中数据被移除时触发 */\n  onRemove: Function as PropType<TdCascaderProps['onRemove']>,\n};\n"], "names": ["autofocus", "Boolean", "borderless", "checkProps", "type", "Object", "checkStrictly", "clearable", "collapsedItems", "Function", "disabled", "empty", "String", "filter", "filterable", "inputProps", "keys", "label", "lazy", "load", "loading", "loadingText", "max", "Number", "minCollapsedNum", "multiple", "option", "options", "Array", "default", "panel<PERSON>ottomContent", "panelTopContent", "placeholder", "popupProps", "popupVisible", "prefixIcon", "readonly", "reserveKeyword", "selectInputProps", "showAllLevels", "size", "validator", "val", "includes", "status", "suffix", "suffixIcon", "tagInputProps", "tagProps", "tips", "trigger", "value", "modelValue", "defaultValue", "valueDisplay", "valueMode", "valueType", "onBlur", "onChange", "onFocus", "onPopupVisibleChange", "onRemove"], "mappings": ";;;;;;;;;;AASA,YAAe;AAEbA,EAAAA,SAAW,EAAAC,OAAA;AAEXC,EAAAA,UAAY,EAAAD,OAAA;AAEZE,EAAAA,UAAY,EAAA;AACVC,IAAAA,IAAM,EAAAC,MAAAA;GACR;AAEAC,EAAAA,aAAe,EAAAL,OAAA;AAEfM,EAAAA,SAAW,EAAAN,OAAA;AAEXO,EAAAA,cAAgB,EAAA;AACdJ,IAAAA,IAAM,EAAAK,QAAAA;GACR;AAEAC,EAAAA,QAAU,EAAA;AACRN,IAAAA,IAAM,EAAAH,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAU,EAAAA,KAAO,EAAA;AACLP,IAAAA,IAAA,EAAM,CAACQ,MAAA,EAAQH,QAAQ,CAAA;GACzB;AAEAI,EAAAA,MAAQ,EAAA;AACNT,IAAAA,IAAM,EAAAK,QAAAA;GACR;AAEAK,EAAAA,UAAY,EAAAb,OAAA;AAEZc,EAAAA,UAAY,EAAA;AACVX,IAAAA,IAAM,EAAAC,MAAAA;GACR;AAEAW,EAAAA,IAAM,EAAA;AACJZ,IAAAA,IAAM,EAAAC,MAAAA;GACR;AAEAY,EAAAA,KAAO,EAAA;AACLb,IAAAA,IAAA,EAAM,CAACQ,MAAA,EAAQH,QAAQ,CAAA;GACzB;AAEAS,EAAAA,IAAM,EAAA;AACJd,IAAAA,IAAM,EAAAH,OAAA;IACN,SAAS,EAAA,IAAA;GACX;AAEAkB,EAAAA,IAAM,EAAA;AACJf,IAAAA,IAAM,EAAAK,QAAAA;GACR;AAEAW,EAAAA,OAAS,EAAAnB,OAAA;AAEToB,EAAAA,WAAa,EAAA;AACXjB,IAAAA,IAAA,EAAM,CAACQ,MAAA,EAAQH,QAAQ,CAAA;GACzB;AAEAa,EAAAA,GAAK,EAAA;AACHlB,IAAAA,IAAM,EAAAmB,MAAA;IACN,SAAS,EAAA,CAAA;GACX;AAEAC,EAAAA,eAAiB,EAAA;AACfpB,IAAAA,IAAM,EAAAmB,MAAA;IACN,SAAS,EAAA,CAAA;GACX;AAEAE,EAAAA,QAAU,EAAAxB,OAAA;AAEVyB,EAAAA,MAAQ,EAAA;AACNtB,IAAAA,IAAM,EAAAK,QAAAA;GACR;AAEAkB,EAAAA,OAAS,EAAA;AACPvB,IAAAA,IAAM,EAAAwB,KAAA;IACN,SAAS,EAAA,SAATC,QAAAA,GAAA;AAAA,MAAA,OAA2C,EAAC,CAAA;AAAA,KAAA;GAC9C;AAEAC,EAAAA,kBAAoB,EAAA;AAClB1B,IAAAA,IAAA,EAAM,CAACQ,MAAA,EAAQH,QAAQ,CAAA;GACzB;AAEAsB,EAAAA,eAAiB,EAAA;AACf3B,IAAAA,IAAA,EAAM,CAACQ,MAAA,EAAQH,QAAQ,CAAA;GACzB;AAEAuB,EAAAA,WAAa,EAAA;AACX5B,IAAAA,IAAM,EAAAQ,MAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAqB,EAAAA,UAAY,EAAA;AACV7B,IAAAA,IAAM,EAAAC,MAAAA;GACR;AAEA6B,EAAAA,YAAc,EAAAjC,OAAA;AAEdkC,EAAAA,UAAY,EAAA;AACV/B,IAAAA,IAAM,EAAAK,QAAAA;GACR;AAEA2B,EAAAA,QAAU,EAAA;AACRhC,IAAAA,IAAM,EAAAH,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAoC,EAAAA,cAAgB,EAAApC,OAAA;AAEhBqC,EAAAA,gBAAkB,EAAA;AAChBlC,IAAAA,IAAM,EAAAC,MAAAA;GACR;AAEAkC,EAAAA,aAAe,EAAA;AACbnC,IAAAA,IAAM,EAAAH,OAAA;IACN,SAAS,EAAA,IAAA;GACX;AAEAuC,EAAAA,IAAM,EAAA;AACJpC,IAAAA,IAAM,EAAAQ,MAAA;AACN,IAAA,SAAA,EAAS,QAAA;AACT6B,IAAAA,WAAAA,SAAAA,UAAUC,GAAuC,EAAA;AAC/C,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;MACjB,OAAO,CAAC,OAAS,EAAA,QAAA,EAAU,OAAO,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AAClD,KAAA;GACF;AAEAE,EAAAA,MAAQ,EAAA;AACNxC,IAAAA,IAAM,EAAAQ,MAAA;AACN,IAAA,SAAA,EAAS,SAAA;AACT6B,IAAAA,WAAAA,SAAAA,UAAUC,GAAyC,EAAA;AACjD,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;AACjB,MAAA,OAAO,CAAC,SAAW,EAAA,SAAA,EAAW,WAAW,OAAO,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AAChE,KAAA;GACF;AAEAG,EAAAA,MAAQ,EAAA;AACNzC,IAAAA,IAAA,EAAM,CAACQ,MAAA,EAAQH,QAAQ,CAAA;GACzB;AAEAqC,EAAAA,UAAY,EAAA;AACV1C,IAAAA,IAAM,EAAAK,QAAAA;GACR;AAEAsC,EAAAA,aAAe,EAAA;AACb3C,IAAAA,IAAM,EAAAC,MAAAA;GACR;AAEA2C,EAAAA,QAAU,EAAA;AACR5C,IAAAA,IAAM,EAAAC,MAAAA;GACR;AAEA4C,EAAAA,IAAM,EAAA;AACJ7C,IAAAA,IAAA,EAAM,CAACQ,MAAA,EAAQH,QAAQ,CAAA;GACzB;AAEAyC,EAAAA,OAAS,EAAA;AACP9C,IAAAA,IAAM,EAAAQ,MAAA;AACN,IAAA,SAAA,EAAS,OAAA;AACT6B,IAAAA,WAAAA,SAAAA,UAAUC,GAA0C,EAAA;AAClD,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;MACjB,OAAO,CAAC,OAAA,EAAS,OAAO,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AACxC,KAAA;GACF;AAEAS,EAAAA,KAAO,EAAA;AACL/C,IAAAA,IAAM,EAAA,CAACQ,MAAQ,EAAAW,MAAA,EAAQK,KAAK,CAAA;AAC5B,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AACAwB,EAAAA,UAAY,EAAA;AACVhD,IAAAA,IAAM,EAAA,CAACQ,MAAQ,EAAAW,MAAA,EAAQK,KAAK,CAAA;AAC5B,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAyB,EAAAA,YAAc,EAAA;AACZjD,IAAAA,IAAM,EAAA,CAACQ,MAAQ,EAAAW,MAAA,EAAQK,KAAK,CAAA;IAC5B,SAAS,EAAA,SAATC,QAAAA,GAAA;AAAA,MAAA,OAAgD,EAAC,CAAA;AAAA,KAAA;GACnD;AAEAyB,EAAAA,YAAc,EAAA;AACZlD,IAAAA,IAAA,EAAM,CAACQ,MAAA,EAAQH,QAAQ,CAAA;GACzB;AAEA8C,EAAAA,SAAW,EAAA;AACTnD,IAAAA,IAAM,EAAAQ,MAAA;AACN,IAAA,SAAA,EAAS,UAAA;AACT6B,IAAAA,WAAAA,SAAAA,UAAUC,GAA4C,EAAA;AACpD,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;MACjB,OAAO,CAAC,UAAY,EAAA,aAAA,EAAe,KAAK,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AACxD,KAAA;GACF;AAEAc,EAAAA,SAAW,EAAA;AACTpD,IAAAA,IAAM,EAAAQ,MAAA;AACN,IAAA,SAAA,EAAS,QAAA;AACT6B,IAAAA,WAAAA,SAAAA,UAAUC,GAA4C,EAAA;AACpD,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;MACjB,OAAO,CAAC,QAAA,EAAU,MAAM,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AACxC,KAAA;GACF;AAEAe,EAAAA,MAAQ,EAAAhD,QAAA;AAERiD,EAAAA,QAAU,EAAAjD,QAAA;AAEVkD,EAAAA,OAAS,EAAAlD,QAAA;AAETmD,EAAAA,oBAAsB,EAAAnD,QAAA;AAEtBoD,EAAAA,QAAU,EAAApD,QAAAA;AACZ,CAAA;;;;"}