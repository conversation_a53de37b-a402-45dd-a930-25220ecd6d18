{"version": 3, "file": "index.js", "sources": ["../../../../components/checkbox/consts/index.ts"], "sourcesContent": ["import { ComputedRef, InjectionKey } from 'vue';\nimport { TdCheckboxGroupProps, TdCheckboxProps } from '../type';\n\nexport interface CheckboxGroupInjectData {\n  name?: string;\n  isCheckAll: boolean;\n  maxExceeded: boolean;\n  disabled: boolean;\n  readonly: boolean;\n  indeterminate: boolean;\n  checkedValues: TdCheckboxGroupProps['value'];\n  handleCheckboxChange: (data: { checked: boolean; e: Event; option: TdCheckboxProps }) => void;\n  onCheckedChange: (p: { checked: boolean; checkAll: boolean; e: Event; option: TdCheckboxProps }) => void;\n}\n\nexport const CheckboxGroupInjectionKey: InjectionKey<ComputedRef<CheckboxGroupInjectData>> =\n  Symbol('CheckboxGroupProvide');\n"], "names": ["CheckboxGroupInjectionKey", "Symbol"], "mappings": ";;;;;;;;;;IAeaA,yBAAA,GACXC,OAAO,sBAAsB;;;;"}