#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vue-tsc@1.8.27_typescript@4.9.5/node_modules/vue-tsc/bin/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vue-tsc@1.8.27_typescript@4.9.5/node_modules/vue-tsc/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vue-tsc@1.8.27_typescript@4.9.5/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vue-tsc@1.8.27_typescript@4.9.5/node_modules/vue-tsc/bin/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vue-tsc@1.8.27_typescript@4.9.5/node_modules/vue-tsc/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vue-tsc@1.8.27_typescript@4.9.5/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/vue-tsc.js" "$@"
else
  exec node  "$basedir/../../bin/vue-tsc.js" "$@"
fi
