import { CascaderContextType, TreeNode } from '../types';
export declare function getFakeArrowIconClass(prefix: string, STATUS: Record<string, string>, cascaderContext: CascaderContextType): (string | {
    [x: string]: boolean;
})[];
export declare function getNodeStatusClass(node: TreeNode, STATUS: Record<string, string>, cascaderContext: CascaderContextType): {
    [x: string]: boolean;
}[];
export declare function getCascaderItemClass(prefix: string, node: TreeNode, SIZE: Record<string, string>, STATUS: Record<string, string>, cascaderContext: CascaderContextType): (string | {
    [x: string]: boolean;
})[];
export declare function getCascaderItemIconClass(prefix: string, node: TreeNode, STATUS: Record<string, string>, cascaderContext: CascaderContextType): (string | {
    [x: string]: boolean;
})[];
