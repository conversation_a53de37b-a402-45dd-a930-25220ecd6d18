/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var Vue = require('vue');
var _defineProperty = require('@babel/runtime/helpers/defineProperty');
var cascader_components_Panel = require('./components/Panel.js');
var selectInput_index = require('../select-input/index.js');
var commonComponents_fakeArrow = require('../common-components/fake-arrow.js');
var cascader_props = require('./props.js');
var cascader_utils_className = require('./utils/className.js');
var cascader_utils_effect = require('./utils/effect.js');
var cascader_utils_helper = require('./utils/helper.js');
require('@babel/runtime/helpers/toConsumableArray');
require('@babel/runtime/helpers/typeof');
require('../_chunks/dep-086ab407.js');
var index$4 = require('../_chunks/dep-059461d7.js');
var index$1 = require('../_chunks/dep-6666de7f.js');
var index = require('../_chunks/dep-e2298443.js');
var index$2 = require('../_chunks/dep-b09565a1.js');
require('@babel/runtime/helpers/slicedToArray');
require('../_chunks/dep-b15ee9eb.js');
var index$3 = require('../_chunks/dep-7e5fc00e.js');
var cascader_hooks_index = require('./hooks/index.js');
var omit = require('../_chunks/dep-74372b45.js');
var configProvider_hooks_useConfig = require('../config-provider/hooks/useConfig.js');
require('./components/Item.js');
require('tdesign-icons-vue-next');
require('../checkbox/index.js');
require('../checkbox/checkbox.js');
require('../checkbox/props.js');
require('../_chunks/dep-988704d0.js');
require('../_chunks/dep-076bd726.js');
require('../_chunks/dep-baa72039.js');
require('../_chunks/dep-7c14108f.js');
require('../_chunks/dep-3bb82c67.js');
require('../_chunks/dep-cabb6240.js');
require('../_chunks/dep-febae5f4.js');
require('../_chunks/dep-12e0aded.js');
require('../_chunks/dep-306b2e72.js');
require('../_chunks/dep-3f3d49e4.js');
require('../checkbox/consts/index.js');
require('../checkbox/hooks/useCheckboxLazyLoad.js');
require('../_chunks/dep-c9fd0e5d.js');
require('../checkbox/hooks/useKeyboardEvent.js');
require('../_chunks/dep-01642c17.js');
require('../_chunks/dep-914897c8.js');
require('../checkbox/group.js');
require('../checkbox/checkbox-group-props.js');
require('../_chunks/dep-872d0152.js');
require('../_chunks/dep-6ae67bab.js');
require('../_chunks/dep-ca39ce6d.js');
require('../_chunks/dep-cbee8f46.js');
require('../_chunks/dep-59fa52ec.js');
require('../_chunks/dep-8c0a3845.js');
require('../_chunks/dep-6b54b4a5.js');
require('../_chunks/dep-94424a57.js');
require('../_chunks/dep-eac47ca0.js');
require('../_chunks/dep-a271f384.js');
require('../_chunks/dep-df581fcb.js');
require('../_chunks/dep-4c75812c.js');
require('../_chunks/dep-fa8a400f.js');
require('../_chunks/dep-ef3df7aa.js');
require('../_chunks/dep-2105b21f.js');
require('../_chunks/dep-ac7dea19.js');
require('../_chunks/dep-2748f688.js');
require('../_chunks/dep-324af0df.js');
require('../_chunks/dep-76847e8b.js');
require('../_chunks/dep-f8224a9c.js');
require('../_chunks/dep-f9e26c60.js');
require('../_chunks/dep-714d992c.js');
require('../_chunks/dep-d5654a4e.js');
require('dayjs');
require('../_chunks/dep-94ff6543.js');
require('../_chunks/dep-6a71c082.js');
require('../_chunks/dep-6c0887f7.js');
require('../_chunks/dep-ca4c3e97.js');
require('../_chunks/dep-6183bb4a.js');
require('../_chunks/dep-53dbb954.js');
require('../_chunks/dep-d1c7139a.js');
require('../_chunks/dep-ba035735.js');
require('../_chunks/dep-756628ef.js');
require('@babel/runtime/helpers/createClass');
require('@babel/runtime/helpers/classCallCheck');
require('../loading/index.js');
require('../_chunks/dep-24c31f97.js');
require('../loading/plugin.js');
require('../_chunks/dep-faade8bd.js');
require('../loading/icon/gradient.js');
require('../_chunks/dep-fd4183e8.js');
require('@babel/runtime/helpers/objectWithoutProperties');
require('../_chunks/dep-f20572a3.js');
require('../_chunks/dep-8c00290f.js');
require('../loading/props.js');
require('../_chunks/dep-605cf2e8.js');
require('../_chunks/dep-925e8207.js');
require('../_chunks/dep-66442631.js');
require('../_chunks/dep-156361a6.js');
require('../_chunks/dep-6d27c874.js');
require('../_chunks/dep-31eb9b48.js');
require('../_chunks/dep-ce7457dd.js');
require('../_chunks/dep-4023d837.js');
require('../_chunks/dep-cbaee605.js');
require('../select-input/select-input.js');
require('../popup/index.js');
require('../popup/popup.js');
require('@popperjs/core');
require('../popup/container.js');
require('../popup/props.js');
require('../_chunks/dep-3c60e4a0.js');
require('../_chunks/dep-301296be.js');
require('../_chunks/dep-ea9e64d2.js');
require('../_chunks/dep-e3bd0b1f.js');
require('../_chunks/dep-0d72ab91.js');
require('../select-input/props.js');
require('../select-input/hooks/useMultiple.js');
require('../tag-input/index.js');
require('../tag-input/tag-input.js');
require('../input/index.js');
require('../input/input.js');
require('../input/props.js');
require('../input/hooks/useInput.js');
require('../form/consts/index.js');
require('../input/hooks/useLengthLimit.js');
require('../_chunks/dep-1e028e1c.js');
require('../input/hooks/useInputEventHandler.js');
require('../input/hooks/useInputWidth.js');
require('../input/input-group.js');
require('../input/input-group-props.js');
require('../tag-input/props.js');
require('../_chunks/dep-1189d7e7.js');
require('../tag-input/hooks/useDragSorter.js');
require('../tag-input/hooks/useHover.js');
require('../tag-input/hooks/useTagScroll.js');
require('../tag-input/hooks/useTagList.js');
require('../tag/index.js');
require('../tag/tag.js');
require('tinycolor2');
require('../tag/props.js');
require('../tag/check-tag.js');
require('../tag/check-tag-props.js');
require('../tag/check-tag-group.js');
require('../tag/check-tag-group-props.js');
require('../select-input/hooks/useOverlayInnerStyle.js');
require('../select-input/hooks/useSingle.js');
require('../_chunks/dep-f5374f9a.js');
require('../_chunks/dep-50dbb763.js');
require('../_chunks/dep-4fe7c57c.js');
require('../_chunks/dep-ba5948c9.js');
require('../_chunks/dep-ce2ce57a.js');
require('mitt');
require('../_chunks/dep-8491f97a.js');
require('@babel/runtime/helpers/asyncToGenerator');
require('@babel/runtime/regenerator');
require('../_chunks/dep-a4f1ef30.js');
require('../_chunks/dep-30cf92fa.js');
require('../_chunks/dep-b2fa5076.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _defineProperty__default = /*#__PURE__*/_interopDefaultLegacy(_defineProperty);

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty__default["default"](e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
var _Cascader = Vue.defineComponent({
  name: "TCascader",
  props: cascader_props["default"],
  setup: function setup(props2, _ref) {
    var slots = _ref.slots;
    var COMPONENT_NAME = index.usePrefixClass("cascader");
    var classPrefix = index.usePrefixClass();
    var _useCommonClassName = index$1.useCommonClassName(),
      STATUS = _useCommonClassName.STATUS;
    var overlayClassName = index.usePrefixClass("cascader__popup");
    var _useConfig = configProvider_hooks_useConfig.useConfig("cascader"),
      globalConfig = _useConfig.globalConfig;
    var isDisabled = index$2.useDisabled();
    var isReadonly = index$3.useReadonly();
    var renderTNodeJSX = index$4.useTNodeJSX();
    var _useCascaderContext = cascader_hooks_index.useCascaderContext(props2),
      cascaderContext = _useCascaderContext.cascaderContext,
      innerValue = _useCascaderContext.innerValue,
      isFilterable = _useCascaderContext.isFilterable,
      getCascaderItems = _useCascaderContext.getCascaderItems;
    var displayValue = Vue.computed(function () {
      return props2.multiple ? cascader_utils_helper.getMultipleContent(cascaderContext.value) : cascader_utils_helper.getSingleContent(cascaderContext.value);
    });
    var panels = Vue.computed(function () {
      return cascader_utils_helper.getPanels(cascaderContext.value.treeNodes);
    });
    var inputPlaceholder = Vue.computed(function () {
      var _props2$placeholder;
      return cascaderContext.value.visible && !props2.multiple && cascader_utils_helper.getSingleContent(cascaderContext.value) || ((_props2$placeholder = props2.placeholder) !== null && _props2$placeholder !== void 0 ? _props2$placeholder : globalConfig.value.placeholder);
    });
    var renderSuffixIcon = function renderSuffixIcon() {
      if (props2.suffixIcon || slots.suffixIcon) {
        return renderTNodeJSX("suffixIcon");
      }
      var _cascaderContext$valu = cascaderContext.value,
        visible = _cascaderContext$valu.visible,
        disabled = _cascaderContext$valu.disabled;
      return Vue.createVNode(commonComponents_fakeArrow["default"], {
        "overlayClassName": cascader_utils_className.getFakeArrowIconClass(classPrefix.value, STATUS.value, cascaderContext.value),
        "isActive": visible,
        "disabled": disabled
      }, null);
    };
    var valueDisplayParams = Vue.computed(function () {
      var arrayValue = innerValue.value instanceof Array ? innerValue.value : [innerValue.value];
      var displayValue2 = props2.multiple && props2.minCollapsedNum ? arrayValue.slice(0, props2.minCollapsedNum) : innerValue.value;
      var options = getCascaderItems(arrayValue);
      return {
        value: innerValue.value,
        selectedOptions: options,
        onClose: function onClose(index) {
          cascader_utils_effect.handleRemoveTagEffect(cascaderContext.value, index, props2.onRemove);
        },
        displayValue: displayValue2
      };
    });
    var renderValueDisplay = function renderValueDisplay() {
      return renderTNodeJSX("valueDisplay", {
        params: valueDisplayParams.value
      });
    };
    var renderLabel = function renderLabel() {
      var label = renderTNodeJSX("label");
      if (props2.multiple) return label;
      if (!label) return null;
      return Vue.createVNode("div", {
        "class": "".concat(classPrefix.value, "-tag-input__prefix")
      }, [label]);
    };
    var cascaderClassNames = Vue.computed(function () {
      return [COMPONENT_NAME.value, props2.multiple ? "".concat(COMPONENT_NAME.value, "--multiple") : "".concat(COMPONENT_NAME.value, "--single")];
    });
    return function () {
      var _props2$popupProps;
      var _cascaderContext$valu2 = cascaderContext.value,
        setVisible = _cascaderContext$valu2.setVisible,
        visible = _cascaderContext$valu2.visible,
        inputVal = _cascaderContext$valu2.inputVal,
        setInputVal = _cascaderContext$valu2.setInputVal;
      return Vue.createVNode(selectInput_index.SelectInput, Vue.mergeProps({
        "class": cascaderClassNames.value,
        "value": displayValue.value,
        "inputValue": visible ? inputVal : "",
        "popupVisible": visible,
        "keys": props2.keys,
        "allowInput": isFilterable.value,
        "min-collapsed-num": props2.minCollapsedNum,
        "collapsed-items": props2.collapsedItems,
        "readonly": isReadonly.value,
        "disabled": isDisabled.value,
        "clearable": props2.clearable,
        "placeholder": inputPlaceholder.value,
        "multiple": props2.multiple,
        "loading": props2.loading,
        "status": props2.status,
        "tips": props2.tips,
        "borderless": props2.borderless,
        "label": renderLabel,
        "valueDisplay": renderValueDisplay,
        "prefixIcon": props2.prefixIcon,
        "suffix": props2.suffix,
        "suffixIcon": function suffixIcon() {
          return renderSuffixIcon();
        },
        "popupProps": _objectSpread(_objectSpread({}, props2.popupProps), {}, {
          overlayInnerStyle: panels.value.length && !props2.loading ? {
            width: "auto"
          } : "",
          overlayClassName: [overlayClassName.value, (_props2$popupProps = props2.popupProps) === null || _props2$popupProps === void 0 ? void 0 : _props2$popupProps.overlayClassName]
        }),
        "inputProps": _objectSpread({
          size: props2.size
        }, props2.inputProps),
        "tagInputProps": _objectSpread({
          size: props2.size
        }, props2.tagInputProps),
        "tagProps": _objectSpread({}, props2.tagProps),
        "onInputChange": function onInputChange(value, ctx) {
          var _props2$selectInputPr, _props2$selectInputPr2;
          if (!isFilterable.value) return;
          setInputVal("".concat(value));
          props2 === null || props2 === void 0 || (_props2$selectInputPr = props2.selectInputProps) === null || _props2$selectInputPr === void 0 || (_props2$selectInputPr2 = _props2$selectInputPr.onInputChange) === null || _props2$selectInputPr2 === void 0 || _props2$selectInputPr2.call(_props2$selectInputPr, value, ctx);
        },
        "onTagChange": function onTagChange(val, ctx) {
          var _props2$selectInputPr3, _props2$selectInputPr4;
          if (ctx.trigger === "enter") return;
          cascader_utils_effect.handleRemoveTagEffect(cascaderContext.value, ctx.index, props2.onRemove);
          props2 === null || props2 === void 0 || (_props2$selectInputPr3 = props2.selectInputProps) === null || _props2$selectInputPr3 === void 0 || (_props2$selectInputPr4 = _props2$selectInputPr3.onTagChange) === null || _props2$selectInputPr4 === void 0 || _props2$selectInputPr4.call(_props2$selectInputPr3, val, ctx);
        },
        "onPopupVisibleChange": function onPopupVisibleChange(val, context) {
          var _props2$selectInputPr5, _props2$selectInputPr6;
          if (isDisabled.value) return;
          setVisible(val, context);
          props2 === null || props2 === void 0 || (_props2$selectInputPr5 = props2.selectInputProps) === null || _props2$selectInputPr5 === void 0 || (_props2$selectInputPr6 = _props2$selectInputPr5.onPopupVisibleChange) === null || _props2$selectInputPr6 === void 0 || _props2$selectInputPr6.call(_props2$selectInputPr5, val, context);
        },
        "onBlur": function onBlur(val, context) {
          var _props2$onBlur, _props2$selectInputPr7, _props2$selectInputPr8;
          (_props2$onBlur = props2.onBlur) === null || _props2$onBlur === void 0 || _props2$onBlur.call(props2, {
            value: cascaderContext.value.value,
            inputValue: context.inputValue || "",
            e: context.e
          });
          props2 === null || props2 === void 0 || (_props2$selectInputPr7 = props2.selectInputProps) === null || _props2$selectInputPr7 === void 0 || (_props2$selectInputPr8 = _props2$selectInputPr7.onBlur) === null || _props2$selectInputPr8 === void 0 || _props2$selectInputPr8.call(_props2$selectInputPr7, val, context);
        },
        "onFocus": function onFocus(val, context) {
          var _props2$onFocus, _props2$selectInputPr9, _props2$selectInputPr0;
          (_props2$onFocus = props2.onFocus) === null || _props2$onFocus === void 0 || _props2$onFocus.call(props2, {
            value: cascaderContext.value.value,
            e: context.e
          });
          props2 === null || props2 === void 0 || (_props2$selectInputPr9 = props2.selectInputProps) === null || _props2$selectInputPr9 === void 0 || (_props2$selectInputPr0 = _props2$selectInputPr9.onFocus) === null || _props2$selectInputPr0 === void 0 || _props2$selectInputPr0.call(_props2$selectInputPr9, val, context);
        },
        "onClear": function onClear(context) {
          var _props2$selectInputPr1, _props2$selectInputPr10;
          cascader_utils_effect.closeIconClickEffect(cascaderContext.value);
          props2 === null || props2 === void 0 || (_props2$selectInputPr1 = props2.selectInputProps) === null || _props2$selectInputPr1 === void 0 || (_props2$selectInputPr10 = _props2$selectInputPr1.onClear) === null || _props2$selectInputPr10 === void 0 || _props2$selectInputPr10.call(_props2$selectInputPr1, context);
        }
      }, omit.omit(props2.selectInputProps, ["onTagChange", "onInputChange", "onPopupVisibleChange", "onBlur", "onFocus", "onClear"])), {
        label: slots.label,
        suffix: slots.suffix,
        prefixIcon: slots.prefixIcon,
        panel: function panel() {
          return Vue.createVNode(Vue.Fragment, null, [renderTNodeJSX("panelTopContent"), Vue.createVNode(cascader_components_Panel["default"], {
            "option": props2.option,
            "empty": props2.empty,
            "visible": visible,
            "trigger": props2.trigger,
            "loading": props2.loading,
            "loadingText": props2.loadingText,
            "cascaderContext": cascaderContext.value
          }, {
            option: slots.option,
            empty: slots.empty,
            loadingText: slots.loadingText
          }), renderTNodeJSX("panelBottomContent")]);
        },
        collapsedItems: slots.collapsedItems
      });
    };
  }
});

exports["default"] = _Cascader;
//# sourceMappingURL=cascader.js.map
