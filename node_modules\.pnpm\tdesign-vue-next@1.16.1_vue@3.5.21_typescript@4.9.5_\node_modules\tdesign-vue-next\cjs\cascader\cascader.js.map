{"version": 3, "file": "cascader.js", "sources": ["../../../components/cascader/cascader.tsx"], "sourcesContent": ["import { defineComponent, computed } from 'vue';\nimport { omit } from 'lodash-es';\nimport Panel from './components/Panel';\nimport SelectInput from '../select-input';\nimport FakeArrow from '../common-components/fake-arrow';\nimport props from './props';\n\nimport { CascaderValue, TdSelectInputProps, TdCascaderProps } from './types';\nimport {\n  closeIconClickEffect,\n  handleRemoveTagEffect,\n  getFakeArrowIconClass,\n  getPanels,\n  getSingleContent,\n  getMultipleContent,\n} from './utils';\n\nimport {\n  useConfig,\n  useTNodeJSX,\n  useDisabled,\n  useReadonly,\n  usePrefixClass,\n  useCommonClassName,\n} from '@tdesign/shared-hooks';\nimport { useCascaderContext } from './hooks';\n\nexport default defineComponent({\n  name: 'TCascader',\n  props,\n  setup(props, { slots }) {\n    const COMPONENT_NAME = usePrefixClass('cascader');\n    const classPrefix = usePrefixClass();\n    const { STATUS } = useCommonClassName();\n    const overlayClassName = usePrefixClass('cascader__popup');\n    const { globalConfig } = useConfig('cascader');\n    const isDisabled = useDisabled();\n    const isReadonly = useReadonly();\n    const renderTNodeJSX = useTNodeJSX();\n\n    // 拿到全局状态的上下文\n    const { cascaderContext, innerValue, isFilterable, getCascaderItems } = useCascaderContext(props);\n\n    const displayValue = computed(() =>\n      props.multiple ? getMultipleContent(cascaderContext.value) : getSingleContent(cascaderContext.value),\n    );\n\n    const panels = computed(() => getPanels(cascaderContext.value.treeNodes));\n\n    const inputPlaceholder = computed(\n      () =>\n        (cascaderContext.value.visible && !props.multiple && getSingleContent(cascaderContext.value)) ||\n        (props.placeholder ?? globalConfig.value.placeholder),\n    );\n\n    const renderSuffixIcon = () => {\n      if (props.suffixIcon || slots.suffixIcon) {\n        return renderTNodeJSX('suffixIcon');\n      }\n\n      const { visible, disabled } = cascaderContext.value;\n      return (\n        <FakeArrow\n          overlayClassName={getFakeArrowIconClass(classPrefix.value, STATUS.value, cascaderContext.value)}\n          isActive={visible}\n          disabled={disabled}\n        />\n      );\n    };\n\n    const valueDisplayParams = computed(() => {\n      const arrayValue = innerValue.value instanceof Array ? innerValue.value : [innerValue.value];\n      const displayValue =\n        props.multiple && props.minCollapsedNum ? arrayValue.slice(0, props.minCollapsedNum) : innerValue.value;\n      const options = getCascaderItems(arrayValue);\n      return {\n        value: innerValue.value,\n        selectedOptions: options,\n        onClose: (index: number) => {\n          handleRemoveTagEffect(cascaderContext.value, index, props.onRemove);\n        },\n        displayValue,\n      };\n    });\n\n    const renderValueDisplay = () => {\n      return renderTNodeJSX('valueDisplay', {\n        params: valueDisplayParams.value,\n      });\n    };\n\n    const renderLabel = () => {\n      const label = renderTNodeJSX('label');\n      if (props.multiple) return label;\n      if (!label) return null;\n      return <div class={`${classPrefix.value}-tag-input__prefix`}>{label}</div>;\n    };\n\n    const cascaderClassNames = computed(() => [\n      COMPONENT_NAME.value,\n      props.multiple ? `${COMPONENT_NAME.value}--multiple` : `${COMPONENT_NAME.value}--single`,\n    ]);\n\n    return () => {\n      const { setVisible, visible, inputVal, setInputVal } = cascaderContext.value;\n\n      return (\n        <SelectInput\n          class={cascaderClassNames.value}\n          value={displayValue.value}\n          inputValue={visible ? inputVal : ''}\n          popupVisible={visible}\n          keys={props.keys}\n          allowInput={isFilterable.value}\n          min-collapsed-num={props.minCollapsedNum}\n          collapsed-items={props.collapsedItems}\n          readonly={isReadonly.value}\n          disabled={isDisabled.value}\n          clearable={props.clearable}\n          placeholder={inputPlaceholder.value}\n          multiple={props.multiple}\n          loading={props.loading}\n          status={props.status}\n          tips={props.tips}\n          borderless={props.borderless}\n          label={renderLabel}\n          valueDisplay={renderValueDisplay}\n          prefixIcon={props.prefixIcon}\n          suffix={props.suffix}\n          suffixIcon={() => renderSuffixIcon()}\n          popupProps={{\n            ...(props.popupProps as TdCascaderProps['popupProps']),\n            overlayInnerStyle: panels.value.length && !props.loading ? { width: 'auto' } : '',\n            overlayClassName: [\n              overlayClassName.value,\n              (props.popupProps as TdCascaderProps['popupProps'])?.overlayClassName,\n            ],\n          }}\n          inputProps={{ size: props.size, ...(props.inputProps as TdCascaderProps['inputProps']) }}\n          tagInputProps={{\n            size: props.size,\n            ...(props.tagInputProps as TdCascaderProps['tagInputProps']),\n          }}\n          tagProps={{ ...(props.tagProps as TdCascaderProps['tagProps']) }}\n          onInputChange={(value, ctx) => {\n            if (!isFilterable.value) return;\n            setInputVal(`${value}`);\n            (props?.selectInputProps as TdSelectInputProps)?.onInputChange?.(value, ctx);\n          }}\n          onTagChange={(val: CascaderValue, ctx) => {\n            // 按 enter 键不处理\n            if (ctx.trigger === 'enter') return;\n            handleRemoveTagEffect(cascaderContext.value, ctx.index, props.onRemove);\n            // @ts-ignore TODO: fix bug\n            (props?.selectInputProps as TdSelectInputProps)?.onTagChange?.(val, ctx);\n          }}\n          onPopupVisibleChange={(val: boolean, context) => {\n            if (isDisabled.value) return;\n            setVisible(val, context);\n            (props?.selectInputProps as TdSelectInputProps)?.onPopupVisibleChange?.(val, context);\n          }}\n          onBlur={(val, context) => {\n            props.onBlur?.({\n              value: cascaderContext.value.value,\n              inputValue: context.inputValue || '',\n              e: context.e as FocusEvent,\n            });\n            (props?.selectInputProps as TdSelectInputProps)?.onBlur?.(val, context);\n          }}\n          onFocus={(val, context) => {\n            props.onFocus?.({\n              value: cascaderContext.value.value,\n              e: context.e,\n            });\n            (props?.selectInputProps as TdSelectInputProps)?.onFocus?.(val, context);\n          }}\n          onClear={(context: { e: MouseEvent }) => {\n            closeIconClickEffect(cascaderContext.value);\n            (props?.selectInputProps as TdSelectInputProps)?.onClear?.(context);\n          }}\n          v-slots={{\n            label: slots.label,\n            suffix: slots.suffix,\n            prefixIcon: slots.prefixIcon,\n            panel: () => (\n              <>\n                {renderTNodeJSX('panelTopContent')}\n                <Panel\n                  option={props.option}\n                  empty={props.empty}\n                  visible={visible}\n                  trigger={props.trigger}\n                  loading={props.loading}\n                  loadingText={props.loadingText}\n                  cascaderContext={cascaderContext.value}\n                  v-slots={{ option: slots.option, empty: slots.empty, loadingText: slots.loadingText }}\n                />\n                {renderTNodeJSX('panelBottomContent')}\n              </>\n            ),\n            collapsedItems: slots.collapsedItems,\n          }}\n          {...omit(props.selectInputProps as TdSelectInputProps, [\n            'onTagChange',\n            'onInputChange',\n            'onPopupVisibleChange',\n            'onBlur',\n            'onFocus',\n            'onClear',\n          ])}\n        />\n      );\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "props", "setup", "slots", "_ref", "COMPONENT_NAME", "usePrefixClass", "classPrefix", "_useCommonClassName", "useCommonClassName", "STATUS", "overlayClassName", "_useConfig", "useConfig", "globalConfig", "isDisabled", "useDisabled", "is<PERSON><PERSON><PERSON>ly", "useReadonly", "renderTNodeJSX", "useTNodeJSX", "_useCascaderContext", "useCascaderContext", "cascaderContext", "innerValue", "isFilterable", "getCascaderItems", "displayValue", "computed", "multiple", "getMultipleContent", "value", "getSingleContent", "panels", "getPanels", "treeNodes", "inputPlaceholder", "_props2$placeholder", "visible", "placeholder", "renderSuffixIcon", "suffixIcon", "_cascaderContext$valu", "disabled", "_createVNode", "FakeArrow", "getFakeArrowIconClass", "valueDisplayParams", "arrayValue", "Array", "minCollapsedNum", "slice", "options", "selectedOptions", "onClose", "index", "handleRemoveTagEffect", "onRemove", "renderValueDisplay", "params", "renderLabel", "label", "concat", "cascaderClassNames", "_props2$popupProps", "_cascaderContext$valu2", "setVisible", "inputVal", "setInputVal", "SelectInput", "_mergeProps", "keys", "collapsedItems", "clearable", "loading", "status", "tips", "borderless", "prefixIcon", "suffix", "popupProps", "overlayInnerStyle", "length", "width", "_objectSpread", "size", "inputProps", "tagInputProps", "tagProps", "onInputChange", "ctx", "_props2$selectInputPr", "_props2$selectInputPr2", "selectInputProps", "onTagChange", "val", "_props2$selectInputPr3", "_props2$selectInputPr4", "trigger", "onPopupVisibleChange", "context", "_props2$selectInputPr5", "_props2$selectInputPr6", "onBlur", "_props2$onBlur", "_props2$selectInputPr7", "_props2$selectInputPr8", "inputValue", "e", "onFocus", "_props2$onFocus", "_props2$selectInputPr9", "_props2$selectInputPr0", "onClear", "_props2$selectInputPr1", "_props2$selectInputPr10", "closeIconClickEffect", "omit", "panel", "Panel", "option", "empty", "loadingText"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,gBAAeA,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,WAAA;AACNC,EAAAA,KAAA,EAAAA,yBAAA;AACAC,EAAAA,KAAMD,WAANC,KAAMD,CAAAA,MAAAA,EAAAA,IAAAA,EAAkB;AAAA,IAAA,IAATE,KAAA,GAAAC,IAAA,CAAAD,KAAA,CAAA;AACP,IAAA,IAAAE,cAAA,GAAiBC,qBAAe,UAAU,CAAA,CAAA;AAChD,IAAA,IAAMC,cAAcD,oBAAe,EAAA,CAAA;AAC7B,IAAA,IAAAE,mBAAA,GAAaC,0BAAmB,EAAA;MAA9BC,MAAO,GAAAF,mBAAA,CAAPE,MAAO,CAAA;AACT,IAAA,IAAAC,gBAAA,GAAmBL,qBAAe,iBAAiB,CAAA,CAAA;AACzD,IAAA,IAAAM,UAAA,GAAyBC,wCAAA,CAAU,UAAU,CAAA;MAArCC,YAAA,GAAAF,UAAA,CAAAE,YAAA,CAAA;AACR,IAAA,IAAMC,aAAaC,mBAAY,EAAA,CAAA;AAC/B,IAAA,IAAMC,aAAaC,mBAAY,EAAA,CAAA;AAC/B,IAAA,IAAMC,iBAAiBC,mBAAY,EAAA,CAAA;AAGnC,IAAA,IAAAC,mBAAA,GAAwEC,wCAAmBrB,MAAK,CAAA;MAAxFsB,eAAiB,GAAAF,mBAAA,CAAjBE,eAAiB;MAAAC,UAAA,GAAAH,mBAAA,CAAAG,UAAA;MAAYC,mCAAAA;MAAcC,gBAAiB,GAAAL,mBAAA,CAAjBK,gBAAiB,CAAA;IAEpE,IAAMC,YAAe,GAAAC,YAAA,CAAS,YAAA;AAAA,MAAA,OAC5B3B,OAAM4B,QAAW,GAAAC,wCAAA,CAAmBP,gBAAgBQ,KAAK,CAAA,GAAIC,sCAAiB,CAAAT,eAAA,CAAgBQ,KAAK,CAAA,CAAA;AAAA,KACrG,CAAA,CAAA;IAEA,IAAME,SAASL,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMM,gCAAUX,eAAgB,CAAAQ,KAAA,CAAMI,SAAS,CAAC,CAAA;KAAA,CAAA,CAAA;IAExE,IAAMC,gBAAmB,GAAAR,YAAA,CACvB,YAAA;AAAA,MAAA,IAAAS,mBAAA,CAAA;AAAA,MAAA,OACGd,eAAA,CAAgBQ,KAAM,CAAAO,OAAA,IAAW,CAACrC,MAAM,CAAA4B,QAAA,IAAYG,sCAAiB,CAAAT,eAAA,CAAgBQ,KAAK,CAAA,KAAA,CAAAM,mBAAA,GAC1FpC,MAAM,CAAAsC,WAAA,MAAAF,IAAAA,IAAAA,mBAAA,KAAAA,KAAAA,CAAAA,GAAAA,mBAAA,GAAevB,aAAaiB,KAAM,CAAAQ,WAAA,CAAA,CAAA;AAAA,KAC7C,CAAA,CAAA;AAEA,IAAA,IAAMC,mBAAmB,SAAnBA,mBAAyB;AACzBvC,MAAAA,IAAAA,MAAAA,CAAMwC,UAAc,IAAAtC,KAAA,CAAMsC,UAAY,EAAA;QACxC,OAAOtB,eAAe,YAAY,CAAA,CAAA;AACpC,OAAA;AAEA,MAAA,IAAAuB,qBAAA,GAA8BnB,eAAgB,CAAAQ,KAAA;QAAtCO,OAAA,GAAAI,qBAAA,CAAAJ,OAAA;QAASK,QAAS,GAAAD,qBAAA,CAATC,QAAS,CAAA;MAC1B,OAAAC,eAAA,CAAAC,qCAAA,EAAA;AAAA,QAAA,kBAAA,EAEsBC,8CAAA,CAAsBvC,YAAYwB,KAAO,EAAArB,MAAA,CAAOqB,KAAO,EAAAR,eAAA,CAAgBQ,KAAK,CAAA;AAAA,QAAA,UAAA,EACpFO,OAAA;QAAA,UACAK,EAAAA,QAAAA;AACZ,OAAA,EAAA,IAAA,CAAA,CAAA;KAEJ,CAAA;AAEM,IAAA,IAAAI,kBAAA,GAAqBnB,aAAS,YAAM;AAClC,MAAA,IAAAoB,UAAA,GAAaxB,WAAWO,KAAiB,YAAAkB,KAAA,GAAQzB,WAAWO,KAAQ,GAAA,CAACP,WAAWO,KAAK,CAAA,CAAA;MACrFJ,IAAAA,aAAAA,GACJ1B,MAAM,CAAA4B,QAAA,IAAY5B,MAAM,CAAAiD,eAAA,GAAkBF,UAAW,CAAAG,KAAA,CAAM,CAAGlD,EAAAA,MAAAA,CAAMiD,eAAe,CAAA,GAAI1B,UAAW,CAAAO,KAAA,CAAA;AAC9F,MAAA,IAAAqB,OAAA,GAAU1B,iBAAiBsB,UAAU,CAAA,CAAA;MACpC,OAAA;QACLjB,OAAOP,UAAW,CAAAO,KAAA;AAClBsB,QAAAA,eAAiB,EAAAD,OAAA;AACjBE,QAAAA,OAAA,EAAS,SAATA,OAAAA,CAAUC,KAAkB,EAAA;UAC1BC,2CAAA,CAAsBjC,eAAgB,CAAAQ,KAAA,EAAOwB,KAAOtD,EAAAA,MAAAA,CAAMwD,QAAQ,CAAA,CAAA;SACpE;AACA9B,QAAAA,YAAAA,EAAAA,aAAAA;OACF,CAAA;AACF,KAAC,CAAA,CAAA;AAED,IAAA,IAAM+B,qBAAqB,SAArBA,qBAA2B;MAC/B,OAAOvC,eAAe,cAAgB,EAAA;QACpCwC,QAAQZ,kBAAmB,CAAAhB,KAAAA;AAC7B,OAAC,CAAA,CAAA;KACH,CAAA;AAEA,IAAA,IAAM6B,cAAc,SAAdA,cAAoB;AAClB,MAAA,IAAAC,KAAA,GAAQ1C,eAAe,OAAO,CAAA,CAAA;AACpC,MAAA,IAAIlB,MAAM,CAAA4B,QAAA,EAAiB,OAAAgC,KAAA,CAAA;AAC3B,MAAA,IAAI,CAACA,KAAA,EAAc,OAAA,IAAA,CAAA;AACnB,MAAA,OAAAjB,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAA,EAAA,CAAAkB,MAAA,CAAsBvD,WAAY,CAAAwB,KAAA,EAAA,oBAAA,CAAA;AAAA,OAAA,EAAA,CAA4B8B;KAChE,CAAA;IAEM,IAAAE,kBAAA,GAAqBnC,aAAS,YAAA;MAAA,OAAM,CACxCvB,cAAe,CAAA0B,KAAA,EACf9B,OAAM4B,QAAW,GAAAiC,EAAAA,CAAAA,MAAA,CAAGzD,cAAe,CAAA0B,KAAA,EAAA+B,YAAAA,CAAAA,GAAAA,EAAAA,CAAAA,MAAA,CAAuBzD,cAAe,CAAA0B,KAAA,EAAA,UAAA,CAAA,CAC1E,CAAA;KAAA,CAAA,CAAA;AAED,IAAA,OAAO,YAAM;AAAA,MAAA,IAAAiC,kBAAA,CAAA;AACX,MAAA,IAAAC,sBAAA,GAAuD1C,eAAgB,CAAAQ,KAAA;QAA/DmC,UAAY,GAAAD,sBAAA,CAAZC,UAAY;QAAA5B,OAAA,GAAA2B,sBAAA,CAAA3B,OAAA;QAAS6B,QAAU,GAAAF,sBAAA,CAAVE,QAAU;QAAAC,WAAA,GAAAH,sBAAA,CAAAG,WAAA,CAAA;AAEvC,MAAA,OAAAxB,eAAA,CAAAyB,6BAAA,EAAAC,cAAA,CAAA;QAAA,OAEWP,EAAAA,kBAAA,CAAmBhC,KAC1B;QAAA,OAAOJ,EAAAA,YAAa,CAAAI,KAAA;AAAA,QAAA,YAAA,EACRO,OAAA,GAAU6B,QAAW,GAAA,EAAA;AAAA,QAAA,cAAA,EACnB7B,OACd;QAAA,MAAMrC,EAAAA,MAAM,CAAAsE,IAAA;QAAA,YACA9C,EAAAA,YAAA,CAAaM,KACzB;QAAA,mBAAmB9B,EAAAA,OAAMiD,eACzB;QAAA,iBAAiBjD,EAAAA,MAAM,CAAAuE,cAAA;QAAA,UACbvD,EAAAA,UAAA,CAAWc,KACrB;QAAA,UAAUhB,EAAAA,WAAWgB,KACrB;QAAA,WAAW9B,EAAAA,MAAM,CAAAwE,SAAA;QAAA,aACJrC,EAAAA,gBAAA,CAAiBL,KAC9B;QAAA,UAAU9B,EAAAA,OAAM4B,QAChB;QAAA,SAAS5B,EAAAA,MAAM,CAAAyE,OAAA;QAAA,QACPzE,EAAAA,MAAAA,CAAM0E,MACd;QAAA,MAAM1E,EAAAA,OAAM2E,IACZ;QAAA,YAAY3E,EAAAA,MAAM,CAAA4E,UAAA;AAAA,QAAA,OAAA,EACXjB,WAAA;AAAA,QAAA,cAAA,EACOF,kBAAA;QAAA,YACFzD,EAAAA,MAAM,CAAA6E,UAAA;QAAA,QACV7E,EAAAA,MAAAA,CAAM8E,MACd;AAAA,QAAA,YAAA,EAAY,SAAAtC,UAAA,GAAA;UAAA,OAAMD,gBAAA;;kDAEZvC,EAAAA,EAAAA,MAAM,CAAA+E,UAAA,CAAA,EAAA,EAAA,EAAA;UACVC,iBAAA,EAAmBhD,MAAO,CAAAF,KAAA,CAAMmD,MAAU,IAAA,CAACjF,OAAMyE,OAAU,GAAA;AAAES,YAAAA,KAAO,EAAA,MAAA;AAAO,WAAI,GAAA,EAAA;AAC/ExE,UAAAA,gBAAkB,EAAA,CAChBA,gBAAiB,CAAAoB,KAAA,GAAAiC,kBAAA,GAChB/D,OAAM+E,UAA8C,MAAAhB,IAAAA,IAAAA,kBAAA,KAApD/D,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,kBAAAA,CAAoDU,gBAAA,CAAA;AACvD,SAAA,CAAA;AAAA,QAAA,YAAA,EAAAyE,aAAA,CAAA;UAEYC,IAAMpF,EAAAA,MAAAA,CAAMoF,IAAAA;SAAUpF,EAAAA,MAAAA,CAAMqF,UAA6C,CAAA;AAAA,QAAA,eAAA,EAAAF,aAAA,CAAA;UAErFC,MAAMpF,MAAM,CAAAoF,IAAAA;SACRpF,EAAAA,MAAM,CAAAsF,aAAA,CAAA;AAAA,QAAA,UAAA,EAAAH,aAAA,CAAA,EAAA,EAEInF,MAAAA,CAAMuF;yBACP,SAAAC,aAAAA,CAAC1D,KAAA,EAAO2D,GAAQ,EAAA;UAAA,IAAAC,qBAAA,EAAAC,sBAAA,CAAA;AAC7B,UAAA,IAAI,CAACnE,YAAa,CAAAM,KAAA,EAAO,OAAA;AACzBqC,UAAAA,WAAA,CAAAN,EAAAA,CAAAA,MAAA,CAAe/B,KAAO,CAAA,CAAA,CAAA;AACrB9B,UAAAA,MAAO,KAAPA,IAAAA,IAAAA,MAAO,KAAA0F,KAAAA,CAAAA,IAAAA,CAAAA,qBAAA,GAAP1F,MAAO,CAAA4F,gBAAA,MAAAF,IAAAA,IAAAA,qBAAA,KAAAC,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAP3F,qBAAAA,CAAgDwF,aAAgB,MAAAG,IAAAA,IAAAA,sBAAA,KAAhE3F,KAAAA,CAAAA,IAAAA,sBAAAA,CAAAA,IAAAA,CAAAA,qBAAAA,EAAgE8B,KAAA,EAAO2D,GAAG,CAAA,CAAA;SAE7E;AAAA,QAAA,aAAA,EAAa,SAAAI,WAAAA,CAACC,GAAA,EAAoBL,GAAQ,EAAA;UAAA,IAAAM,sBAAA,EAAAC,sBAAA,CAAA;AAExC,UAAA,IAAIP,IAAIQ,OAAY,KAAA,OAAA,EAAS,OAAA;AAC7B1C,UAAAA,2CAAA,CAAsBjC,eAAgB,CAAAQ,KAAA,EAAO2D,GAAI,CAAAnC,KAAA,EAAOtD,OAAMwD,QAAQ,CAAA,CAAA;AAErExD,UAAAA,MAAO,KAAPA,IAAAA,IAAAA,MAAO,KAAA+F,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAP/F,MAAO,CAAA4F,gBAAA,MAAAG,IAAAA,IAAAA,sBAAA,KAAAC,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAPhG,sBAAAA,CAAgD6F,WAAc,MAAAG,IAAAA,IAAAA,sBAAA,KAA9DhG,KAAAA,CAAAA,IAAAA,sBAAAA,CAAAA,IAAAA,CAAAA,sBAAAA,EAA8D8F,GAAA,EAAKL,GAAG,CAAA,CAAA;SAEzE;AAAA,QAAA,sBAAA,EAAsB,SAAAS,oBAAAA,CAACJ,GAAA,EAAcK,OAAY,EAAA;UAAA,IAAAC,sBAAA,EAAAC,sBAAA,CAAA;UAC/C,IAAIvF,UAAW,CAAAgB,KAAA,EAAO,OAAA;AACtBmC,UAAAA,UAAA,CAAW6B,KAAKK,OAAO,CAAA,CAAA;AACtBnG,UAAAA,MAAO,KAAPA,IAAAA,IAAAA,MAAO,KAAAoG,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAPpG,MAAO,CAAA4F,gBAAA,MAAAQ,IAAAA,IAAAA,sBAAA,KAAAC,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAPrG,sBAAAA,CAAgDkG,oBAAuB,MAAAG,IAAAA,IAAAA,sBAAA,KAAvErG,KAAAA,CAAAA,IAAAA,sBAAAA,CAAAA,IAAAA,CAAAA,sBAAAA,EAAuE8F,GAAA,EAAKK,OAAO,CAAA,CAAA;SAEtF;AAAA,QAAA,QAAA,EAAQ,SAAAG,MAAAA,CAACR,GAAA,EAAKK,OAAY,EAAA;AAAA,UAAA,IAAAI,cAAA,EAAAC,sBAAA,EAAAC,sBAAA,CAAA;AACxBzG,UAAAA,CAAAA,cAAAA,GAAAA,OAAMsG,MAAS,MAAA,IAAA,IAAAC,cAAA,KAAA,KAAA,CAAA,IAAfvG,cAAAA,CAAAA,IAAAA,CAAAA,QAAe;AACb8B,YAAAA,KAAA,EAAOR,gBAAgBQ,KAAM,CAAAA,KAAA;AAC7B4E,YAAAA,UAAA,EAAYP,QAAQO,UAAc,IAAA,EAAA;YAClCC,GAAGR,OAAQ,CAAAQ,CAAAA;AACb,WAAC,CAAA,CAAA;AACA3G,UAAAA,MAAO,KAAPA,IAAAA,IAAAA,MAAO,KAAAwG,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAPxG,MAAO,CAAA4F,gBAAA,MAAAY,IAAAA,IAAAA,sBAAA,KAAAC,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAPzG,sBAAAA,CAAgDsG,MAAS,MAAAG,IAAAA,IAAAA,sBAAA,KAAzDzG,KAAAA,CAAAA,IAAAA,sBAAAA,CAAAA,IAAAA,CAAAA,sBAAAA,EAAyD8F,GAAA,EAAKK,OAAO,CAAA,CAAA;SAExE;AAAA,QAAA,SAAA,EAAS,SAAAS,OAAAA,CAACd,GAAA,EAAKK,OAAY,EAAA;AAAA,UAAA,IAAAU,eAAA,EAAAC,sBAAA,EAAAC,sBAAA,CAAA;AACzB/G,UAAAA,CAAAA,eAAAA,GAAAA,OAAM4G,OAAU,MAAA,IAAA,IAAAC,eAAA,KAAA,KAAA,CAAA,IAAhB7G,eAAAA,CAAAA,IAAAA,CAAAA,QAAgB;AACd8B,YAAAA,KAAA,EAAOR,gBAAgBQ,KAAM,CAAAA,KAAA;YAC7B6E,GAAGR,OAAQ,CAAAQ,CAAAA;AACb,WAAC,CAAA,CAAA;AACA3G,UAAAA,MAAO,KAAPA,IAAAA,IAAAA,MAAO,KAAA8G,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAP9G,MAAO,CAAA4F,gBAAA,MAAAkB,IAAAA,IAAAA,sBAAA,KAAAC,KAAAA,CAAAA,IAAAA,CAAAA,sBAAA,GAAP/G,sBAAAA,CAAgD4G,OAAU,MAAAG,IAAAA,IAAAA,sBAAA,KAA1D/G,KAAAA,CAAAA,IAAAA,sBAAAA,CAAAA,IAAAA,CAAAA,sBAAAA,EAA0D8F,GAAA,EAAKK,OAAO,CAAA,CAAA;SACzE;QAAA,SACS,EAAA,SAAAa,OAACb,CAAAA,OAA+B,EAAA;UAAA,IAAAc,sBAAA,EAAAC,uBAAA,CAAA;AACvCC,UAAAA,0CAAA,CAAqB7F,gBAAgBQ,KAAK,CAAA,CAAA;UACzC9B,MAAAA,KAAAA,IAAAA,IAAAA,MAAAA,KAAAA,KAAAA,CAAAA,IAAAA,CAAAA,sBAAAA,GAAAA,MAAAA,CAAO4F,gBAAyC,MAAA,IAAA,IAAAqB,sBAAA,KAAA,KAAA,CAAA,IAAA,CAAAC,uBAAA,GAAhDlH,sBAAAA,CAAgDgH,OAAA,MAAAE,IAAAA,IAAAA,uBAAA,KAAhDlH,KAAAA,CAAAA,IAAAA,uBAAAA,CAAAA,IAAAA,CAAAA,sBAAAA,EAA0DmG,OAAO,CAAA,CAAA;AACpE,SAAA;OAuBIiB,EAAAA,SAAKpH,CAAAA,MAAAA,CAAM4F,gBAAwC,EAAA,CACrD,aAAA,EACA,eAAA,EACA,sBAAA,EACA,QAAA,EACA,SAAA,EACA,SAAA,CACD,CACH,CA9BW,EAAA;QACPhC,OAAO1D,KAAM,CAAA0D,KAAA;QACbkB,QAAQ5E,KAAM,CAAA4E,MAAA;QACdD,YAAY3E,KAAM,CAAA2E,UAAA;QAClBwC,OAAO,SAAPA;+CAEKnG,IAAAA,EAAAA,CAAAA,eAAe,iBAAiB,CAAA,EAAAyB,eAAA,CAAA2E,oCAAA,EAAA;YAAA,QAEvBtH,EAAAA,MAAAA,CAAMuH;mBACPvH,EAAAA,MAAAA,CAAMwH,KACb;AAAA,YAAA,SAAA,EAASnF,OACT;YAAA,SAASrC,EAAAA,MAAM,CAAAiG,OAAA;YAAA,SACNjG,EAAAA,MAAM,CAAAyE,OAAA;YAAA,aACFzE,EAAAA,MAAAA,CAAMyH,WACnB;AAAA,YAAA,iBAAA,EAAiBnG,eAAgB,CAAAQ,KAAAA;WACxB,EAAA;YAAEyF,MAAA,EAAQrH,KAAM,CAAAqH,MAAA;YAAQC,OAAOtH,KAAM,CAAAsH,KAAA;YAAOC,WAAa,EAAAvH,KAAA,CAAMuH,WAAAA;AAAY,WACtF,CACCvG,EAAAA,eAAe,oBAAoB,CAAA,CAAA,CAAA,CAAA;SACtC;QAEFqD,gBAAgBrE,KAAM,CAAAqE,cAAAA;OACxB,CAAA,CAAA;KAWN,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}