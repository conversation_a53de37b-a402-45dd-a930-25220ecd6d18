import { CalendarCell } from './type';
declare const _default: import("vue").DefineComponent<{
    item: {
        type: ObjectConstructor;
        default: () => CalendarCell;
    };
    fillWithZero: {
        type: BooleanConstructor;
        default: any;
    };
    theme: {
        type: StringConstructor;
        default: () => string;
    };
    t: FunctionConstructor;
    global: ObjectConstructor;
    cell: (FunctionConstructor | StringConstructor)[];
    cellAppend: (FunctionConstructor | StringConstructor)[];
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, string[], string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    item: {
        type: ObjectConstructor;
        default: () => CalendarCell;
    };
    fillWithZero: {
        type: BooleanConstructor;
        default: any;
    };
    theme: {
        type: StringConstructor;
        default: () => string;
    };
    t: FunctionConstructor;
    global: ObjectConstructor;
    cell: (FunctionConstructor | StringConstructor)[];
    cellAppend: (FunctionConstructor | StringConstructor)[];
}>> & {
    [x: `on${Capitalize<string>}`]: (...args: any[]) => any;
}, {
    item: Record<string, any>;
    theme: string;
    fillWithZero: boolean;
}, {}>;
export default _default;
