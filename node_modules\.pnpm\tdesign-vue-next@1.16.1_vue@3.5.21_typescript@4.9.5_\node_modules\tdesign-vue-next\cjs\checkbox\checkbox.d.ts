declare const _default: import("vue").DefineComponent<{
    needRipple: BooleanConstructor;
    stopLabelTrigger: BooleanConstructor;
    index: NumberConstructor;
    data: ObjectConstructor;
    checkAll: BooleanConstructor;
    checked: {
        type: BooleanConstructor;
        default: any;
    };
    modelValue: {
        type: BooleanConstructor;
        default: any;
    };
    defaultChecked: BooleanConstructor;
    default: {
        type: import("vue").PropType<import("./type").TdCheckboxProps["default"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    indeterminate: BooleanConstructor;
    label: {
        type: import("vue").PropType<import("./type").TdCheckboxProps["label"]>;
    };
    lazyLoad: BooleanConstructor;
    name: {
        type: StringConstructor;
        default: string;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    title: {
        type: StringConstructor;
        default: string;
    };
    value: {
        type: import("vue").PropType<import("./type").TdCheckboxProps["value"]>;
    };
    onChange: import("vue").PropType<import("./type").TdCheckboxProps["onChange"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    needRipple: BooleanConstructor;
    stopLabelTrigger: BooleanConstructor;
    index: NumberConstructor;
    data: ObjectConstructor;
    checkAll: BooleanConstructor;
    checked: {
        type: BooleanConstructor;
        default: any;
    };
    modelValue: {
        type: BooleanConstructor;
        default: any;
    };
    defaultChecked: BooleanConstructor;
    default: {
        type: import("vue").PropType<import("./type").TdCheckboxProps["default"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    indeterminate: BooleanConstructor;
    label: {
        type: import("vue").PropType<import("./type").TdCheckboxProps["label"]>;
    };
    lazyLoad: BooleanConstructor;
    name: {
        type: StringConstructor;
        default: string;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    title: {
        type: StringConstructor;
        default: string;
    };
    value: {
        type: import("vue").PropType<import("./type").TdCheckboxProps["value"]>;
    };
    onChange: import("vue").PropType<import("./type").TdCheckboxProps["onChange"]>;
}>>, {
    disabled: boolean;
    name: string;
    checked: boolean;
    indeterminate: boolean;
    title: string;
    defaultChecked: boolean;
    modelValue: boolean;
    readonly: boolean;
    needRipple: boolean;
    stopLabelTrigger: boolean;
    checkAll: boolean;
    lazyLoad: boolean;
}, {}>;
export default _default;
