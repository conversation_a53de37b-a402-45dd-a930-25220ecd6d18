#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/bin/barcodes/EAN_UPC/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/bin/barcodes/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/bin/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/jsbarcode@3.11.5/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/bin/barcodes/EAN_UPC/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/bin/barcodes/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/bin/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/jsbarcode@3.11.5/node_modules/jsbarcode/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/jsbarcode@3.11.5/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../jsbarcode@3.11.5/node_modules/jsbarcode/bin/barcodes/EAN_UPC/UPCE.js" "$@"
else
  exec node  "$basedir/../../../../../../jsbarcode@3.11.5/node_modules/jsbarcode/bin/barcodes/EAN_UPC/UPCE.js" "$@"
fi
