import { TdCascaderProps } from './type';
import { PropType } from 'vue';
declare const _default: {
    autofocus: BooleanConstructor;
    borderless: BooleanConstructor;
    checkProps: {
        type: PropType<TdCascaderProps["checkProps"]>;
    };
    checkStrictly: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: PropType<TdCascaderProps["collapsedItems"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    empty: {
        type: PropType<TdCascaderProps["empty"]>;
    };
    filter: {
        type: PropType<TdCascaderProps["filter"]>;
    };
    filterable: BooleanConstructor;
    inputProps: {
        type: PropType<TdCascaderProps["inputProps"]>;
    };
    keys: {
        type: PropType<TdCascaderProps["keys"]>;
    };
    label: {
        type: PropType<TdCascaderProps["label"]>;
    };
    lazy: {
        type: BooleanConstructor;
        default: boolean;
    };
    load: {
        type: PropType<TdCascaderProps["load"]>;
    };
    loading: BooleanConstructor;
    loadingText: {
        type: PropType<TdCascaderProps["loadingText"]>;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    option: {
        type: PropType<TdCascaderProps["option"]>;
    };
    options: {
        type: PropType<TdCascaderProps["options"]>;
        default: () => TdCascaderProps["options"];
    };
    panelBottomContent: {
        type: PropType<TdCascaderProps["panelBottomContent"]>;
    };
    panelTopContent: {
        type: PropType<TdCascaderProps["panelTopContent"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: PropType<TdCascaderProps["popupProps"]>;
    };
    popupVisible: BooleanConstructor;
    prefixIcon: {
        type: PropType<TdCascaderProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    reserveKeyword: BooleanConstructor;
    selectInputProps: {
        type: PropType<TdCascaderProps["selectInputProps"]>;
    };
    showAllLevels: {
        type: BooleanConstructor;
        default: boolean;
    };
    size: {
        type: PropType<TdCascaderProps["size"]>;
        default: TdCascaderProps["size"];
        validator(val: TdCascaderProps["size"]): boolean;
    };
    status: {
        type: PropType<TdCascaderProps["status"]>;
        default: TdCascaderProps["status"];
        validator(val: TdCascaderProps["status"]): boolean;
    };
    suffix: {
        type: PropType<TdCascaderProps["suffix"]>;
    };
    suffixIcon: {
        type: PropType<TdCascaderProps["suffixIcon"]>;
    };
    tagInputProps: {
        type: PropType<TdCascaderProps["tagInputProps"]>;
    };
    tagProps: {
        type: PropType<TdCascaderProps["tagProps"]>;
    };
    tips: {
        type: PropType<TdCascaderProps["tips"]>;
    };
    trigger: {
        type: PropType<TdCascaderProps["trigger"]>;
        default: TdCascaderProps["trigger"];
        validator(val: TdCascaderProps["trigger"]): boolean;
    };
    value: {
        type: PropType<TdCascaderProps["value"]>;
        default: TdCascaderProps["value"];
    };
    modelValue: {
        type: PropType<TdCascaderProps["value"]>;
        default: TdCascaderProps["value"];
    };
    defaultValue: {
        type: PropType<TdCascaderProps["defaultValue"]>;
        default: () => TdCascaderProps["defaultValue"];
    };
    valueDisplay: {
        type: PropType<TdCascaderProps["valueDisplay"]>;
    };
    valueMode: {
        type: PropType<TdCascaderProps["valueMode"]>;
        default: TdCascaderProps["valueMode"];
        validator(val: TdCascaderProps["valueMode"]): boolean;
    };
    valueType: {
        type: PropType<TdCascaderProps["valueType"]>;
        default: TdCascaderProps["valueType"];
        validator(val: TdCascaderProps["valueType"]): boolean;
    };
    onBlur: PropType<TdCascaderProps["onBlur"]>;
    onChange: PropType<TdCascaderProps["onChange"]>;
    onFocus: PropType<TdCascaderProps["onFocus"]>;
    onPopupVisibleChange: PropType<TdCascaderProps["onPopupVisibleChange"]>;
    onRemove: PropType<TdCascaderProps["onRemove"]>;
};
export default _default;
