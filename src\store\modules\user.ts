import {
  reqChangeTenant,
  reqLogin,
  reqLogout,
  reqUserInfo,
  queryAllGroupTreeList,
  queryUserFunction,
  queryToolYeMianAuthListAPI,
} from '@/api'
import { TOKEN_NAME } from '@/config/global'
import { loginRequestListI, userInfoType, encrypUserInfo } from '@/types'
import { defineStore } from 'pinia'
import store from '..'
import { filterAsyncRoutes, filterMenus } from '@/utils'
import {
  filterAsyncRoutes as filterAsyncRoutes2,
  filterMenus as filterMenus2,
} from '@/utils/common copy'
import { asyncRoutes } from '@/router/routes'
import { RouteRecordRaw } from 'vue-router'
import router from '@/router'
import { ElMessageBox } from 'element-plus'
import { useThemeStoreHook } from '@/store/modules/theme'
import { getAppInfoStore } from '@/store/modules/app'
import { get } from 'lodash'
import { number } from 'echarts'
export const useUserStore = defineStore('user', {
  state: () => ({
    token: '',
    tenantCode: '',
    userInfo: {} as userInfoType,
    loginUserTenantList: [{ tenantId: '', tenantName: '' }], //用户登录的租户列表
    userId: '',
    longToken: '',
    companyName: '', // 企业名称
    selectIdIndex: 0, // 企业ID
    admin: false,
    permission: [] as any[],
    menus: [] as any[],
    menusNav: [] as any[],
    specialMenus: [] as any[],
    menusNavCount: 7,
    isCollapse: true,
    isManualCollapse: false, // 是否手动折叠
    definedTemCodeList: [],
    permissionList: [] as any[],
    appId: '',
    sheetName: '',
    tenantId: '',
    selectTableName: '',
    messageCount: 0, // 消息数量
  }),
  actions: {
    setMessageCount(messageCount: number) {
      this.messageCount = messageCount
    },
    setSheetName(name: string) {
      this.sheetName = name
    },
    getSheetName() {
      return this.sheetName
    },
    setAppId(id: string) {
      this.appId = id
    },
    getAppId() {
      return this.appId
    },
    setLoginUserTenantList(loginUserTenantList: any[]) {
      this.loginUserTenantList = loginUserTenantList
    },
    setTenantId(tenantId: string) {
      this.tenantId = tenantId
    },
    setUserId(userId: any) {
      this.userId = userId
    },
    userSetLongToken(longToken: string) {
      this.longToken = longToken
    },
    setMenus(menus: any[]) {
      this.menus = menus
    },
    async userLogin(
      data: any,
      callback: (resList: loginRequestListI[]) => void,
    ) {
      const res = await reqLogin(data)
      this.loginUserTenantList = res.loginUserTenantList
      callback(res.loginUserTenantList)
    },
    async userLogout(callback: () => void) {
      if (this.userInfo.username) {
        await reqLogout()
      }
      useThemeStoreHook().changeTheme('light')
      this.$reset()
      if (location.origin === 'https://apps.nytanbao.com') {
        location.href = 'https://client.nytanbao.com/login'
      } else {
        callback()
      }
    },
    async stateReqUserInfo() {
      const result = await reqUserInfo()
      this.userInfo.username = result.username
      this.userInfo.name = result.name
      this.companyName = result.tenantName
      return this.userInfo.username
    },
    async setUserPermission() {
      let roles = await queryUserFunction('kuangjia')

      this.permissionList = roles
        .filter((item: any) => item.type === 2)
        .map((item: any) => item.viewCode)
      // 通过接口返回过滤路由
      let tempRoutes = asyncRoutes
      // const accessMenus = filterMenus(asyncRoutes, roles)
      // if (getAppInfoStore().appCode) {
      //   try {
      //     const module = await import(
      //       `../../router/configRoutes-${getAppInfoStore().appCode}.ts`
      //     )
      //     tempRoutes = module.default
      //   } catch (e) {
      //     console.log('appCode is not exist')
      //   }
      // }
      try {
        const allGroupTreeList = await queryAllGroupTreeList()

        if (allGroupTreeList) {
          for (const item of allGroupTreeList) {
            // 分类之下直接带页面
            if (item.formId) {
              // tempRoutes.push({
              //   path: `/${item.formId}`,
              //   name: item.formId,
              //   component: () =>
              //     import('@/components/prod-dynamic-container/index.vue'),
              //   meta: {
              //     menuName: item.formName || item.formId,
              //     hidden: true,
              //     isConfig: true,
              //   },
              // })

              tempRoutes.push({
                path: `/${item.formId}`,
                component: () => import('@/views/layout/index.vue'),
                name: `/${item.formId}`,
                meta: {
                  appId: -1,
                },
                redirect: `/${item.formId}/${item.formId}`,
                children: [
                  {
                    path: `/${item.formId}/${item.formId}`,
                    name: `/${item.formId}/${item.formId}`,
                    component: () =>
                      import('@/components/prod-dynamic-container/index.vue'),
                    meta: {
                      menuName: item.formName || item.formId,
                      hidden: true,
                      isConfig: true,
                      appId: -1,
                    },
                  },
                ],
              })
            }
            // 分类之下带应用
            if (item.data) {
              let appFirstRoute: any
              // 应用之下直接带页面
              if (item.data.formInfoList.length > 0) {
                for (const form of item.data.formInfoList) {
                  if (!appFirstRoute) {
                    tempRoutes.push({
                      path: `/${item.data.groupId}`,
                      redirect: `/${item.data.groupId}/${form.formId}`,
                      meta: {
                        isConfig: true,
                        appId: item.data.groupId,
                        hidden: true,
                        menuName: '跳转路由',
                      },
                    })
                  }
                  const appRoutes = {
                    path: `/${item.data.groupId}/${form.formId}`,
                    name: form.formId,
                    component: () => import('@/views/layout/index.vue'),
                    redirect: `/${item.data.groupId}/${form.formId}/${form.formId}`,
                    meta: {
                      menuName: form.formName || form.formId,
                      icon: 'page',
                      isConfig: true,
                      appId: item.data.groupId,
                    },
                    children: [
                      {
                        path: `/${item.data.groupId}/${form.formId}/${form.formId}`,
                        name: `/${item.data.groupId}/${form.formId}/${form.formId}`,
                        component: () =>
                          import(
                            '@/components/prod-dynamic-container/index.vue'
                          ),
                        meta: {
                          menuName: form.formName || form.formId,
                          hidden: true,
                          isConfig: true,
                          appId: item.data.groupId,
                        },
                      },
                    ],
                  }
                  tempRoutes.push(appRoutes)
                }
              }
              // 应用之下带功能
              if (item.children.length > 0) {
                for (const func of item.children) {
                  if (func.data.formInfoList.length > 0) {
                    if (!appFirstRoute) {
                      tempRoutes.push({
                        path: `/${item.data.groupId}`,
                        redirect: `/${item.data.groupId}/${func.data.groupId}`,
                        meta: {
                          isConfig: true,
                          appId: item.data.groupId,
                          hidden: true,
                          menuName: '跳转路由',
                        },
                      })
                    }
                    const appRoutes = {
                      path: `/${item.data.groupId}/${func.data.groupId}`,
                      name: `/${item.data.groupId}/${func.data.groupId}`,
                      component: () => import('@/views/layout/index.vue'),
                      redirect: `/${item.data.groupId}/${func.data.groupId}/${func.data.formInfoList[0].formId}`,
                      meta: {
                        menuName: func.data.groupName || func.data.groupId,
                        icon: 'page',
                        isConfig: true,
                        appId: item.data.groupId,
                      },
                      children: func.data.formInfoList.map((form: any) => {
                        return {
                          path: `/${item.data.groupId}/${func.data.groupId}/${form.formId}`,
                          name: `/${item.data.groupId}/${func.data.groupId}/${form.formId}`,
                          component: () =>
                            import(
                              '@/components/prod-dynamic-container/index.vue'
                            ),
                          meta: {
                            menuName: form.formName || form.formId,
                            isConfig: true,
                            appId: item.data.groupId,
                          },
                        }
                      }),
                    }
                    tempRoutes.push(appRoutes)
                  }
                }
              }
            }
          }
        }
      } catch (error) {}

      const accessRoutes = filterAsyncRoutes(tempRoutes, roles)
      const adminWorkbenchIndex = accessRoutes.findIndex(
        (item: any) => item.meta?.viewCode === 'workbench',
      )
      const userWorkbenchIndex = accessRoutes.findIndex(
        (item: any) => item.meta?.viewCode === 'userWorkbench',
      )
      if (adminWorkbenchIndex !== -1 && userWorkbenchIndex !== -1) {
        accessRoutes.splice(userWorkbenchIndex, 1)
      }

      if (roles.length === 0 || accessRoutes.length === 0) {
        //清除token
        this.$reset()
        // 提示没有配置用户权限
        ElMessageBox.confirm('没有配置用户权限，请联系管理员', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        })
        return
      }
      accessRoutes.forEach((route: RouteRecordRaw) => {
        // @ts-ignore
        router.addRoute({
          component: route.component,
          name: route.name,
          path: route.path,
          meta: route.meta,
          redirect: route.redirect,
        })
        if (route.children && route.children.length > 0) {
          // console.log(route.children)
          route.children.forEach((childRoute: RouteRecordRaw) => {
            // @ts-ignore
            router.addRoute(route.name, {
              component: childRoute.component,
              name: childRoute.name,
              path: childRoute.path,
              meta: childRoute.meta,
            })
          })
        }
      })

      // console.log(router.getRoutes())
      const accessMenus = filterMenus(accessRoutes, roles)

      this.setMenus(accessMenus)

      if (this.menus.length > 0) {
        const indexRoute: RouteRecordRaw = {
          path: '/',
          redirect: this.menus[0].path,
        }
        router.addRoute(indexRoute)
      }
    },
    async setUserPermission2() {
      let roles = await queryUserFunction('kuangjia')
      // let roles = await queryToolYeMianAuthListAPI()
      if (roles.length === 0) {
        ElMessage.warning('没有配置用户权限，请联系管理员')
        this.$reset()
        return
      }

      // 确保可视化配置权限存在
      const hasConfigurationPermission = roles.some(
        (role: any) => role.yeMianName === '可视化配置',
      )
      if (!hasConfigurationPermission) {
        roles.push({
          yeMianName: '可视化配置',
        })
      }

      // 通过接口返回过滤路由
      let tempRoutes = asyncRoutes
      // const accessMenus = filterMenus(asyncRoutes, roles)
      // if (getAppInfoStore().appCode) {
      //   try {
      //     const module = await import(
      //       `../../router/configRoutes-${getAppInfoStore().appCode}.ts`
      //     )
      //     tempRoutes = module.default
      //   } catch (e) {
      //     console.log('appCode is not exist')
      //   }
      // }
      try {
        const allGroupTreeList = await queryAllGroupTreeList()

        if (allGroupTreeList) {
          for (const item of allGroupTreeList) {
            // 分类之下直接带页面
            if (item.formId) {
              // tempRoutes.push({
              //   path: `/${item.formId}`,
              //   name: item.formId,
              //   component: () =>
              //     import('@/components/prod-dynamic-container/index.vue'),
              //   meta: {
              //     menuName: item.formName || item.formId,
              //     hidden: true,
              //     isConfig: true,
              //   },
              // })

              tempRoutes.push({
                path: `/${item.formId}`,
                component: () => import('@/views/layout/index.vue'),
                name: `/${item.formId}`,
                meta: {
                  appId: -1,
                },
                redirect: `/${item.formId}/${item.formId}`,
                children: [
                  {
                    path: `/${item.formId}/${item.formId}`,
                    name: `/${item.formId}/${item.formId}`,
                    component: () =>
                      import('@/components/prod-dynamic-container/index.vue'),
                    meta: {
                      menuName: item.formName || item.formId,
                      hidden: true,
                      isConfig: true,
                      appId: -1,
                    },
                  },
                ],
              })
            }
            // 分类之下带应用
            if (item.data) {
              let appFirstRoute: any
              // 应用之下直接带页面
              if (item.data.formInfoList.length > 0) {
                for (const form of item.data.formInfoList) {
                  if (!appFirstRoute) {
                    tempRoutes.push({
                      path: `/${item.data.groupId}`,
                      redirect: `/${item.data.groupId}/${form.formId}`,
                      meta: {
                        isConfig: true,
                        appId: item.data.groupId,
                        hidden: true,
                        menuName: '跳转路由',
                      },
                    })
                  }
                  const appRoutes = {
                    path: `/${item.data.groupId}/${form.formId}`,
                    name: form.formId,
                    component: () => import('@/views/layout/index.vue'),
                    redirect: `/${item.data.groupId}/${form.formId}/${form.formId}`,
                    meta: {
                      menuName: form.formName || form.formId,
                      icon: 'page',
                      isConfig: true,
                      appId: item.data.groupId,
                    },
                    children: [
                      {
                        path: `/${item.data.groupId}/${form.formId}/${form.formId}`,
                        name: `/${item.data.groupId}/${form.formId}/${form.formId}`,
                        component: () =>
                          import(
                            '@/components/prod-dynamic-container/index.vue'
                          ),
                        meta: {
                          menuName: form.formName || form.formId,
                          hidden: true,
                          isConfig: true,
                          appId: item.data.groupId,
                        },
                      },
                    ],
                  }
                  tempRoutes.push(appRoutes)
                }
              }
              // 应用之下带功能
              if (item.children.length > 0) {
                for (const func of item.children) {
                  if (func.data.formInfoList.length > 0) {
                    if (!appFirstRoute) {
                      tempRoutes.push({
                        path: `/${item.data.groupId}`,
                        redirect: `/${item.data.groupId}/${func.data.groupId}`,
                        meta: {
                          isConfig: true,
                          appId: item.data.groupId,
                          hidden: true,
                          menuName: '跳转路由',
                        },
                      })
                    }
                    const appRoutes = {
                      path: `/${item.data.groupId}/${func.data.groupId}`,
                      name: `/${item.data.groupId}/${func.data.groupId}`,
                      component: () => import('@/views/layout/index.vue'),
                      redirect: `/${item.data.groupId}/${func.data.groupId}/${func.data.formInfoList[0].formId}`,
                      meta: {
                        menuName: func.data.groupName || func.data.groupId,
                        icon: 'page',
                        isConfig: true,
                        appId: item.data.groupId,
                      },
                      children: func.data.formInfoList.map((form: any) => {
                        return {
                          path: `/${item.data.groupId}/${func.data.groupId}/${form.formId}`,
                          name: `/${item.data.groupId}/${func.data.groupId}/${form.formId}`,
                          component: () =>
                            import(
                              '@/components/prod-dynamic-container/index.vue'
                            ),
                          meta: {
                            menuName: form.formName || form.formId,
                            isConfig: true,
                            appId: item.data.groupId,
                          },
                        }
                      }),
                    }
                    tempRoutes.push(appRoutes)
                  }
                }
              }
            }
          }
        }
      } catch (error) {}

      const accessRoutes = filterAsyncRoutes2(tempRoutes, roles)
      const adminWorkbenchIndex = accessRoutes.findIndex(
        (item: any) => item.meta?.viewCode === 'workbench',
      )
      const userWorkbenchIndex = accessRoutes.findIndex(
        (item: any) => item.meta?.viewCode === 'userWorkbench',
      )
      if (adminWorkbenchIndex !== -1 && userWorkbenchIndex !== -1) {
        accessRoutes.splice(
          roles.length === 1 ? adminWorkbenchIndex : userWorkbenchIndex,
          1,
        )
      }

      if (roles.length === 0 || accessRoutes.length === 0) {
        //清除token
        this.$reset()
        // 提示没有配置用户权限
        ElMessageBox.confirm('没有配置用户权限，请联系管理员', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        })
        return
      }
      accessRoutes.forEach((route: RouteRecordRaw) => {
        // @ts-ignore
        router.addRoute({
          component: route.component,
          name: route.name,
          path: route.path,
          meta: route.meta,
          redirect: route.redirect,
        })
        if (route.children && route.children.length > 0) {
          // console.log(route.children)
          route.children.forEach((childRoute: RouteRecordRaw) => {
            // @ts-ignore
            router.addRoute(route.name, {
              component: childRoute.component,
              name: childRoute.name,
              path: childRoute.path,
              meta: childRoute.meta,
            })
          })
        }
      })

      // console.log(router.getRoutes())
      const accessMenus = filterMenus2(accessRoutes, roles)

      this.setMenus(accessMenus)

      if (this.menus.length > 0) {
        const indexRoute: RouteRecordRaw = {
          path: '/',
          redirect: this.menus[0].path,
        }
        router.addRoute(indexRoute)
      }
    },
    async changeTenant(reqChangeTenantId: string) {
      this.selectIdIndex = this.loginUserTenantList.findIndex((item) => {
        if (item.tenantId === reqChangeTenantId) {
          this.tenantId = item.tenantId
          this.companyName = item.tenantName
          return true
        }
      })
      const res = await reqChangeTenant(reqChangeTenantId)
      this.tenantCode = res.tenantCode
      this.tenantId = res.tenantId
      this.token = res.token
      this.longToken = res.longToken
      this.userInfo.name = res.name
    },
    getToken() {
      return this.token
    },
    userSetToken(token: string) {
      this.token = token
    },
    setTenantCode(tenantCode: string) {
      this.tenantCode = tenantCode
    },
    setSelectTableName(tableName: string) {
      this.selectTableName = tableName
    },
    getSelectTableName() {
      return this.selectTableName
    },
    userEncrypUserInfo(info: any) {
      this.userInfo.name = info.username
      this.companyName = info.systemName
      this.token = info.token
      this.loginUserTenantList = info.loginUserTenantList
      this.longToken = info.longToken
      this.tenantCode = info.tenantCode
      this.tenantId = info.tenantId

      // 找到companyName在loginUserTenantList中的索引
      this.selectIdIndex = this.loginUserTenantList.findIndex((item) => {
        if (item.tenantName === info.systemName) {
          return true
        }
      })
    },
    // convertNodesToRoutes(nodes: any) {
    //   return nodes.map((node: any) => {
    //     const route: any = {
    //       path: `/${node.code}`,
    //       name: node.name,
    //       component: () => import(`@/views/energyCarbonDataAnalysis/index.vue`),
    //       meta: {
    //         menuName: node.name,
    //         // viewCode: node.code,
    //         keepAlive: true,
    //       },
    //     }
    //
    //     if (node.children && node.children.length > 0) {
    //       route.children = this.convertNodesToRoutes(node.children)
    //     }
    //
    //     return route
    //   })
    // },
  },
  // 将状态在本地进行持久化存储
  persist: [
    {
      key: TOKEN_NAME,
      paths: ['token'],
    },
    {
      key: 'tenantCode',
      paths: ['tenantCode'],
    },
    {
      key: 'longToken',
      paths: ['longToken'],
    },
    {
      key: 'userId',
      paths: ['userId'],
    },
    {
      key: 'selectTableName',
      paths: ['selectTableName'],
    },
    {
      key: 'userInfo',
      paths: ['userInfo'],
    },
    {
      key: 'menusNav',
      paths: ['menusNav'],
    },
    {
      key: 'specialMenus',
      paths: ['specialMenus'],
    },
    {
      key: 'admin',
      paths: ['admin'],
    },
    {
      key: 'permission',
      paths: ['permission'],
    },
    {
      key: 'isCollapse',
      paths: ['isCollapse'],
    },
    {
      key: 'isManualCollapse',
      paths: ['isManualCollapse'],
    },
    {
      paths: ['companyName'],
      storage: localStorage,
      key: 'companyName',
    },
    {
      paths: ['loginUserTenantList'],
      storage: localStorage,
      key: 'loginUserTenantList',
    },
    {
      paths: ['selectIdIndex'],
      storage: localStorage,
      key: 'selectIdIndex',
    },
    {
      paths: ['menusNavCount'],
      storage: localStorage,
      key: 'menusNavCount',
    },
    {
      paths: ['tenantId'],
      storage: localStorage,
      key: 'tenantId',
    },
  ],
})

export const getUserStore = () => useUserStore(store)
