{"version": 3, "file": "Item.js", "sources": ["../../../../components/cascader/components/Item.tsx"], "sourcesContent": ["import { defineComponent, PropType, computed, ref } from 'vue';\nimport { ChevronRightIcon as TdChevronRightIcon } from 'tdesign-icons-vue-next';\n\nimport { getFullPathLabel, getCascaderItemClass, getCascaderItemIconClass } from '../utils';\n\nimport Checkbox from '../../checkbox/index';\nimport TLoading from '../../loading';\n\nimport { CascaderContextType, TreeNodeValue, TreeNode, TdCascaderProps } from '../types';\nimport { useRipple, useGlobalIcon, usePrefixClass, useCommonClassName } from '@tdesign/shared-hooks';\n\nconst props = {\n  node: {\n    type: Object as PropType<TreeNode>,\n    default() {\n      return {};\n    },\n  },\n  optionChild: {\n    type: [Object, Array] as PropType<TdCascaderProps['option']>,\n  },\n  cascaderContext: {\n    type: Object as PropType<CascaderContextType>,\n  },\n  onChange: Function as PropType<() => void>,\n  onClick: Function as PropType<() => void>,\n  onMouseenter: Function as PropType<() => void>,\n};\n\nexport default defineComponent({\n  name: 'TCascaderItem',\n  props,\n  setup(props) {\n    const liRef = ref<HTMLElement>();\n    useRipple(liRef);\n\n    const COMPONENT_NAME = usePrefixClass('cascader__item');\n    const classPrefix = usePrefixClass();\n    const { ChevronRightIcon } = useGlobalIcon({ ChevronRightIcon: TdChevronRightIcon });\n    const { STATUS, SIZE } = useCommonClassName();\n\n    const itemClass = computed(() => {\n      return getCascaderItemClass(classPrefix.value, props.node, SIZE.value, STATUS.value, props.cascaderContext);\n    });\n\n    const iconClass = computed(() => {\n      return getCascaderItemIconClass(classPrefix.value, props.node, STATUS.value, props.cascaderContext);\n    });\n\n    function RenderLabelInner(node: TreeNode, cascaderContext: CascaderContextType) {\n      const { inputVal } = cascaderContext;\n      const labelText = inputVal ? getFullPathLabel(node) : node.label;\n      if (inputVal) {\n        const texts = labelText.split(inputVal as string);\n        const doms = [];\n        for (let index = 0; index < texts.length; index++) {\n          doms.push(<span key={index}>{texts[index]}</span>);\n          if (index === texts.length - 1) break;\n          doms.push(\n            <span key={`${index}filter`} class={`${COMPONENT_NAME.value}-label--filter`}>\n              {inputVal}\n            </span>,\n          );\n        }\n        return doms;\n      }\n      return labelText;\n    }\n\n    const renderTitle = (node: TreeNode) => {\n      if (typeof node.label === 'string') return node.label;\n\n      return null;\n    };\n\n    function RenderLabelContent(node: TreeNode, cascaderContext: CascaderContextType) {\n      const label = RenderLabelInner(node, cascaderContext);\n\n      const labelCont = (\n        <span\n          title={cascaderContext.inputVal ? getFullPathLabel(node) : renderTitle(node)}\n          class={[`${COMPONENT_NAME.value}-label`, `${COMPONENT_NAME.value}-label--ellipsis`]}\n          role=\"label\"\n        >\n          {label}\n        </span>\n      );\n\n      return labelCont;\n    }\n\n    function RenderCheckBox(node: TreeNode, cascaderContext: CascaderContextType) {\n      const { checkProps, value, max, inputVal } = cascaderContext;\n      const label = RenderLabelInner(node, cascaderContext);\n      return (\n        <Checkbox\n          checked={node.checked}\n          indeterminate={node.indeterminate}\n          disabled={node.isDisabled() || ((value as TreeNodeValue[]).length >= max && max !== 0)}\n          // node.value maybe string or number\n          name={String(node.value)}\n          stopLabelTrigger={!!node.children}\n          title={inputVal ? getFullPathLabel(node) : renderTitle(node)}\n          onChange={() => {\n            props.onChange();\n          }}\n          {...checkProps}\n        >\n          {label}\n        </Checkbox>\n      );\n    }\n\n    return () => {\n      const { cascaderContext, node, optionChild } = props;\n      const isOptionChildAndMultiple = optionChild && cascaderContext.multiple;\n      return (\n        <li\n          ref={liRef}\n          class={itemClass.value}\n          onClick={() => (isOptionChildAndMultiple ? props.onChange() : props.onClick())}\n          onMouseenter={props.onMouseenter}\n        >\n          {optionChild ||\n            (cascaderContext.multiple\n              ? RenderCheckBox(node, cascaderContext)\n              : RenderLabelContent(node, cascaderContext))}\n          {node.children &&\n            (node.loading ? (\n              <TLoading class={iconClass.value} size=\"small\" />\n            ) : (\n              <ChevronRightIcon class={iconClass.value} />\n            ))}\n        </li>\n      );\n    };\n  },\n});\n"], "names": ["_isSlot", "s", "Object", "prototype", "toString", "call", "_isVNode", "props", "node", "type", "default", "option<PERSON><PERSON>d", "Array", "cascaderContext", "onChange", "Function", "onClick", "onMouseenter", "defineComponent", "name", "setup", "liRef", "ref", "useRipple", "COMPONENT_NAME", "usePrefixClass", "classPrefix", "_useGlobalIcon", "useGlobalIcon", "ChevronRightIcon", "TdChevronRightIcon", "_useCommonClassName", "useCommonClassName", "STATUS", "SIZE", "itemClass", "computed", "getCascaderItemClass", "value", "iconClass", "getCascaderItemIconClass", "RenderLabelInner", "inputVal", "labelText", "getFullPathLabel", "label", "texts", "split", "doms", "index", "length", "push", "_createVNode", "concat", "renderTitle", "RenderLabel<PERSON><PERSON>nt", "labelCont", "RenderCheckBox", "checkProps", "max", "Checkbox", "_mergeProps", "checked", "indeterminate", "isDisabled", "String", "children", "_default", "isOptionChildAndMultiple", "multiple", "loading", "TLoading"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS6E,SAAAA,QAAAC,CAAA,EAAA;AAAA,EAAA,OAAA,OAAAA,CAAA,KAAA,UAAA,IAAAC,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAC,IAAA,CAAAJ,CAAA,CAAAK,KAAAA,iBAAAA,IAAAA,CAAAA,WAAA,CAAAL,CAAA,CAAA,CAAA;AAAA,CAAA;AAE7E,IAAMM,KAAQ,GAAA;AACZC,EAAAA,IAAM,EAAA;AACJC,IAAAA,IAAM,EAAAP,MAAA;IAAA,SACNQ,EAAAA,SAAAA,QAAUA,GAAA;AACR,MAAA,OAAO,EAAC,CAAA;AACV,KAAA;GACF;AACAC,EAAAA,WAAa,EAAA;AACXF,IAAAA,IAAA,EAAM,CAACP,MAAA,EAAQU,KAAK,CAAA;GACtB;AACAC,EAAAA,eAAiB,EAAA;AACfJ,IAAAA,IAAM,EAAAP,MAAAA;GACR;AACAY,EAAAA,QAAU,EAAAC,QAAA;AACVC,EAAAA,OAAS,EAAAD,QAAA;AACTE,EAAAA,YAAc,EAAAF,QAAAA;AAChB,CAAA,CAAA;AAEA,WAAeG,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,eAAA;AACNZ,EAAAA,KAAA,EAAAA,KAAA;AACAa,EAAAA,OAAAA,SAAAA,MAAMb,MAAO,EAAA;AACX,IAAA,IAAMc,QAAQC,OAAiB,EAAA,CAAA;IAC/BC,eAAA,CAAUF,KAAK,CAAA,CAAA;AAET,IAAA,IAAAG,cAAA,GAAiBC,uBAAe,gBAAgB,CAAA,CAAA;AACtD,IAAA,IAAMC,cAAcD,sBAAe,EAAA,CAAA;IACnC,IAAAE,cAAA,GAA6BC,sBAAc;AAAEC,QAAAA,gBAAA,EAAkBC,oCAAAA;AAAmB,OAAC,CAAA;MAA3ED,gBAAiB,GAAAF,cAAA,CAAjBE,gBAAiB,CAAA;AACzB,IAAA,IAAAE,mBAAA,GAAyBC,0BAAmB,EAAA;MAApCC,MAAA,GAAAF,mBAAA,CAAAE,MAAA;MAAQC,IAAK,GAAAH,mBAAA,CAALG,IAAK,CAAA;AAEf,IAAA,IAAAC,SAAA,GAAYC,aAAS,YAAM;MACxB,OAAAC,6CAAA,CAAqBX,WAAY,CAAAY,KAAA,EAAO/B,MAAM,CAAAC,IAAA,EAAM0B,KAAKI,KAAO,EAAAL,MAAA,CAAOK,KAAO/B,EAAAA,MAAAA,CAAMM,eAAe,CAAA,CAAA;AAC5G,KAAC,CAAA,CAAA;AAEK,IAAA,IAAA0B,SAAA,GAAYH,aAAS,YAAM;AACxB,MAAA,OAAAI,iDAAA,CAAyBd,YAAYY,KAAO/B,EAAAA,MAAAA,CAAMC,MAAMyB,MAAO,CAAAK,KAAA,EAAO/B,OAAMM,eAAe,CAAA,CAAA;AACpG,KAAC,CAAA,CAAA;AAEQ,IAAA,SAAA4B,gBAAAA,CAAiBjC,MAAgBK,eAAsC,EAAA;AACxE,MAAA,IAAE6B,WAAa7B,eAAA,CAAb6B;MACR,IAAMC,SAAY,GAAAD,QAAA,GAAWE,sCAAiB,CAAApC,IAAI,IAAIA,IAAK,CAAAqC,KAAA,CAAA;AAC3D,MAAA,IAAIH,QAAU,EAAA;AACN,QAAA,IAAAI,KAAA,GAAQH,SAAU,CAAAI,KAAA,CAAML,QAAkB,CAAA,CAAA;QAChD,IAAMM,OAAO,EAAC,CAAA;AACd,QAAA,KAAA,IAASC,KAAQ,GAAA,CAAA,EAAGA,KAAQ,GAAAH,KAAA,CAAMI,QAAQD,KAAS,EAAA,EAAA;UAC5CD,IAAA,CAAAG,IAAA,CAAAC,eAAA,CAAA,MAAA,EAAA;YAAA,KAAgBH,EAAAA,KAAAA;cAAQH,KAAM,CAAAG,KAAA,CAAA,EAAc,CAAA,CAAA;AAC7C,UAAA,IAAAA,KAAA,KAAUH,MAAMI,MAAS,GAAA,CAAA,EAAG,MAAA;UAC3BF,IAAA,CAAAG,IAAA,CAAAC,eAAA,CAAA,MAAA,EAAA;YAAA,KAAAC,EAAAA,EAAAA,CAAAA,MAAA,CACWJ,KAAA,EAAA,QAAA,CAAA;AAAA,YAAA,OAAA,EAAA,EAAA,CAAAI,MAAA,CAAyB7B,cAAA,CAAec,KACnD,EAAA,gBAAA,CAAA;WAAAI,EAAAA,CAAAA,QAAA,EAEL,CAAA,CAAA;AACF,SAAA;AACO,QAAA,OAAAM,IAAA,CAAA;AACT,OAAA;AACO,MAAA,OAAAL,SAAA,CAAA;AACT,KAAA;AAEM,IAAA,IAAAW,WAAA,GAAc,SAAdA,WAAAA,CAAe9C,IAAmB,EAAA;MAClC,IAAA,OAAOA,KAAKqC,KAAU,KAAA,QAAA,EAAU,OAAOrC,IAAK,CAAAqC,KAAA,CAAA;AAEzC,MAAA,OAAA,IAAA,CAAA;KACT,CAAA;AAES,IAAA,SAAAU,kBAAAA,CAAmB/C,MAAgBK,eAAsC,EAAA;AAC1E,MAAA,IAAAgC,KAAA,GAAQJ,gBAAiB,CAAAjC,IAAA,EAAMK,eAAe,CAAA,CAAA;MAE9C,IAAA2C,SAAA,GAAAJ,eAAA,CAAA,MAAA,EAAA;AAAA,QAAA,OAAA,EAEKvC,eAAA,CAAgB6B,WAAWE,sCAAiB,CAAApC,IAAI,CAAI,GAAA8C,WAAA,CAAY9C,IAAI,CAAA;AAAA,QAAA,OAAA,EACpE,CAAA6C,EAAAA,CAAAA,MAAA,CAAI7B,cAAe,CAAAc,KAAA,EAAAe,QAAAA,CAAAA,EAAAA,EAAAA,CAAAA,MAAA,CAAkB7B,cAAA,CAAec,OAC3D,kBAAA,CAAA,CAAA;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,OAAA,EAAA,CAECO,KAAA,CALF,CAAA,CAAA;AASI,MAAA,OAAAW,SAAA,CAAA;AACT,KAAA;AAES,IAAA,SAAAC,cAAAA,CAAejD,MAAgBK,eAAsC,EAAA;AAC5E,MAAA,IAAQ6C,UAAA,GAAqC7C,eAAA,CAArC6C,UAAA;QAAYpB,KAAO,GAAkBzB,eAAA,CAAzByB,KAAO;QAAAqB,GAAA,GAAkB9C,eAAA,CAAlB8C,GAAA;QAAKjB,WAAa7B,eAAA,CAAb6B;AAC1B,MAAA,IAAAG,KAAA,GAAQJ,gBAAiB,CAAAjC,IAAA,EAAMK,eAAe,CAAA,CAAA;AACpD,MAAA,OAAAuC,eAAA,CAAAQ,uBAAA,EAAAC,cAAA,CAAA;QAAA,SAEarD,EAAAA,IAAK,CAAAsD,OAAA;QAAA,eACCtD,EAAAA,IAAK,CAAAuD,aAAA;AAAA,QAAA,UAAA,EACVvD,IAAA,CAAKwD,YAAkB,IAAA1B,KAAA,CAA0BY,MAAU,IAAAS,GAAA,IAAOA,QAAQ,CAEpF;AAAA,QAAA,MAAA,EAAMM,MAAO,CAAAzD,IAAA,CAAK8B,KAAK,CACvB;AAAA,QAAA,kBAAA,EAAkB,CAAC,CAAC9B,KAAK0D,QACzB;QAAA,OAAOxB,EAAAA,QAAW,GAAAE,sCAAA,CAAiBpC,IAAI,CAAI,GAAA8C,WAAA,CAAY9C,IAAI,CAAA;QAAA,UACjD,EAAA,SAAAM,WAAM;UACdP,OAAMO,QAAS,EAAA,CAAA;AACjB,SAAA;AAAA,OAAA,EACI4C,UAEH,CAAA1D,EAAAA,OAAA,CAAA6C,KAAA,IAAAA,KAAA,GAAA;AAAA,QAAA,SAAA,EAAA,SAAAsB,QAAA,GAAA;AAAA,UAAA,OAAA,CAAAtB,KAAA,CAAA,CAAA;AAAA,SAAA;AAAA,OAAA,CAAA,CAAA;AAGP,KAAA;AAEA,IAAA,OAAO,YAAM;AACX,MAAA,IAAQhC,eAAA,GAAuCN,MAAAA,CAAvCM,eAAA;QAAiBL,IAAM,GAAgBD,MAAAA,CAAtBC,IAAM;QAAAG,WAAA,GAAgBJ,MAAAA,CAAhBI,WAAA,CAAA;AACzB,MAAA,IAAAyD,wBAAA,GAA2BzD,eAAeE,eAAgB,CAAAwD,QAAA,CAAA;AAChE,MAAA,OAAAjB,eAAA,CAAA,IAAA,EAAA;AAAA,QAAA,KAAA,EAES/B,KACL;QAAA,OAAOc,EAAAA,UAAUG,KACjB;AAAA,QAAA,SAAA,EAAS,SAAAtB,OAAA,GAAA;AAAA,UAAA,OAAOoD,wBAAA,GAA2B7D,OAAMO,QAAS,EAAA,GAAIP,OAAMS,OAAQ,EAAA,CAAA;AAAA,SAAA;AAAA,QAAA,cAAA,EAC9DT,MAAM,CAAAU,YAAAA;AAAA,OAAA,EAAA,CAEnBN,WAAA,KACEE,gBAAgBwD,QACb,GAAAZ,cAAA,CAAejD,MAAMK,eAAe,CAAA,GACpC0C,kBAAmB,CAAA/C,IAAA,EAAMK,eAAe,CAAA,CAAA,EAC7CL,KAAK0D,QACH,KAAA1D,IAAA,CAAK8D,OACJ,GAAAlB,eAAA,CAAAmB,qBAAA,EAAA;QAAA,OAAiBhC,EAAAA,SAAA,CAAUD,KAAO;AAAA,QAAA,MAAA,EAAA,OAAA;OAAAc,EAAAA,IAAAA,CAAAA,GAAAA,eAAA,CAAAvB,gBAAA,EAAA;AAAA,QAAA,OAAA,EAETU,UAAUD,KAAAA;AAAO,OAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KAIpD,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}