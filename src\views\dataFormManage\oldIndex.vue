<template>
  <div class="ledger-table">
    <!-- <div style="display: flex; gap: 16px">
      <el-card style="flex: 1; min-width: 200px">
        <auto-fit-grid :gap="20" :min-width="200">
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding-top: 10px;
            "
          >
            <div
              style="display: flex; flex-direction: column; align-items: center"
            >
              <span
                style="margin-bottom: 20px; font-weight: 600; font-size: 18px"
              >
                消息台账
              </span>
              <span style="font-weight: 600; font-size: 24px">
                {{ totalNum }}条
              </span>
            </div>
            <svg-icon
              name="ledgerTitle"
              :icon-style="{
                width: '50px',
                height: '50px',
                marginRight: '10px',
              }"
            ></svg-icon>
          </div>
        </auto-fit-grid>
      </el-card>
      <el-card style="flex: 3" class="right">
        <auto-fit-grid :gap="20" :min-width="100">
          <div
            v-for="(item, index) in ledgerCount"
            :key="item.name"
            style="display: flex"
          >
            <div
              style="
                min-width: 100px;
                height: 90px;
                flex: 1;
                align-items: center;
              "
              :class="{
                item1: index === 0,
                item2: index === 1,
                item3: index === 2,
                item4: index === 3,
                item5: index === 4,
              }"
            >
              <div
                style="
                  padding-left: 28px;
                  padding-top: 16px;
                  font-size: 16px;
                  color: #000000;
                "
              >
                <div>{{ item.name }}</div>
                <div style="margin-top: 10px; font-size: 24px">
                  {{ item.label }}
                </div>
              </div>
              <div class="leftIcon"></div>
            </div>
          </div>
        </auto-fit-grid>
      </el-card>
    </div> -->
    <!-- <el-card style="margin-top: 20px">
      <div class="header-title">数据表列表</div>
      <div style="margin: 16px 0">
        <BaseButton
          @handle-click="connectDataForm"
          :name="'生成数据表'"
          :disabled="!selectData.length"
        ></BaseButton>
        <ExportPlain
          style="float: right"
          @handle-click="exportData"
        ></ExportPlain>
        <ImportPlain
          style="float: right"
          @handle-click="importDialogVisible = true"
        ></ImportPlain>
      </div>
      <el-table
        border
        stripe
        ref="myTableRef"
        :data="tableData"
        @selection-change="onSelectChange"
        style="max-width: 1920px"
      >
        <el-table-column type="selection" width="40"></el-table-column>
        <el-table-column
          label="序号"
          type="index"
          align="center"
          width="80"
        ></el-table-column>
        <el-table-column label="表名" min-width="200px">
          <template #header>
            <div>表名</div>
            <div>
              <el-input
                v-model.trim="searchForm.tableDesc"
                maxlength="50"
                @input="() => initData({ ...page, ...searchForm })"
              ></el-input>
            </div>
          </template>

<template #default="{ row }">
            <LinkBase
              :name="row.tableDesc"
              @handle-click="updatedataForm(row)"
            ></LinkBase>
          </template>
</el-table-column>
<el-table-column prop="tableTopic" label="所属主题">
  <template #header>
            <div>所属主题</div>
            <div>
              <el-input
                v-model.trim="searchForm.tableTopic"
                maxlength="50"
                @input="() => initData({ ...page, ...searchForm })"
              ></el-input>
            </div>
          </template>
</el-table-column>

<el-table-column label="用途" min-width="120px">
  <template #header>
            <div>用途</div>
            <div>
              <el-input
                maxlength="50"
                @input="() => initData({ ...page, ...searchForm })"
              ></el-input>
            </div>
          </template>
</el-table-column>

<el-table-column prop="tableType" label="表类型">
  <template #header>
            <div>表类型</div>
            <div>
              <el-select
                v-model="searchForm.tableType"
                placeholder="请选择"
                clearable
                @change="() => initData({ ...page, ...searchForm })"
              >
                <el-option
                  v-for="item in formTypeOptions"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </div>
          </template>

  <template #default="{ row }">
            {{
              formTypeOptions.find((item: any) => item.value === row.tableType)
                ?.label
            }}
          </template>
</el-table-column>
<el-table-column prop="creatorName" label="创建人">
  <template #header>
            <div>创建人</div>
            <div>
              <el-input
                v-model.trim="searchForm.creatorName"
                maxlength="50"
                @input="() => initData({ ...page, ...searchForm })"
              ></el-input>
            </div>
          </template>
</el-table-column>

<el-table-column prop="createTime" label="创建时间" min-width="300px">
  <template #header>
            <div>创建时间</div>
            <div>
              <el-date-picker
                v-model="searchForm.approvalFinishTime"
                @change="handleTimeChange"
                type="daterange"
                range-separator="-"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </div>
          </template>

  <template #default="{ row }">
            {{ dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
</el-table-column>

<el-table-column prop="generatesTableStatus" label="生成实体表状态" min-width="120px">
  <template #header>
            <div>生成实体表状态</div>
            <div>
              <el-select
                v-model="searchForm.generatesTableStatus"
                placeholder="请选择"
                clearable
                @change="() => initData({ ...page, ...searchForm })"
              >
                <el-option
                  v-for="item in entityOptions"
                  :value="item.value"
                  :key="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </div>
          </template>
  <template #default="{ row }">
            <span v-if="row.generatesTableStatus === 0">未生成</span>
            <span v-if="row.generatesTableStatus === 1">已生成</span>
            <span v-if="row.generatesTableStatus === 2">部分生成</span>
          </template>
</el-table-column>
<el-table-column prop="lastUpdaterName" label="更新人" min-width="120px">
  <template #header>
            <div>更新人</div>
            <div>
              <el-input
                v-model.trim="searchForm.lastUpdaterName"
                maxlength="50"
                @input="() => initData({ ...page, ...searchForm })"
              ></el-input>
            </div>
          </template>
</el-table-column>
<el-table-column label="操作" min-width="160px" fixed="right">
  <template #default="{ row }">
            <DeleteLink @handle-click="deleteDataSourceById(row)"></DeleteLink>
          </template>
</el-table-column>
</el-table>
<div class="footer" style="margin-top: 16px">
  <el-pagination v-model:currentPage="page.pageNo" v-model:page-size="page.pageSize" :page-sizes="[5, 10, 15, 20]"
    :disabled="false" :background="false" layout="total, prev, pager, next, sizes" :total="page.totalCount"
    @size-change="handleSizeChange" @current-change="handleCurrentChange" />
</div>
</el-card> -->

    <GeneralLedgerTable
      ref="ledgerTableRef"
      :columns-config="generalLedgerTableColumns2"
      :showExportButton="false"
      :ledgerTableName="'DataTableManagement'"
      :row-key="'id'"
      form-id="10000003"
      @selectionChange="onSelectChange"
    >
      <template #custom-buttons>
        <!-- <BaseButton
          @handle-click="connectDataForm"
          :name="'生成数据表'"
          :disabled="!selectData.length"
        ></BaseButton> -->
        <!-- <ExportPlain
          @handle-click="exportData"
        ></ExportPlain> -->
        <ImportPlain @handle-click="importDialogVisible = true"></ImportPlain>
      </template>

      <template #column-tableDesc="{ row }">
        <LinkBase
          :name="row.tableDesc"
          @handle-click="updatedataForm(row)"
        ></LinkBase>
      </template>
      <template #column-generatesTableStatus="{ row }">
        <span v-if="row.generatesTableStatus === 0">未生成</span>
        <span v-if="row.generatesTableStatus === 1">已生成</span>
        <span v-if="row.generatesTableStatus === 2">部分生成</span>
      </template>

      <template #column-tableType="{ row }">
        {{
          formTypeOptions.find((item: any) => item.value === row.tableType)
            ?.label
        }}
      </template>

      <template #operate="{ row }">
        <DeleteLink @handle-click="deleteDataSourceById(row)"></DeleteLink>
      </template>
    </GeneralLedgerTable>
    <el-dialog title="物理表选择" v-model="dialogVisible">
      <MyTable
        v-bind="physicsTableConfigRef"
        ref="physicsTableRef"
        :page="physicsPage"
        :tableData="physicsTableData"
        :hasBorder="true"
        :list-count="physicsPage.totalCount"
        @update:page="physicsPageInfoChange"
        @select-change="onPhysicsSelectChange"
      ></MyTable>
      <template #footer>
        <BaseButton @handle-click="connectPhysicsForm"></BaseButton>
        <BaseButton
          type="info"
          @handle-click="dialogVisible = false"
          :name="'取消'"
        ></BaseButton>
      </template>
    </el-dialog>
    <el-dialog
      :title="dialogType + '数据表'"
      v-model="dialog"
      :close-on-click-modal="false"
      @close="dialogClose"
    >
      <div class="header-title" style="margin-bottom: 16px">数据表基本信息</div>
      <el-form
        :model="dialogForm"
        ref="dialogFormRef"
        label-width="auto"
        :rules="dialogFormRules"
      >
        <el-row :gutter="32">
          <el-col :span="8">
            <el-form-item label="表名" prop="tableName">
              <el-input
                placeholder="请输入表名称"
                v-model="dialogForm.tableName"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="表类型" prop="tableType">
              <el-select
                placeholder="请选择表类型"
                v-model="dialogForm.tableType"
              >
                <el-option
                  v-for="item in formTypeOptions"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="所属主题" prop="tableTopic">
              <el-input
                placeholder="请输入所属主题"
                v-model="dialogForm.tableTopic"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="32"></el-row>
        <el-row :gutter="32">
          <el-col :span="24">
            <el-form-item label="描述" prop="tableDesc">
              <el-input
                type="textarea"
                maxlength="500"
                placeholder="请输入描述"
                v-model="dialogForm.tableDesc"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-table
          border
          stripe
          ref="myTableRef"
          :data="tableData"
          @selection-change="onSelectChange"
          v-if="false"
          style="max-width: 1920px"
        >
          <el-table-column type="selection" width="40"></el-table-column>
          <el-table-column
            label="序号"
            type="index"
            align="center"
            width="80"
          ></el-table-column>
          <el-table-column
            prop="tableDesc"
            label="别名"
            show-overflow-tooltip
            min-width="220"
          >
            <template #header>
              <div>别名</div>
              <div>
                <el-input
                  v-model.trim="searchForm.tableDesc"
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="topic"
            label="字段名称唯一性"
            show-overflow-tooltip
            min-width="140"
          >
            <template #header>
              <div>字段名称唯一性</div>
              <div>
                <el-input
                  v-model.trim="searchForm.topic"
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop=""
            label="字段名称"
            show-overflow-tooltip
            min-width="140"
          >
            <template #header>
              <div>字段名称</div>
              <div>
                <el-input
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="tableType" label="数据类型" min-width="140">
            <template #header>
              <div>数据类型</div>
              <div>
                <el-select
                  v-model="searchForm.tableType"
                  placeholder="请选择"
                  clearable
                  @change="() => initData({ ...page, ...searchForm })"
                >
                  <el-option
                    v-for="item in formTypeOptions"
                    :value="item.value"
                    :label="item.label"
                  ></el-option>
                </el-select>
              </div>
            </template>

            <template #default="{ row }">
              {{
                formTypeOptions.find(
                  (item: any) => item.value === row.tableType
                )?.label
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="fieldDesc"
            label="字段属性"
            show-overflow-tooltip
            min-width="140"
          >
            <template #header>
              <div>字段属性</div>
              <div>
                <el-input
                  v-model.trim="searchForm.fieldDesc"
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="aliasName"
            label="数据宽度"
            show-overflow-tooltip
            min-width="140"
          >
            <template #header>
              <div>数据宽度</div>
              <div>
                <el-input
                  v-model.trim="searchForm.aliasName"
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="creatorName" label="关联字段" min-width="140">
            <template #header>
              <div>关联字段</div>
              <div>
                <el-input
                  v-model.trim="searchForm.creatorName"
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="creatorName" label="选项值" min-width="140">
            <template #header>
              <div>选项值</div>
              <div>
                <el-input
                  v-model.trim="searchForm.creatorName"
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="creatorName" label="数值单位" min-width="140">
            <template #header>
              <div>数值单位</div>
              <div>
                <el-input
                  v-model.trim="searchForm.creatorName"
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="creatorName"
            label="时序数据时间戳格式"
            min-width="140"
          >
            <template #header>
              <div>数值单位</div>
              <div>
                <el-input
                  v-model.trim="searchForm.creatorName"
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="creatorName" label="储存单位" min-width="140">
            <template #header>
              <div>储存单位</div>
              <div>
                <el-input
                  v-model.trim="searchForm.creatorName"
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="creatorName" label="格式" min-width="140">
            <template #header>
              <div>格式</div>
              <div>
                <el-input
                  v-model.trim="searchForm.creatorName"
                  placeholder="搜索"
                  maxlength="50"
                  @input="() => initData({ ...page, ...searchForm })"
                ></el-input>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="operation"
            label="操作"
            width="200"
            fixed="right"
          >
            <template #default="{ row }"></template>
          </el-table-column>
        </el-table>
        <div class="footer" style="margin-top: 16px" v-if="false">
          <el-pagination
            v-model:currentPage="page.pageNo"
            v-model:page-size="page.pageSize"
            :page-sizes="[5, 10, 15, 20]"
            :disabled="false"
            :background="false"
            layout="total, prev, pager, next, sizes"
            :total="page.totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>

        <div class="header-title" style="margin-bottom: 16px">数据责任主体</div>

        <el-row :gutter="32">
          <el-col :span="8">
            <el-form-item label="数据责任主体-租户" prop="tenantId">
              <el-select
                placeholder="请选择数据责任主体-租户"
                v-model="dialogForm.tenantId"
                @change="tenantChange"
              >
                <el-option
                  v-for="item in tenantList"
                  :value="item.busId"
                  :label="item.name"
                  :key="item.busId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据责任主体-部门" prop="departmentId">
              <el-select
                placeholder="请选择数据责任主体-部门"
                v-model="dialogForm.departmentId"
                :disabled="dialogForm.tenantId ? false : true"
                @change="departmentChange"
              >
                <el-option
                  v-for="item in DepartmentDataList"
                  :value="item.busId"
                  :label="item.departmentName"
                  :key="item.busId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="数据责任主体-责任人" prop="dutyPersonId">
              <el-select
                placeholder="请选择数据责任主体-责任人"
                v-model="dialogForm.dutyPersonId"
                @change="dutyPersonChange"
              >
                <el-option
                  v-for="item in dutyPersonList"
                  :label="item.realName"
                  :value="item.busId"
                  :key="item.busId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="32">
          <el-col :span="8">
            <el-form-item label="数据安全等级" prop="safeLevel">
              <el-select
                placeholder="请选择数据安全等级"
                v-model="dialogForm.safeLevel"
              >
                <el-option
                  v-for="item in dataList"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <BaseButton @handle-click="saveDataForm"></BaseButton>
        <BaseButton
          type="info"
          @handle-click="dialog = false"
          :name="'取消'"
        ></BaseButton>
      </template>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="上传操作"
      v-model="importDialogVisible"
      width="30vw"
      center
    >
      <el-form
        :model="uploadForm"
        :rules="uploadFormRules"
        ref="uploadFormRef"
        label-width="100px"
      >
        <el-form-item label="数据源" prop="datasourceId">
          <div
            style="display: flex; align-items: center; gap: 10px; width: 100%"
          >
            <el-input
              v-model="selectedDatasourceName"
              placeholder="请选择数据源"
              readonly
              style="width: calc(100% - 80px)"
            ></el-input>
            <el-button
              type="primary"
              @click="datasourceDialogVisible = true"
              style="width: 70px"
            >
              选择
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <el-upload
        drag
        action="#"
        :auto-upload="false"
        v-model:file-list="fileList"
        :on-change="uploadFile"
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">点击选择文件，或将文件拖拽到此处</div>
      </el-upload>
      <template #footer>
        <div>
          <el-button type="primary" class="save-button" @click="handleUpload">
            确认上传
          </el-button>
          <el-button
            type="primary"
            class="cancel-button"
            @click="importDialogVisible = false"
          >
            取消
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据源选择弹窗 -->
    <el-dialog
      title="选择数据源"
      v-model="datasourceDialogVisible"
      width="60vw"
      center
    >
      <el-table
        :data="datasourceList"
        stripe
        border
        @selection-change="onDatasourceSelectChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          prop="name"
          label="数据源名称"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="dbtypeName"
          label="数据库类型"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="ipAddress"
          label="IP地址"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="port"
          label="端口"
          min-width="80"
        ></el-table-column>
        <el-table-column
          prop="dbname"
          label="数据库实例名"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="username"
          label="用户名"
          min-width="100"
        ></el-table-column>
        <el-table-column label="状态" min-width="80">
          <template #default="{ row }">
            <span v-if="row.status === 1" style="color: green">正常</span>
            <span v-else style="color: red">异常</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container" style="margin-top: 16px">
        <el-pagination
          v-model:current-page="datasourcePage.pageNo"
          v-model:page-size="datasourcePage.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :disabled="false"
          :background="false"
          layout="total, prev, pager, next, sizes"
          :total="datasourcePage.totalCount"
          @size-change="handleDatasourceSizeChange"
          @current-change="handleDatasourceCurrentChange"
        />
      </div>
      <template #footer>
        <el-button @click="datasourceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelectDatasource"
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref, onActivated, watch } from "vue";
import MyTable from "@/components/base-ui/table/index.vue";
import GeneralLedgerTable from "@/components/generalLedgerTable/index.vue";
import { tableConfig, physicsTableConfig } from "./config/oldTableConfig";
import DeleteLink from "@/components/base-ui/button/deleteLink.vue";
import BaseButton from "@/components/base-ui/button/base.vue";
import { useUserStore } from "@/store";
const userStore = useUserStore();
import {
  queryNewConfigTable,
  saveConfigTable,
  deleteConfigTable,
  getConfigTable,
  updateConfigTable,
  exportConfigTable,
  getDataTable,
  downloadConfigTableTemplate,
  importConfigTableTemplate,
  getDataTableTotal,
  getTenantQueryApi,
  getDepartmentData,
  getTableAndTableRelationInfo,
  getTenantNameDepartmentNameUserListApi,
  getRoleData,
  queryDatasourcePage,
} from "API";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
import { ElLoading, ElMessage } from "element-plus";
const ledgerTableRef = ref(null);
const formTypeOptions = ref<any>([
  { value: 1, label: "基础数据表" },
  { value: 2, label: "业务子表" },
  { value: 5, label: "分析表" },
  { value: 6, label: "规则表" },
]);
const entityOptions = ref<any>([
  { value: 0, label: "未生成" },
  { value: 1, label: "已生成" },
  { value: 2, label: "部分生成" },
]);
const generalLedgerTableColumns2 = ref<any[]>([
  {
    dataTypeStatus: 2,
    prop: "tableDesc",
    fieldName: "tableDesc",
    label: "表名",
    width: "200",
  },
  {
    dataTypeStatus: 2,
    prop: "tableTopic",
    fieldName: "tableTopic",
    label: "所属主题",
    width: "200",
  },
  {
    dataTypeStatus: 4,
    prop: "tableType",
    fieldName: "tableType",
    label: "表类型",
    width: "200",
    formTypeOptions: formTypeOptions.value,
  },

  {
    dataTypeStatus: 2,
    prop: "creatorName",
    fieldName: "creatorName",
    label: "创建人",
    width: "100",
  },
  {
    dataTypeStatus: 3,
    prop: "createTime",
    fieldName: "createTime",
    label: "创建时间",
    width: "200",
  },

  {
    dataTypeStatus: 4,
    prop: "generatesTableStatus",
    fieldName: "generatesTableStatus",
    label: "生成实体表状态",
    width: "200",
    formTypeOptions: entityOptions.value,
  },
  {
    dataTypeStatus: 2,
    prop: "lastUpdaterName",
    fieldName: "lastUpdaterName",
    label: "更新人",
    width: "200",
  },
]);

const dutyPersonList = ref<any>([]);
const router = useRouter();
const dialogType = ref<any>("");
const dialogFormRef = ref<any>();

const dataList = ref<any>([
  { value: "部门公开", label: "部门公开" },
  { value: "企业公开", label: "企业公开" },
  { value: "集团公开", label: "集团公开" },
]);

const tableConfigRef = ref(tableConfig);
const physicsTableConfigRef = ref(physicsTableConfig);
const page = ref({ pageNo: 1, pageSize: 10, totalCount: 0 });
const physicsPage = ref({ pageNo: 1, pageSize: 10, totalCount: 0 });
const tableData = ref([]);
const totalNum = ref(0);
const ledgerCount = ref<any>([
  { name: "今日新增", label: "" },
  { name: "最近三日", label: "" },
  { name: "最近一周", label: "" },
  { name: "最近一月", label: "" },
  { name: "最近一年", label: "" },
]);

const getNumTotol = () => {
  getDataTableTotal().then((res: any) => {
    totalNum.value = res?.total || 0;
    ledgerCount.value[0].label = res?.todayNumber || 0;
    ledgerCount.value[1].label = res?.threeDayNumber || 0;
    ledgerCount.value[2].label = res?.weekNumber || 0;
    ledgerCount.value[3].label = res?.monthNumber || 0;
    ledgerCount.value[4].label = res?.yearNumber || 0;
  });
};
const physicsTableData = ref([]);
const searchForm = ref<any>({});
const myTableRef = ref<any>();
const searchFormRef = ref<any>();
const dialogVisible = ref(false);
const importDialogVisible = ref(false);

const dialogForm = ref<any>({});
const dialog = ref<boolean>(false);

const dialogFormRules = ref<any>({
  tableName: [
    {
      required: true,
      message: "请输入表名称",
      trigger: "blur",
    },
  ],
  tableType: [
    {
      required: true,
      message: "请选择表类型",
      trigger: "change",
    },
  ],
  applicationModule: [
    {
      required: true,
      message: "请输入功能模块",

      trigger: "blur",
    },
  ],
  tableDesc: [
    {
      required: true,
      message: "请输入描述",
      trigger: "blur",
    },
  ],
  dataBaseConfigBusinessId: [
    {
      required: true,
      message: "请选择数据库",
      trigger: "change",
    },
  ],
  tenantId: [
    {
      required: true,
      message: "请选择据责任主体-租户",
      trigger: "blur",
    },
  ],
  departmentId: [
    {
      required: true,
      message: "请选择据责任主体-租户",
      trigger: "blur",
    },
  ],
  dutyPersonId: [
    {
      required: true,
      message: "请选择数据责任主体-责任人",
      trigger: "blur",
    },
  ],
  safeLevel: [
    {
      required: true,
      message: "请选择数据责任主体-责任人",
      trigger: "blur",
    },
  ],
});
const fileList = ref<any>([]);

// 数据源选择相关
const datasourceDialogVisible = ref(false);
const datasourceList = ref<any>([]);
const datasourcePage = ref({ pageNo: 1, pageSize: 10, totalCount: 0 });
const selectedDatasource = ref<any>(null);
const selectedDatasourceName = ref("");
const selectedDatasourceList = ref<any>([]);

// 上传表单相关
const uploadForm = ref<any>({
  datasourceId: "",
});
const uploadFormRef = ref<any>();
const uploadFormRules = ref<any>({
  datasourceId: [
    {
      required: true,
      message: "请选择数据源",
      trigger: "change",
    },
  ],
});

const downloadTemplate = () => {
  const loading = ElLoading.service({ text: "正在下载..." });
  downloadConfigTableTemplate()
    .then((data) => {
      console.log("下载loading");
      const blob = new Blob([data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = "数据表导入模板.xlsx";
      link.click();
      window.URL.revokeObjectURL(url);
      loading.close();
    })
    .catch(() => {
      ElMessage.error("下载失败");
      loading.close();
    });
};
const uploadFile = (file: any) => {
  const fileExtension = file.name.split(".").pop();
  const size = file.size / 1024 / 1024;
  if (fileExtension !== "xlsx" && fileExtension !== "xls") {
    ElMessage.warning("只能上传excel文件");
    fileList.value = fileList.value.slice(0, -1);
    return;
  }
  if (size > 10) {
    ElMessage.warning("文件大小不得超过10M");
    fileList.value = fileList.value.slice(0, -1);
    return;
  }
  setTimeout(() => {
    fileList.value = [file];
  });
};
const handleUpload = async () => {
  // 先验证表单
  const valid = await uploadFormRef.value?.validate();
  if (!valid) {
    return;
  }

  if (fileList.value.length === 0) {
    ElMessage.warning("未选择文件");
    return;
  }

  const formData = new FormData();
  formData.append("file", fileList.value[0].raw);
  formData.append("datasourceId", uploadForm.value.datasourceId);

  importConfigTableTemplate(formData, uploadForm.value.datasourceId)
    .then(() => {
      ElMessage.success("导入成功");
      importDialogVisible.value = false;
      // 重置表单
      uploadFormRef.value?.resetFields();
      selectedDatasourceName.value = "";
      fileList.value = [];
      ledgerTableRef.value?.initData();
    })
    .catch((e) => {
      console.log(e);
    });
};

// 数据源相关方法
const getDatasourceList = async () => {
  try {
    const res = await queryDatasourcePage({
      pageNo: datasourcePage.value.pageNo,
      pageSize: datasourcePage.value.pageSize,
    });
    datasourceList.value = res.data || [];
    datasourcePage.value.totalCount = res.totalCount || 0;
  } catch (error) {
    console.error("获取数据源列表失败:", error);
  }
};

const onDatasourceSelectChange = (selection: any[]) => {
  selectedDatasourceList.value = selection;
};

const confirmSelectDatasource = () => {
  if (selectedDatasourceList.value.length === 0) {
    ElMessage.warning("请选择数据源");
    return;
  }

  const selected = selectedDatasourceList.value[0];
  uploadForm.value.datasourceId = selected.busId;
  selectedDatasourceName.value = selected.name;
  datasourceDialogVisible.value = false;
};

const handleDatasourceSizeChange = (pageSize: number) => {
  datasourcePage.value.pageSize = pageSize;
  getDatasourceList();
};

const handleDatasourceCurrentChange = (pageNo: number) => {
  datasourcePage.value.pageNo = pageNo;
  getDatasourceList();
};
const connectPhysicsForm = async () => {
  let newData = selectData.value.map((item: any) => {
    return item.tableId;
  });
  await getDataTable(newData).then((res: any) => {
    dialogVisible.value = false;
    ElMessage.success("操作成功");

    // myTableRef.value.clearSelection()
  });
  await ledgerTableRef.value?.initData();
  await initData({ ...page.value, ...searchForm.value });
};

const exportData = async () => {
  const loading = ElLoading.service({
    text: "文件下载中。。。",
  });
  try {
    await exportConfigTable().then((res) => {
      if (!res) {
        ElMessage.error("导出失败");
        return;
      }
      downLoadExcel(res, "数据表");
    });
  } catch (error) {
  } finally {
    loading.close();
  }
};

const downLoadExcel = (data: any, name: string) => {
  const url = window.URL.createObjectURL(new Blob([data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", `${name}.xls`); // 替换成你想要的文件名
  document.body.appendChild(link);
  link.click();
};

const saveDataForm = async () => {
  let valid = await dialogFormRef.value?.validate(() => {});
  if (valid) {
    if (dialogType.value === "新增")
      saveConfigTable({ ...dialogForm.value }).then((res: any) => {
        initData({ ...page.value });
        // dialogFormRef.value?.resetFields()
        dialog.value = false;
        ElMessage.success("保存成功");
      });
    else
      updateConfigTable({ ...dialogForm.value }).then((res: any) => {
        initData({ ...page.value });
        // dialogFormRef.value?.resetFields()
        dialog.value = false;
        ElMessage.success("修改成功");
      });
  }
};

const updatedataForm = (row: any) => {
  router.push({
    name: "dataInfor",
    params: {
      tableId: row.tableId,
      id: row.id,
      type: "数据表",
      status: row.generatesTableStatus,
    },
  });
};

const dialogClose = () => {
  dialogFormRef.value?.resetFields();
};
const dutyPersonChange = (val: any) => {
  dutyPersonList.value.forEach((item: any) => {
    if (item.busId === val) {
      dialogForm.value.dutyPersonName = item.realName;
    }
  });
};

const handleTimeChange = (val: any) => {
  if (val.length) {
    searchForm.value.startTime = dayjs(val[0] + "T00:00:00Z").valueOf();
    searchForm.value.endTime = dayjs(val[1] + "T23:59:59Z").valueOf();
  } else {
    searchForm.value.startTime = "";
    searchForm.value.endTime = "";
  }
  initData({ ...searchForm.value, ...page.value });
};

const physicsPageInfoChange = (pageInfo: any) => {
  physicsPage.value = pageInfo;
};

const handlePageInfoChange = (pageInfo: any) => {
  page.value = pageInfo;
  initData(page.value);
};

const connectDataForm = () => {
  physicsTableData.value = selectData.value;
  dialogVisible.value = true;
};
const departmentChange = (val: any) => {
  DepartmentDataList.value.forEach((item: any) => {
    if (item.busId === val) {
      dialogForm.value.departmentName = item.departmentName;
    }
  });

  getDepartmentNameUserList();

  console.log("哈哈哈啊", dialogForm.value.departmentName);
};
const deleteBatchDataSource = (data: any) => {
  deleteConfigTable(data).then(() => {
    ElMessage.success("删除成功");
    ledgerTableRef.value?.initData();
  });
};

const selectData = ref<any>([]);
const physicsSelectData = ref<any>({});

const onPhysicsSelectChange = (val: any) => {
  physicsSelectData.value = val;
};

const onSelectChange = (val: any) => {
  selectData.value = val;
};

const deleteDataSourceById = (row: any) => {
  deleteBatchDataSource(row.id);
};

const goUpdatePage = (row: any) => {
  userStore.setSheetName(row.tableDesc);

  router.push({
    name: "dataFormManageIndex",
    // params: { tableId: row.tableId },
  });
};

const handleClickQuery = async () => {
  initData({ ...searchForm.value, ...page.value });
};

const resetUnitForm = async () => {
  searchFormRef.value.resetFields();
  searchForm.value.startTime = "";
  searchForm.value.endTime = "";
  searchForm.value.approvalFinishTime = [];
  initData(page.value);
};
const handleSizeChange = (pageSize: number) => {
  page.value.pageSize = pageSize;
  initData({ ...page.value, ...searchForm.value });
};
const handleCurrentChange = (pageNo: number) => {
  page.value.pageNo = pageNo;
  initData({ ...page.value, ...searchForm.value });
};
const initData = (data: any = {}) => {
  queryNewConfigTable({ ...data }).then((res: any) => {
    tableData.value = res.data || [];
    page.value.pageNo = res.pageNo || 1;
    page.value.pageSize = res.pageSize || 10;
    page.value.totalCount = res.totalCount || 0;
  });
};

const TenantQuer = reactive({
  pageNo: 1,
  pageSize: 9999999,
  listCount: 0,
});
//获取可看租户
const tenantList = ref<any[]>([]);
const getTenantData = async () => {
  await getTenantQueryApi({ ...TenantQuer }).then((res) => {
    tenantList.value = res.data || [];
  });
};
const DepartmentDataList = ref<any[]>([]);
const RoleDataList = ref<any[]>([]);

const getDepartmentDatas = () => {
  getDepartmentData({
    ...TenantQuer,
    tenantId: dialogForm.value.tenantId,
  }).then((res) => {
    DepartmentDataList.value = res.data || [];
  });
};

const getRolePageData = () => {
  getRoleData({ ...TenantQuer, tenantId: dialogForm.value.tenantId }).then(
    (res) => {
      RoleDataList.value = res.data || [];
    }
  );
};
const getDepartmentNameUserList = () => {
  getTenantNameDepartmentNameUserListApi(
    dialogForm.value.tenantId,
    dialogForm.value.departmentId
  ).then((res) => {
    // dataItemData.value = res
    dutyPersonList.value = res || [];
  });
};
const tenantChange = async (val: any) => {
  tenantList.value.forEach((item: any) => {
    if (item.busId === val) {
      dialogForm.value.tenantCode = item.code;
      dialogForm.value.tenantName = item.name;
    }
  });
  console.log("哈哈哈啊", dialogForm.value);

  await getDepartmentDatas();
  await getRolePageData();
};
// 监听数据源弹窗打开，加载数据源列表
watch(datasourceDialogVisible, (newVal) => {
  if (newVal) {
    getDatasourceList();
  }
});

// 监听上传弹窗关闭，重置表单
watch(importDialogVisible, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时重置表单
    uploadFormRef.value?.resetFields();
    selectedDatasourceName.value = "";
    fileList.value = [];
    uploadForm.value.datasourceId = "";
  }
});

onMounted(async () => {
  await getTenantData();
  // getNumTotol()
  // initData(page.value)
});
onActivated(async () => {
  await getTenantData();
});
</script>
<style lang="scss" scoped>
.ledger-table {
  width: 100%;
  position: relative;
  background-color: rgb(245, 246, 247);

  .right {
    .item1 {
      background: #f4f7ff;
      position: relative;

      .leftIcon {
        width: 8px;
        height: 64px;
        position: absolute;
        top: 9px;
        background-image: url(@/assets/img/todayNum.png);
      }
    }

    .item2 {
      background: #f2fff5;
      position: relative;

      .leftIcon {
        width: 8px;
        height: 64px;
        position: absolute;
        top: 9px;
        background-image: url(@/assets/img/pastThreeDaysNum.png);
      }
    }

    .item3 {
      background: #fff6ee;
      position: relative;

      .leftIcon {
        width: 8px;
        height: 64px;
        position: absolute;
        top: 9px;
        background-image: url(@/assets/img/pastWeekNum.png);
      }
    }

    .item4 {
      background: #eef3ff;
      position: relative;

      .leftIcon {
        width: 8px;
        height: 64px;
        position: absolute;
        top: 9px;
        background-image: url(@/assets/img/pastWeekNum.png);
      }
    }

    .item5 {
      background: #eefeff;
      position: relative;

      .leftIcon {
        width: 8px;
        height: 64px;
        position: absolute;
        top: 9px;
        background-image: url(@/assets/img/pastWeekNum.png);
      }
    }
  }

  .collectTab {
    margin-left: 16px;
    width: 94px;
    height: 24px;
    color: #009a29;
    border: 1px solid #009a29;
    background-color: #e7f9ea;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px 16px;
  }

  .header-title {
    display: flex;
    justify-content: space-between;

    .titile {
      font-size: 20px;
      font-weight: bold;
    }

    .title-button {
      display: flex;

      button {
        margin-left: 10px;
      }
    }
  }

  .selectable {
    width: 100%;
    position: absolute;
    right: 16px;
    gap: 16px;
    display: flex;
    flex-direction: row-reverse;

    > div {
      width: 120px;
      height: 16px;
      padding: 3px 16px;
      background-color: #e2e9fd;
      color: #0e42d2;
      border: 1px solid #0e42d2;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .title-main {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;

      .info {
        display: flex;
      }
    }
  }

  .search {
    display: flex;
    align-items: center;
    gap: 10px;

    .selectable {
      border: 1px solid #ccc;
      padding: 8px;
      cursor: pointer;
    }

    .selected {
      background-color: blue;
      color: white;
    }

    .row-hover-default:hover {
      background-color: blue;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }
}
</style>
