/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var Vue = require('vue');
var _defineProperty = require('@babel/runtime/helpers/defineProperty');
var _slicedToArray = require('@babel/runtime/helpers/slicedToArray');
var dayjs = require('dayjs');
var calendar_props = require('./props.js');
var calendar_utils_index = require('./utils/index.js');
require('@babel/runtime/helpers/toConsumableArray');
require('@babel/runtime/helpers/typeof');
require('../_chunks/dep-086ab407.js');
var index = require('../_chunks/dep-059461d7.js');
var configProvider_hooks_useConfig = require('../config-provider/hooks/useConfig.js');
require('../_chunks/dep-b15ee9eb.js');
var calendar_hooks_useState = require('./hooks/useState.js');
var calendar_hooks_useCalendarClass = require('./hooks/useCalendarClass.js');
var calendar_hooks_useController = require('./hooks/useController.js');
var calendar_hooks_useColHeaders = require('./hooks/useColHeaders.js');
var calendar_consts_index = require('./consts/index.js');
var select_index = require('../select/index.js');
var radio_index = require('../radio/index.js');
var button_index = require('../button/index.js');
var tag_index = require('../tag/index.js');
var calendar_calendarCell = require('./calendar-cell.js');
var isFunction = require('../_chunks/dep-94424a57.js');
var isArray = require('../_chunks/dep-12e0aded.js');
var _baseIteratee = require('../_chunks/dep-156361a6.js');
var omit = require('../_chunks/dep-74372b45.js');
var _isIndex = require('../_chunks/dep-6c0887f7.js');
require('../_chunks/dep-d5654a4e.js');
require('../_chunks/dep-2748f688.js');
require('../_chunks/dep-914897c8.js');
require('../_chunks/dep-febae5f4.js');
require('../_chunks/dep-3f3d49e4.js');
require('../_chunks/dep-6ae67bab.js');
require('../_chunks/dep-ca39ce6d.js');
require('../_chunks/dep-324af0df.js');
require('../_chunks/dep-cabb6240.js');
require('../_chunks/dep-306b2e72.js');
require('../_chunks/dep-76847e8b.js');
require('../_chunks/dep-f8224a9c.js');
require('../_chunks/dep-3bb82c67.js');
require('../_chunks/dep-7c14108f.js');
require('../_chunks/dep-f9e26c60.js');
require('../_chunks/dep-714d992c.js');
require('../_chunks/dep-94ff6543.js');
require('../_chunks/dep-fa8a400f.js');
require('../_chunks/dep-6b54b4a5.js');
require('../_chunks/dep-eac47ca0.js');
require('../_chunks/dep-df581fcb.js');
require('../_chunks/dep-6a71c082.js');
require('../_chunks/dep-ef3df7aa.js');
require('../_chunks/dep-8c0a3845.js');
require('../_chunks/dep-4c75812c.js');
require('../_chunks/dep-ca4c3e97.js');
require('../_chunks/dep-6183bb4a.js');
require('../_chunks/dep-53dbb954.js');
require('../_chunks/dep-d1c7139a.js');
require('../_chunks/dep-ba035735.js');
require('@babel/runtime/helpers/createClass');
require('@babel/runtime/helpers/classCallCheck');
require('../_chunks/dep-e2298443.js');
require('../_chunks/dep-756628ef.js');
require('../select/select.js');
require('@babel/runtime/helpers/objectWithoutProperties');
require('@babel/runtime/helpers/asyncToGenerator');
require('@babel/runtime/regenerator');
require('../common-components/fake-arrow.js');
require('../select-input/index.js');
require('../select-input/select-input.js');
require('../popup/index.js');
require('../popup/popup.js');
require('@popperjs/core');
require('../_chunks/dep-6666de7f.js');
require('../_chunks/dep-076bd726.js');
require('../_chunks/dep-baa72039.js');
require('../popup/container.js');
require('../popup/props.js');
require('../_chunks/dep-3c60e4a0.js');
require('../_chunks/dep-301296be.js');
require('../_chunks/dep-ea9e64d2.js');
require('../_chunks/dep-e3bd0b1f.js');
require('../_chunks/dep-0d72ab91.js');
require('../_chunks/dep-ac7dea19.js');
require('../select-input/props.js');
require('../select-input/hooks/useMultiple.js');
require('../tag-input/index.js');
require('../tag-input/tag-input.js');
require('tdesign-icons-vue-next');
require('../input/index.js');
require('../input/input.js');
require('../input/props.js');
require('../_chunks/dep-b09565a1.js');
require('../_chunks/dep-cbaee605.js');
require('../_chunks/dep-7e5fc00e.js');
require('../input/hooks/useInput.js');
require('../form/consts/index.js');
require('../input/hooks/useLengthLimit.js');
require('../_chunks/dep-1e028e1c.js');
require('../_chunks/dep-fd4183e8.js');
require('../_chunks/dep-2105b21f.js');
require('../_chunks/dep-f20572a3.js');
require('../_chunks/dep-8c00290f.js');
require('../input/hooks/useInputEventHandler.js');
require('../input/hooks/useInputWidth.js');
require('../input/input-group.js');
require('../input/input-group-props.js');
require('../tag-input/props.js');
require('../_chunks/dep-1189d7e7.js');
require('../tag-input/hooks/useDragSorter.js');
require('../tag-input/hooks/useHover.js');
require('../tag-input/hooks/useTagScroll.js');
require('../tag-input/hooks/useTagList.js');
require('../tag/tag.js');
require('tinycolor2');
require('../tag/props.js');
require('../tag/check-tag.js');
require('../tag/check-tag-props.js');
require('../_chunks/dep-01642c17.js');
require('../tag/check-tag-group.js');
require('../tag/check-tag-group-props.js');
require('../loading/index.js');
require('../_chunks/dep-24c31f97.js');
require('../loading/plugin.js');
require('../_chunks/dep-faade8bd.js');
require('../loading/icon/gradient.js');
require('../loading/props.js');
require('../_chunks/dep-605cf2e8.js');
require('../_chunks/dep-925e8207.js');
require('../_chunks/dep-59fa52ec.js');
require('../_chunks/dep-66442631.js');
require('../_chunks/dep-6d27c874.js');
require('../_chunks/dep-31eb9b48.js');
require('../_chunks/dep-ce7457dd.js');
require('../_chunks/dep-4023d837.js');
require('../select-input/hooks/useOverlayInnerStyle.js');
require('../select-input/hooks/useSingle.js');
require('../_chunks/dep-f5374f9a.js');
require('../_chunks/dep-50dbb763.js');
require('../_chunks/dep-4fe7c57c.js');
require('../select/components/select-panel.js');
require('../select/option.js');
require('../select/option-props.js');
require('../checkbox/index.js');
require('../checkbox/checkbox.js');
require('../checkbox/props.js');
require('../_chunks/dep-988704d0.js');
require('../checkbox/consts/index.js');
require('../checkbox/hooks/useCheckboxLazyLoad.js');
require('../_chunks/dep-c9fd0e5d.js');
require('../checkbox/hooks/useKeyboardEvent.js');
require('../checkbox/group.js');
require('../checkbox/checkbox-group-props.js');
require('../_chunks/dep-872d0152.js');
require('../_chunks/dep-cbee8f46.js');
require('../_chunks/dep-a271f384.js');
require('../select/utils/index.js');
require('../select/consts/index.js');
require('../select/option-group.js');
require('../select/option-group-props.js');
require('../select/props.js');
require('../select/hooks/usePanelVirtualScroll.js');
require('../select/hooks/useKeyboardControl.js');
require('../select/hooks/useSelectOptions.js');
require('../_chunks/dep-013e62e8.js');
require('../_chunks/dep-b2fa5076.js');
require('../radio/radio.js');
require('../radio/props.js');
require('../radio/consts/index.js');
require('../radio/group.js');
require('../radio/radio-group-props.js');
require('../radio/radio-button.js');
require('../radio/hooks/useKeyboard.js');
require('../watermark/hooks/index.js');
require('../_chunks/dep-d169db90.js');
require('../_chunks/dep-a4f1ef30.js');
require('../button/button.js');
require('../button/props.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _defineProperty__default = /*#__PURE__*/_interopDefaultLegacy(_defineProperty);
var _slicedToArray__default = /*#__PURE__*/_interopDefaultLegacy(_slicedToArray);
var dayjs__default = /*#__PURE__*/_interopDefaultLegacy(dayjs);

/** Used for built-in method references. */
var arrayProto = Array.prototype;

/** Built-in value references. */
var splice = arrayProto.splice;

/**
 * The base implementation of `_.pullAt` without support for individual
 * indexes or capturing the removed elements.
 *
 * @private
 * @param {Array} array The array to modify.
 * @param {number[]} indexes The indexes of elements to remove.
 * @returns {Array} Returns `array`.
 */
function basePullAt(array, indexes) {
  var length = array ? indexes.length : 0,
    lastIndex = length - 1;
  while (length--) {
    var index = indexes[length];
    if (length == lastIndex || index !== previous) {
      var previous = index;
      if (_isIndex.isIndex(index)) {
        splice.call(array, index, 1);
      } else {
        omit.baseUnset(array, index);
      }
    }
  }
  return array;
}

/**
 * Removes all elements from `array` that `predicate` returns truthy for
 * and returns an array of the removed elements. The predicate is invoked
 * with three arguments: (value, index, array).
 *
 * **Note:** Unlike `_.filter`, this method mutates `array`. Use `_.pull`
 * to pull elements from an array by value.
 *
 * @static
 * @memberOf _
 * @since 2.0.0
 * @category Array
 * @param {Array} array The array to modify.
 * @param {Function} [predicate=_.identity] The function invoked per iteration.
 * @returns {Array} Returns the new array of removed elements.
 * @example
 *
 * var array = [1, 2, 3, 4];
 * var evens = _.remove(array, function(n) {
 *   return n % 2 == 0;
 * });
 *
 * console.log(array);
 * // => [1, 3]
 *
 * console.log(evens);
 * // => [2, 4]
 */
function remove(array, predicate) {
  var result = [];
  if (!(array && array.length)) {
    return result;
  }
  var index = -1,
    indexes = [],
    length = array.length;
  predicate = _baseIteratee.baseIteratee(predicate);
  while (++index < length) {
    var value = array[index];
    if (predicate(value, index, array)) {
      result.push(value);
      indexes.push(index);
    }
  }
  basePullAt(array, indexes);
  return result;
}

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty__default["default"](e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _isSlot(s) {
  return typeof s === 'function' || Object.prototype.toString.call(s) === '[object Object]' && !Vue.isVNode(s);
}
var _Calendar = Vue.defineComponent({
  name: "TCalendar",
  props: calendar_props["default"],
  setup: function setup(props2, _ref) {
    var slots = _ref.slots;
    var renderContent = index.useContent();
    var _useConfig = configProvider_hooks_useConfig.useConfig(calendar_consts_index.COMPONENT_NAME),
      t = _useConfig.t,
      globalConfig = _useConfig.globalConfig;
    var _useState = calendar_hooks_useState.useState(props2),
      state = _useState.state,
      toToday = _useState.toToday,
      checkDayVisible = _useState.checkDayVisible;
    var cls = calendar_hooks_useCalendarClass.useCalendarClass(props2, state);
    var _useColHeaders = calendar_hooks_useColHeaders.useColHeaders(props2, state),
      cellColHeaders = _useColHeaders.cellColHeaders;
    var controller = calendar_hooks_useController.userController(props2, state);
    var rangeFromTo = Vue.computed(function () {
      if (!props2.range || props2.range.length < 2) {
        return null;
      }
      var _props2$range = _slicedToArray__default["default"](props2.range, 2),
        v1 = _props2$range[0],
        v2 = _props2$range[1];
      if (dayjs__default["default"](v1).isBefore(dayjs__default["default"](v2))) {
        return {
          from: v1,
          to: v2
        };
      }
      return {
        from: v2,
        to: v1
      };
    });
    function checkMonthAndYearSelectedDisabled(year, month) {
      var disabled = false;
      if (rangeFromTo.value && rangeFromTo.value.from && rangeFromTo.value.to) {
        var beginYear = dayjs__default["default"](rangeFromTo.value.from).year();
        var endYear = dayjs__default["default"](rangeFromTo.value.to).year();
        if (year === beginYear) {
          var beginMon = parseInt(dayjs__default["default"](rangeFromTo.value.from).format("M"), 10);
          disabled = month < beginMon;
        } else if (year === endYear) {
          var endMon = parseInt(dayjs__default["default"](rangeFromTo.value.to).format("M"), 10);
          disabled = month > endMon;
        }
      }
      return disabled;
    }
    function adjustMonth() {
      var _rangeFromTo$value, _rangeFromTo$value2;
      if ((_rangeFromTo$value = rangeFromTo.value) !== null && _rangeFromTo$value !== void 0 && _rangeFromTo$value.from && (_rangeFromTo$value2 = rangeFromTo.value) !== null && _rangeFromTo$value2 !== void 0 && _rangeFromTo$value2.to) {
        var beginYear = dayjs__default["default"](rangeFromTo.value.from).year();
        var endYear = dayjs__default["default"](rangeFromTo.value.to).year();
        var beginMon = parseInt(dayjs__default["default"](rangeFromTo.value.from).format("M"), 10);
        if (checkMonthAndYearSelectedDisabled(state.curSelectedYear, state.curSelectedMonth)) {
          state.curSelectedMonth = state.curSelectedYear === beginYear ? beginMon : state.curSelectedYear === endYear ? 1 : state.curSelectedMonth;
        }
      }
    }
    Vue.watch(function () {
      return {
        year: "".concat(state.curSelectedYear),
        month: "".concat(state.curSelectedMonth)
      };
    }, function (v) {
      isFunction.isFunction(props2.onMonthChange) && props2.onMonthChange(_objectSpread({}, v));
      controller.emitControllerChange();
    });
    var dateSelect = {
      yearSelectOptionList: Vue.computed(function () {
        var re = [];
        var begin = state.curSelectedYear - 10;
        var end = state.curSelectedYear + 10;
        if (rangeFromTo.value && rangeFromTo.value.from && rangeFromTo.value.to) {
          begin = dayjs__default["default"](rangeFromTo.value.from).year();
          end = dayjs__default["default"](rangeFromTo.value.to).year();
        }
        if (begin < calendar_consts_index.MIN_YEAR) {
          begin = calendar_consts_index.MIN_YEAR;
        }
        if (end < calendar_consts_index.MIN_YEAR) {
          end = calendar_consts_index.MIN_YEAR;
        }
        for (var i = begin; i <= end; i++) {
          re.push({
            value: i,
            label: t(globalConfig.value.yearSelection, {
              year: i
            }),
            disabled: false
          });
        }
        return re;
      }),
      isYearSelectVisible: Vue.computed(function () {
        return controller.checkControllerVisible("year");
      }),
      isYearSelectDisabled: Vue.computed(function () {
        return controller.checkControllerDisabled("year", "selectProps");
      }),
      monthSelectOptionList: Vue.computed(function () {
        adjustMonth();
        var re = [];
        for (var i = calendar_consts_index.FIRST_MONTH_OF_YEAR; i <= calendar_consts_index.LAST_MONTH_OF_YEAR; i++) {
          var disabled = checkMonthAndYearSelectedDisabled(state.curSelectedYear, i);
          re.push({
            value: i,
            label: t(globalConfig.value.monthSelection, {
              month: i
            }),
            disabled: disabled
          });
        }
        return re;
      }),
      isMonthSelectVisible: Vue.computed(function () {
        return state.curSelectedMode === "month" && controller.checkControllerVisible("month");
      }),
      isMonthSelectDisabled: Vue.computed(function () {
        return controller.checkControllerDisabled("month", "selectProps");
      })
    };
    var modeSelect = {
      optionList: Vue.computed(function () {
        return [{
          value: "month",
          label: t(globalConfig.value.monthRadio)
        }, {
          value: "year",
          label: t(globalConfig.value.yearRadio)
        }];
      }),
      isVisible: Vue.computed(function () {
        return controller.checkControllerVisible("mode");
      }),
      isDisabled: Vue.computed(function () {
        return controller.checkControllerDisabled("mode", "radioGroupProps");
      })
    };
    var weekendBtn = {
      text: Vue.computed(function () {
        return state.isShowWeekend ? t(globalConfig.value.hideWeekend) : t(globalConfig.value.showWeekend);
      }),
      vBind: Vue.computed(function () {
        var c = controller.configData.value.weekend;
        return state.isShowWeekend ? c.hideWeekendButtonProps : c.showWeekendButtonProps;
      }),
      isVisible: Vue.computed(function () {
        return props2.theme === "full" && controller.checkControllerVisible("current") && controller.checkControllerVisible("weekend");
      }),
      isDisabled: Vue.computed(function () {
        var p = state.isShowWeekend ? "hideWeekendButtonProps" : "showWeekendButtonProps";
        return controller.checkControllerDisabled("weekend", p);
      })
    };
    var currentBtn = {
      text: Vue.computed(function () {
        return state.curSelectedMode === "month" ? t(globalConfig.value.today) : t(globalConfig.value.thisMonth);
      }),
      vBind: Vue.computed(function () {
        var c = controller.configData.value.current;
        return state.curSelectedMode === "month" ? c.currentDayButtonProps : c.currentMonthButtonProps;
      }),
      isVisible: Vue.computed(function () {
        return props2.theme === "full" && controller.checkControllerVisible("current");
      }),
      isDisabled: Vue.computed(function () {
        var p = state.curSelectedMode === "month" ? "currentDayButtonProps" : "currentMonthButtonProps";
        return controller.checkControllerDisabled("current", p);
      })
    };
    var renderControl = function renderControl() {
      var _slot;
      return Vue.createVNode("div", {
        "class": cls.control.value
      }, [Vue.createVNode("div", {
        "class": cls.title.value
      }, [renderContent("head", void 0, {
        params: _objectSpread({}, controller.options.value)
      })]), Vue.createVNode("div", {
        "class": cls.controlSection.value
      }, [dateSelect.isYearSelectVisible.value && Vue.createVNode("div", {
        "class": cls.controlSectionCell.value
      }, [Vue.createVNode(select_index.Select, Vue.mergeProps({
        "modelValue": state.curSelectedYear,
        "onUpdate:modelValue": function onUpdateModelValue($event) {
          return state.curSelectedYear = $event;
        },
        "size": state.controlSize,
        "autoWidth": true
      }, controller.configData.value.year.selectProps, {
        "disabled": dateSelect.isYearSelectDisabled.value,
        "options": dateSelect.yearSelectOptionList.value
      }), null)]), dateSelect.isMonthSelectVisible.value && Vue.createVNode("div", {
        "class": cls.controlSectionCell.value
      }, [Vue.createVNode(select_index.Select, Vue.mergeProps({
        "autoWidth": true,
        "modelValue": state.curSelectedMonth,
        "onUpdate:modelValue": function onUpdateModelValue($event) {
          return state.curSelectedMonth = $event;
        },
        "size": state.controlSize
      }, controller.configData.value.month.selectProps, {
        "disabled": dateSelect.isMonthSelectDisabled.value,
        "options": dateSelect.monthSelectOptionList.value
      }), null)]), modeSelect.isVisible.value && Vue.createVNode("div", {
        "class": cls.controlSectionCell.value,
        "style": "height: auto"
      }, [Vue.createVNode(radio_index.RadioGroup, Vue.mergeProps({
        "modelValue": state.curSelectedMode,
        "onUpdate:modelValue": function onUpdateModelValue($event) {
          return state.curSelectedMode = $event;
        },
        "variant": "default-filled",
        "size": state.controlSize
      }, controller.configData.value.mode.radioGroupProps, {
        "disabled": modeSelect.isDisabled.value,
        "onChange": controller.emitControllerChange
      }), _isSlot(_slot = modeSelect.optionList.value.map(function (item) {
        return Vue.createVNode(radio_index.RadioButton, {
          "key": item.value,
          "value": item.value
        }, {
          "default": function _default() {
            return [item.label];
          }
        });
      })) ? _slot : {
        "default": function _default() {
          return [_slot];
        }
      })]), weekendBtn.isVisible.value && Vue.createVNode("div", {
        "class": cls.controlSectionCell.value
      }, [Vue.createVNode(tag_index.CheckTag, Vue.mergeProps({
        "class": cls.controlTag.value,
        "theme": state.isShowWeekend ? "default" : "primary",
        "size": "large"
      }, weekendBtn.vBind.value, {
        "disabled": weekendBtn.isDisabled.value,
        "onClick": function onClick() {
          state.isShowWeekend = !state.isShowWeekend;
          controller.emitControllerChange();
        }
      }), {
        "default": function _default() {
          return [weekendBtn.text.value];
        }
      })]), currentBtn.isVisible.value && Vue.createVNode("div", {
        "class": cls.controlSectionCell.value
      }, [Vue.createVNode(button_index.Button, Vue.mergeProps({
        "size": state.controlSize
      }, currentBtn.vBind.value, {
        "disabled": currentBtn.isDisabled.value,
        "onClick": function onClick() {
          toToday();
        }
      }), {
        "default": function _default() {
          return [currentBtn.text.value];
        }
      })])])]);
    };
    var cellClickEmit = function cellClickEmit(eventPropsName, e, cellData) {
      if (isFunction.isFunction(props2[eventPropsName])) {
        var options = {
          cell: _objectSpread(_objectSpread({}, cellData), controller.options.value),
          e: e
        };
        props2[eventPropsName](options);
      }
    };
    var clickCell = function clickCell(e, cellData) {
      var d = dayjs__default["default"](cellData.date);
      if (props2.multiple) {
        if (state.curDateList.find(function (item) {
          return item.isSame(d);
        })) {
          state.curDateList = remove(state.curDateList, function (item) {
            return !item.isSame(d);
          });
        } else {
          state.curDateList.push(d);
        }
      } else {
        state.curDate = d;
      }
      cellClickEmit("onCellClick", e, cellData);
    };
    var doubleClickCell = function doubleClickCell(e, cellData) {
      cellClickEmit("onCellDoubleClick", e, cellData);
    };
    var rightClickCell = function rightClickCell(e, cellData) {
      if (props2.preventCellContextmenu) {
        e.preventDefault();
      }
      cellClickEmit("onCellRightClick", e, cellData);
    };
    var monthCellsData = Vue.computed(function () {
      var daysArr = calendar_utils_index.createMonthCellsData(props2, state);
      return daysArr;
    });
    var renderMonthBody = function renderMonthBody() {
      return Vue.createVNode("table", {
        "class": cls.table.value
      }, [Vue.createVNode("thead", {
        "class": cls.tableHead.value
      }, [Vue.createVNode("tr", {
        "class": cls.tableHeadRow.value
      }, [cellColHeaders.value.map(function (item, index) {
        return checkDayVisible(item.num) && Vue.createVNode("th", {
          "class": cls.tableHeadCell.value
        }, [isArray.isArray(props2.week) ? props2.week[index] : renderContent("week", void 0, {
          defaultNode: Vue.createVNode("span", null, [item.display]),
          params: {
            day: item.num
          }
        })]);
      })])]), Vue.createVNode("tbody", {
        "class": cls.tableBody.value
      }, [monthCellsData.value.map(function (week, weekIndex) {
        return Vue.createVNode("tr", {
          "class": cls.tableBodyRow.value
        }, [week.map(function (item, itemIndex) {
          return (state.isShowWeekend || item.day < 6) && Vue.createVNode(calendar_calendarCell["default"], {
            "key": "d-".concat(weekIndex, "-").concat(itemIndex),
            "item": item,
            "theme": props2.theme,
            "t": t,
            "global": globalConfig.value,
            "cell": props2.cell,
            "cellAppend": props2.cellAppend,
            "fillWithZero": props2.fillWithZero,
            "onClick": function onClick(e) {
              return clickCell(e, item);
            },
            "onDblclick": function onDblclick(e) {
              return doubleClickCell(e, item);
            },
            "onRightclick": function onRightclick(e) {
              return rightClickCell(e, item);
            }
          }, _objectSpread({}, slots));
        })]);
      })])]);
    };
    var yearCellsData = Vue.computed(function () {
      var re = [];
      var monthsArr = calendar_utils_index.createYearCellsData(props2, state);
      var rowCount = Math.ceil(monthsArr.length / calendar_consts_index.DEFAULT_YEAR_CELL_NUMINROW);
      var index = 0;
      for (var i = 1; i <= rowCount; i++) {
        var row = [];
        for (var j = 1; j <= calendar_consts_index.DEFAULT_YEAR_CELL_NUMINROW; j++) {
          row.push(monthsArr[index]);
          index += 1;
        }
        re.push(row);
      }
      return re;
    });
    var renderYearBody = function renderYearBody() {
      return Vue.createVNode("table", {
        "class": cls.table.value
      }, [Vue.createVNode("tbody", {
        "class": cls.tableBody.value
      }, [yearCellsData.value.map(function (cell, cellIndex) {
        return Vue.createVNode("tr", {
          "class": cls.tableBodyRow.value
        }, [cell.map(function (item, itemIndex) {
          return Vue.createVNode(calendar_calendarCell["default"], {
            "key": "m-".concat(cellIndex, "-").concat(itemIndex),
            "item": item,
            "theme": props2.theme,
            "t": t,
            "global": globalConfig.value,
            "cell": props2.cell,
            "cellAppend": props2.cellAppend,
            "fillWithZero": props2.fillWithZero,
            "onClick": function onClick(e) {
              return clickCell(e, item);
            },
            "onDblclick": function onDblclick(e) {
              return doubleClickCell(e, item);
            },
            "onRightclick": function onRightclick(e) {
              return rightClickCell(e, item);
            }
          }, _objectSpread({}, slots));
        })]);
      })])]);
    };
    return function () {
      return Vue.createVNode("div", {
        "class": cls.body.value
      }, [controller.visible.value && renderControl(), Vue.createVNode("div", {
        "class": cls.panel.value
      }, [state.curSelectedMode === "month" ? renderMonthBody() : renderYearBody()])]);
    };
  }
});

exports["default"] = _Calendar;
//# sourceMappingURL=calendar.js.map
