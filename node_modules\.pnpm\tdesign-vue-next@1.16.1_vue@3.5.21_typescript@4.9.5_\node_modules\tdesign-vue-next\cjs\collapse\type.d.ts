import { TNode } from '../common';
export interface TdCollapseProps {
    borderless?: boolean;
    defaultExpandAll?: boolean;
    disabled?: boolean;
    expandIcon?: boolean | TNode;
    expandIconPlacement?: 'left' | 'right';
    expandMutex?: boolean;
    expandOnRowClick?: boolean;
    value?: CollapseValue;
    defaultValue?: CollapseValue;
    modelValue?: CollapseValue;
    onChange?: (value: CollapseValue) => void;
}
export interface TdCollapsePanelProps {
    content?: string | TNode;
    default?: string | TNode;
    destroyOnCollapse?: boolean;
    disabled?: boolean;
    expandIcon?: boolean | TNode;
    header?: string | TNode;
    headerRightContent?: string | TNode;
    value?: string | number;
}
export type CollapseValue = Array<string | number>;
export type CollapsePanelValue = string | number;
