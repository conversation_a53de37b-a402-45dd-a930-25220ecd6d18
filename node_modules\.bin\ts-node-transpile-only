#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/ts-node@10.9.2_@types+node@18.19.124_typescript@4.9.5/node_modules/ts-node/dist/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/ts-node@10.9.2_@types+node@18.19.124_typescript@4.9.5/node_modules/ts-node/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/ts-node@10.9.2_@types+node@18.19.124_typescript@4.9.5/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/ts-node@10.9.2_@types+node@18.19.124_typescript@4.9.5/node_modules/ts-node/dist/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/ts-node@10.9.2_@types+node@18.19.124_typescript@4.9.5/node_modules/ts-node/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/ts-node@10.9.2_@types+node@18.19.124_typescript@4.9.5/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-node/dist/bin-transpile.js" "$@"
else
  exec node  "$basedir/../ts-node/dist/bin-transpile.js" "$@"
fi
