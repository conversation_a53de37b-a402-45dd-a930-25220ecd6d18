#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vite@4.5.14_@types+node@18.19.124_less@4.4.1_sass@1.92.0_stylus@0.57.0_terser@5.44.0/node_modules/vite/bin/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vite@4.5.14_@types+node@18.19.124_less@4.4.1_sass@1.92.0_stylus@0.57.0_terser@5.44.0/node_modules/vite/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vite@4.5.14_@types+node@18.19.124_less@4.4.1_sass@1.92.0_stylus@0.57.0_terser@5.44.0/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vite@4.5.14_@types+node@18.19.124_less@4.4.1_sass@1.92.0_stylus@0.57.0_terser@5.44.0/node_modules/vite/bin/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vite@4.5.14_@types+node@18.19.124_less@4.4.1_sass@1.92.0_stylus@0.57.0_terser@5.44.0/node_modules/vite/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/vite@4.5.14_@types+node@18.19.124_less@4.4.1_sass@1.92.0_stylus@0.57.0_terser@5.44.0/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../vite/bin/vite.js" "$@"
fi
