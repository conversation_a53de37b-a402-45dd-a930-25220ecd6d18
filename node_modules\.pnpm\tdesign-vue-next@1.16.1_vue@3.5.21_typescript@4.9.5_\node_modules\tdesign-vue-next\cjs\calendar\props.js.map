{"version": 3, "file": "props.js", "sources": ["../../../components/calendar/props.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * 该文件为脚本自动生成文件，请勿随意修改。如需修改请联系 PMC\n * */\n\nimport { TdCalendarProps } from './type';\nimport { PropType } from 'vue';\n\nexport default {\n  /** 单元格插槽 */\n  cell: {\n    type: [String, Function] as PropType<TdCalendarProps['cell']>,\n  },\n  /** 单元格插槽，在原来的内容之后追加 */\n  cellAppend: {\n    type: [String, Function] as PropType<TdCalendarProps['cellAppend']>,\n  },\n  /** 右上角控制器配置。支持全局配置。值为 false 则表示不显示控制器，值为 true 则显示控制器默认配置，值类型为 CalendarController 则显示为自定义控制器配置 */\n  controllerConfig: {\n    type: [Boolean, Object] as PropType<TdCalendarProps['controllerConfig']>,\n    default: undefined as TdCalendarProps['controllerConfig'],\n  },\n  /** 小于 10 的日期，是否使用 '0' 填充。支持全局配置。默认表现为 `01` `02`，值为 false 表现为 `1` `2` `9` */\n  fillWithZero: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** 第一天从星期几开始，仅在日历展示维度为月份时（mode = month）有效。默认为 1 */\n  firstDayOfWeek: {\n    type: Number,\n    validator(val: TdCalendarProps['firstDayOfWeek']): boolean {\n      if (!val) return true;\n      return [1, 2, 3, 4, 5, 6, 7].includes(val);\n    },\n  },\n  /** 用于格式化日期，决定事件参数 formattedFilterDate 的输出值。[详细文档](https://day.js.org/docs/en/display/format) */\n  format: {\n    type: String,\n    default: 'YYYY-MM-DD',\n  },\n  /** 头部插槽（左上角处，默认不显示任何内容） */\n  head: {\n    type: [String, Function] as PropType<TdCalendarProps['head']>,\n  },\n  /** 默认是否显示周末 */\n  isShowWeekendDefault: {\n    type: Boolean,\n    default: true,\n  },\n  /** 日历展示维度 */\n  mode: {\n    type: String as PropType<TdCalendarProps['mode']>,\n    default: 'month' as TdCalendarProps['mode'],\n    validator(val: TdCalendarProps['mode']): boolean {\n      if (!val) return true;\n      return ['month', 'year'].includes(val);\n    },\n  },\n  /** 控制当前面板展示月份，优先级高于 `controllerConfig.month` */\n  month: {\n    type: [String, Number] as PropType<TdCalendarProps['month']>,\n  },\n  /** 是否高亮多个日期单元格 */\n  multiple: Boolean,\n  /** 是否禁用单元格右键默认系统菜单 */\n  preventCellContextmenu: Boolean,\n  /** 用于设置日历的年月份显示范围，[范围开始，范围结束] */\n  range: {\n    type: Array as PropType<TdCalendarProps['range']>,\n  },\n  /** 日历风格 */\n  theme: {\n    type: String as PropType<TdCalendarProps['theme']>,\n    default: 'full' as TdCalendarProps['theme'],\n    validator(val: TdCalendarProps['theme']): boolean {\n      if (!val) return true;\n      return ['full', 'card'].includes(val);\n    },\n  },\n  /** 当前高亮的日期 */\n  value: {\n    type: [String, Array, Date] as PropType<TdCalendarProps['value']>,\n  },\n  /** 用于自定义日历星期呈现方式。CalendarWeek.day 表示当前是星期几。示例一：['周一', '周二', '周三', '周四', '周五', '星期六', '星期天']。示例二：`({ day }) => '周' + day` */\n  week: {\n    type: [Array, Function] as PropType<TdCalendarProps['week']>,\n  },\n  /** 控制当前面板展示年份，优先级高于 `controllerConfig.year` */\n  year: {\n    type: [String, Number] as PropType<TdCalendarProps['year']>,\n  },\n  /** 日历单元格点击时触发 */\n  onCellClick: Function as PropType<TdCalendarProps['onCellClick']>,\n  /** 日历单元格双击时触发 */\n  onCellDoubleClick: Function as PropType<TdCalendarProps['onCellDoubleClick']>,\n  /** 日历单元格右击时触发 */\n  onCellRightClick: Function as PropType<TdCalendarProps['onCellRightClick']>,\n  /** 右上角控件组选中值有变化的时候触发 */\n  onControllerChange: Function as PropType<TdCalendarProps['onControllerChange']>,\n  /** 月份切换时触发 */\n  onMonthChange: Function as PropType<TdCalendarProps['onMonthChange']>,\n};\n"], "names": ["cell", "type", "String", "Function", "cellAppend", "controllerConfig", "Boolean", "Object", "fillWithZero", "firstDayOfWeek", "Number", "validator", "val", "includes", "format", "head", "isShowWeekendDefault", "mode", "month", "multiple", "preventCellContextmenu", "range", "Array", "theme", "value", "Date", "week", "year", "onCellClick", "onCellDoubleClick", "onCellRightClick", "onControllerChange", "onMonthChange"], "mappings": ";;;;;;;;;;AASA,YAAe;AAEbA,EAAAA,IAAM,EAAA;AACJC,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAC,EAAAA,UAAY,EAAA;AACVH,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAE,EAAAA,gBAAkB,EAAA;AAChBJ,IAAAA,IAAA,EAAM,CAACK,OAAA,EAASC,MAAM,CAAA;AACtB,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAC,EAAAA,YAAc,EAAA;AACZP,IAAAA,IAAM,EAAAK,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAG,EAAAA,cAAgB,EAAA;AACdR,IAAAA,IAAM,EAAAS,MAAA;AACNC,IAAAA,WAAAA,SAAAA,UAAUC,GAAiD,EAAA;AACzD,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;AACV,MAAA,OAAA,CAAC,CAAG,EAAA,CAAA,EAAG,CAAG,EAAA,CAAA,EAAG,GAAG,CAAG,EAAA,CAAC,CAAE,CAAAC,QAAA,CAASD,GAAG,CAAA,CAAA;AAC3C,KAAA;GACF;AAEAE,EAAAA,MAAQ,EAAA;AACNb,IAAAA,IAAM,EAAAC,MAAA;IACN,SAAS,EAAA,YAAA;GACX;AAEAa,EAAAA,IAAM,EAAA;AACJd,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAa,EAAAA,oBAAsB,EAAA;AACpBf,IAAAA,IAAM,EAAAK,OAAA;IACN,SAAS,EAAA,IAAA;GACX;AAEAW,EAAAA,IAAM,EAAA;AACJhB,IAAAA,IAAM,EAAAC,MAAA;AACN,IAAA,SAAA,EAAS,OAAA;AACTS,IAAAA,WAAAA,SAAAA,UAAUC,GAAuC,EAAA;AAC/C,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;MACjB,OAAO,CAAC,OAAA,EAAS,MAAM,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AACvC,KAAA;GACF;AAEAM,EAAAA,KAAO,EAAA;AACLjB,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQQ,MAAM,CAAA;GACvB;AAEAS,EAAAA,QAAU,EAAAb,OAAA;AAEVc,EAAAA,sBAAwB,EAAAd,OAAA;AAExBe,EAAAA,KAAO,EAAA;AACLpB,IAAAA,IAAM,EAAAqB,KAAAA;GACR;AAEAC,EAAAA,KAAO,EAAA;AACLtB,IAAAA,IAAM,EAAAC,MAAA;AACN,IAAA,SAAA,EAAS,MAAA;AACTS,IAAAA,WAAAA,SAAAA,UAAUC,GAAwC,EAAA;AAChD,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;MACjB,OAAO,CAAC,MAAA,EAAQ,MAAM,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AACtC,KAAA;GACF;AAEAY,EAAAA,KAAO,EAAA;AACLvB,IAAAA,IAAM,EAAA,CAACC,MAAQ,EAAAoB,KAAA,EAAOG,IAAI,CAAA;GAC5B;AAEAC,EAAAA,IAAM,EAAA;AACJzB,IAAAA,IAAA,EAAM,CAACqB,KAAA,EAAOnB,QAAQ,CAAA;GACxB;AAEAwB,EAAAA,IAAM,EAAA;AACJ1B,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQQ,MAAM,CAAA;GACvB;AAEAkB,EAAAA,WAAa,EAAAzB,QAAA;AAEb0B,EAAAA,iBAAmB,EAAA1B,QAAA;AAEnB2B,EAAAA,gBAAkB,EAAA3B,QAAA;AAElB4B,EAAAA,kBAAoB,EAAA5B,QAAA;AAEpB6B,EAAAA,aAAe,EAAA7B,QAAAA;AACjB,CAAA;;;;"}