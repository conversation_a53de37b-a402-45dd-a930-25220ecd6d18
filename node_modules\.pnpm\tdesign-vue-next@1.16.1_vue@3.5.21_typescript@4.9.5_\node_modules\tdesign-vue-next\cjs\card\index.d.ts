import { TdCardProps } from './type';
import './style';
export * from './type';
export type CardProps = TdCardProps;
export declare const Card: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        actions: {
            type: import("vue").PropType<TdCardProps["actions"]>;
        };
        avatar: {
            type: import("vue").PropType<TdCardProps["avatar"]>;
        };
        bordered: {
            type: BooleanConstructor;
            default: boolean;
        };
        bodyClassName: {
            type: import("vue").PropType<TdCardProps["bodyClassName"]>;
        };
        bodyStyle: {
            type: import("vue").PropType<TdCardProps["bodyStyle"]>;
        };
        content: {
            type: import("vue").PropType<TdCardProps["content"]>;
        };
        cover: {
            type: import("vue").PropType<TdCardProps["cover"]>;
        };
        default: {
            type: import("vue").PropType<TdCardProps["default"]>;
        };
        description: {
            type: import("vue").PropType<TdCardProps["description"]>;
        };
        footer: {
            type: import("vue").PropType<TdCardProps["footer"]>;
        };
        footerClassName: {
            type: import("vue").PropType<TdCardProps["footerClassName"]>;
        };
        footerStyle: {
            type: import("vue").PropType<TdCardProps["footerStyle"]>;
        };
        header: {
            type: import("vue").PropType<TdCardProps["header"]>;
        };
        headerClassName: {
            type: import("vue").PropType<TdCardProps["headerClassName"]>;
        };
        headerStyle: {
            type: import("vue").PropType<TdCardProps["headerStyle"]>;
        };
        headerBordered: BooleanConstructor;
        hoverShadow: BooleanConstructor;
        loading: {
            type: import("vue").PropType<TdCardProps["loading"]>;
            default: TdCardProps["loading"];
        };
        loadingProps: {
            type: import("vue").PropType<TdCardProps["loadingProps"]>;
        };
        shadow: BooleanConstructor;
        size: {
            type: import("vue").PropType<TdCardProps["size"]>;
            default: TdCardProps["size"];
            validator(val: TdCardProps["size"]): boolean;
        };
        status: {
            type: StringConstructor;
            default: string;
        };
        subtitle: {
            type: import("vue").PropType<TdCardProps["subtitle"]>;
        };
        theme: {
            type: import("vue").PropType<TdCardProps["theme"]>;
            default: TdCardProps["theme"];
            validator(val: TdCardProps["theme"]): boolean;
        };
        title: {
            type: import("vue").PropType<TdCardProps["title"]>;
        };
    }>>, () => any, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        actions: {
            type: import("vue").PropType<TdCardProps["actions"]>;
        };
        avatar: {
            type: import("vue").PropType<TdCardProps["avatar"]>;
        };
        bordered: {
            type: BooleanConstructor;
            default: boolean;
        };
        bodyClassName: {
            type: import("vue").PropType<TdCardProps["bodyClassName"]>;
        };
        bodyStyle: {
            type: import("vue").PropType<TdCardProps["bodyStyle"]>;
        };
        content: {
            type: import("vue").PropType<TdCardProps["content"]>;
        };
        cover: {
            type: import("vue").PropType<TdCardProps["cover"]>;
        };
        default: {
            type: import("vue").PropType<TdCardProps["default"]>;
        };
        description: {
            type: import("vue").PropType<TdCardProps["description"]>;
        };
        footer: {
            type: import("vue").PropType<TdCardProps["footer"]>;
        };
        footerClassName: {
            type: import("vue").PropType<TdCardProps["footerClassName"]>;
        };
        footerStyle: {
            type: import("vue").PropType<TdCardProps["footerStyle"]>;
        };
        header: {
            type: import("vue").PropType<TdCardProps["header"]>;
        };
        headerClassName: {
            type: import("vue").PropType<TdCardProps["headerClassName"]>;
        };
        headerStyle: {
            type: import("vue").PropType<TdCardProps["headerStyle"]>;
        };
        headerBordered: BooleanConstructor;
        hoverShadow: BooleanConstructor;
        loading: {
            type: import("vue").PropType<TdCardProps["loading"]>;
            default: TdCardProps["loading"];
        };
        loadingProps: {
            type: import("vue").PropType<TdCardProps["loadingProps"]>;
        };
        shadow: BooleanConstructor;
        size: {
            type: import("vue").PropType<TdCardProps["size"]>;
            default: TdCardProps["size"];
            validator(val: TdCardProps["size"]): boolean;
        };
        status: {
            type: StringConstructor;
            default: string;
        };
        subtitle: {
            type: import("vue").PropType<TdCardProps["subtitle"]>;
        };
        theme: {
            type: import("vue").PropType<TdCardProps["theme"]>;
            default: TdCardProps["theme"];
            validator(val: TdCardProps["theme"]): boolean;
        };
        title: {
            type: import("vue").PropType<TdCardProps["title"]>;
        };
    }>>, {
        loading: boolean | ((h: typeof import("vue").h) => import("..").TNodeReturnValue);
        size: "small" | "medium";
        status: string;
        theme: "normal" | "poster1" | "poster2";
        bordered: boolean;
        headerBordered: boolean;
        hoverShadow: boolean;
        shadow: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        actions: {
            type: import("vue").PropType<TdCardProps["actions"]>;
        };
        avatar: {
            type: import("vue").PropType<TdCardProps["avatar"]>;
        };
        bordered: {
            type: BooleanConstructor;
            default: boolean;
        };
        bodyClassName: {
            type: import("vue").PropType<TdCardProps["bodyClassName"]>;
        };
        bodyStyle: {
            type: import("vue").PropType<TdCardProps["bodyStyle"]>;
        };
        content: {
            type: import("vue").PropType<TdCardProps["content"]>;
        };
        cover: {
            type: import("vue").PropType<TdCardProps["cover"]>;
        };
        default: {
            type: import("vue").PropType<TdCardProps["default"]>;
        };
        description: {
            type: import("vue").PropType<TdCardProps["description"]>;
        };
        footer: {
            type: import("vue").PropType<TdCardProps["footer"]>;
        };
        footerClassName: {
            type: import("vue").PropType<TdCardProps["footerClassName"]>;
        };
        footerStyle: {
            type: import("vue").PropType<TdCardProps["footerStyle"]>;
        };
        header: {
            type: import("vue").PropType<TdCardProps["header"]>;
        };
        headerClassName: {
            type: import("vue").PropType<TdCardProps["headerClassName"]>;
        };
        headerStyle: {
            type: import("vue").PropType<TdCardProps["headerStyle"]>;
        };
        headerBordered: BooleanConstructor;
        hoverShadow: BooleanConstructor;
        loading: {
            type: import("vue").PropType<TdCardProps["loading"]>;
            default: TdCardProps["loading"];
        };
        loadingProps: {
            type: import("vue").PropType<TdCardProps["loadingProps"]>;
        };
        shadow: BooleanConstructor;
        size: {
            type: import("vue").PropType<TdCardProps["size"]>;
            default: TdCardProps["size"];
            validator(val: TdCardProps["size"]): boolean;
        };
        status: {
            type: StringConstructor;
            default: string;
        };
        subtitle: {
            type: import("vue").PropType<TdCardProps["subtitle"]>;
        };
        theme: {
            type: import("vue").PropType<TdCardProps["theme"]>;
            default: TdCardProps["theme"];
            validator(val: TdCardProps["theme"]): boolean;
        };
        title: {
            type: import("vue").PropType<TdCardProps["title"]>;
        };
    }>>, () => any, {}, {}, {}, {
        loading: boolean | ((h: typeof import("vue").h) => import("..").TNodeReturnValue);
        size: "small" | "medium";
        status: string;
        theme: "normal" | "poster1" | "poster2";
        bordered: boolean;
        headerBordered: boolean;
        hoverShadow: boolean;
        shadow: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    actions: {
        type: import("vue").PropType<TdCardProps["actions"]>;
    };
    avatar: {
        type: import("vue").PropType<TdCardProps["avatar"]>;
    };
    bordered: {
        type: BooleanConstructor;
        default: boolean;
    };
    bodyClassName: {
        type: import("vue").PropType<TdCardProps["bodyClassName"]>;
    };
    bodyStyle: {
        type: import("vue").PropType<TdCardProps["bodyStyle"]>;
    };
    content: {
        type: import("vue").PropType<TdCardProps["content"]>;
    };
    cover: {
        type: import("vue").PropType<TdCardProps["cover"]>;
    };
    default: {
        type: import("vue").PropType<TdCardProps["default"]>;
    };
    description: {
        type: import("vue").PropType<TdCardProps["description"]>;
    };
    footer: {
        type: import("vue").PropType<TdCardProps["footer"]>;
    };
    footerClassName: {
        type: import("vue").PropType<TdCardProps["footerClassName"]>;
    };
    footerStyle: {
        type: import("vue").PropType<TdCardProps["footerStyle"]>;
    };
    header: {
        type: import("vue").PropType<TdCardProps["header"]>;
    };
    headerClassName: {
        type: import("vue").PropType<TdCardProps["headerClassName"]>;
    };
    headerStyle: {
        type: import("vue").PropType<TdCardProps["headerStyle"]>;
    };
    headerBordered: BooleanConstructor;
    hoverShadow: BooleanConstructor;
    loading: {
        type: import("vue").PropType<TdCardProps["loading"]>;
        default: TdCardProps["loading"];
    };
    loadingProps: {
        type: import("vue").PropType<TdCardProps["loadingProps"]>;
    };
    shadow: BooleanConstructor;
    size: {
        type: import("vue").PropType<TdCardProps["size"]>;
        default: TdCardProps["size"];
        validator(val: TdCardProps["size"]): boolean;
    };
    status: {
        type: StringConstructor;
        default: string;
    };
    subtitle: {
        type: import("vue").PropType<TdCardProps["subtitle"]>;
    };
    theme: {
        type: import("vue").PropType<TdCardProps["theme"]>;
        default: TdCardProps["theme"];
        validator(val: TdCardProps["theme"]): boolean;
    };
    title: {
        type: import("vue").PropType<TdCardProps["title"]>;
    };
}>>, () => any, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, {
    loading: boolean | ((h: typeof import("vue").h) => import("..").TNodeReturnValue);
    size: "small" | "medium";
    status: string;
    theme: "normal" | "poster1" | "poster2";
    bordered: boolean;
    headerBordered: boolean;
    hoverShadow: boolean;
    shadow: boolean;
}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("vue").Plugin;
export default Card;
