/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _defineProperty = require('@babel/runtime/helpers/defineProperty');
var Vue = require('vue');
var cascader_components_Item = require('./Item.js');
var cascader_props = require('../props.js');
require('@babel/runtime/helpers/toConsumableArray');
require('@babel/runtime/helpers/typeof');
require('../../_chunks/dep-086ab407.js');
var renderTnode = require('../../_chunks/dep-6ae67bab.js');
var index = require('../../_chunks/dep-059461d7.js');
var index$1 = require('../../_chunks/dep-e2298443.js');
require('@babel/runtime/helpers/slicedToArray');
require('../../_chunks/dep-b15ee9eb.js');
var cascader_utils_effect = require('../utils/effect.js');
var cascader_utils_helper = require('../utils/helper.js');
var configProvider_hooks_useConfig = require('../../config-provider/hooks/useConfig.js');
require('tdesign-icons-vue-next');
require('../utils/className.js');
require('../../checkbox/index.js');
require('../../checkbox/checkbox.js');
require('../../checkbox/props.js');
require('../../_chunks/dep-6666de7f.js');
require('../../_chunks/dep-b09565a1.js');
require('../../_chunks/dep-756628ef.js');
require('../../_chunks/dep-febae5f4.js');
require('../../_chunks/dep-3f3d49e4.js');
require('../../_chunks/dep-7e5fc00e.js');
require('../../_chunks/dep-988704d0.js');
require('../../_chunks/dep-076bd726.js');
require('../../_chunks/dep-baa72039.js');
require('../../_chunks/dep-7c14108f.js');
require('../../_chunks/dep-3bb82c67.js');
require('../../_chunks/dep-cabb6240.js');
require('../../_chunks/dep-12e0aded.js');
require('../../_chunks/dep-306b2e72.js');
require('../../checkbox/consts/index.js');
require('../../checkbox/hooks/useCheckboxLazyLoad.js');
require('../../_chunks/dep-c9fd0e5d.js');
require('../../checkbox/hooks/useKeyboardEvent.js');
require('../../_chunks/dep-01642c17.js');
require('../../_chunks/dep-914897c8.js');
require('../../_chunks/dep-d5654a4e.js');
require('../../_chunks/dep-2748f688.js');
require('../../_chunks/dep-94424a57.js');
require('../../_chunks/dep-ca39ce6d.js');
require('../../_chunks/dep-324af0df.js');
require('../../_chunks/dep-76847e8b.js');
require('../../_chunks/dep-f8224a9c.js');
require('../../_chunks/dep-f9e26c60.js');
require('../../_chunks/dep-714d992c.js');
require('dayjs');
require('../../_chunks/dep-94ff6543.js');
require('../../_chunks/dep-fa8a400f.js');
require('../../_chunks/dep-6b54b4a5.js');
require('../../_chunks/dep-eac47ca0.js');
require('../../_chunks/dep-df581fcb.js');
require('../../_chunks/dep-6a71c082.js');
require('../../_chunks/dep-ef3df7aa.js');
require('../../_chunks/dep-6c0887f7.js');
require('../../_chunks/dep-8c0a3845.js');
require('../../_chunks/dep-4c75812c.js');
require('../../_chunks/dep-ca4c3e97.js');
require('../../_chunks/dep-6183bb4a.js');
require('../../_chunks/dep-53dbb954.js');
require('../../_chunks/dep-d1c7139a.js');
require('../../_chunks/dep-ba035735.js');
require('@babel/runtime/helpers/createClass');
require('@babel/runtime/helpers/classCallCheck');
require('../../checkbox/group.js');
require('../../checkbox/checkbox-group-props.js');
require('../../_chunks/dep-872d0152.js');
require('../../_chunks/dep-cbee8f46.js');
require('../../_chunks/dep-59fa52ec.js');
require('../../_chunks/dep-a271f384.js');
require('../../_chunks/dep-2105b21f.js');
require('../../_chunks/dep-ac7dea19.js');
require('../../loading/index.js');
require('../../_chunks/dep-24c31f97.js');
require('../../loading/plugin.js');
require('../../_chunks/dep-faade8bd.js');
require('../../loading/icon/gradient.js');
require('../../_chunks/dep-fd4183e8.js');
require('@babel/runtime/helpers/objectWithoutProperties');
require('../../_chunks/dep-f20572a3.js');
require('../../_chunks/dep-8c00290f.js');
require('../../loading/props.js');
require('../../_chunks/dep-605cf2e8.js');
require('../../_chunks/dep-925e8207.js');
require('../../_chunks/dep-66442631.js');
require('../../_chunks/dep-156361a6.js');
require('../../_chunks/dep-6d27c874.js');
require('../../_chunks/dep-31eb9b48.js');
require('../../_chunks/dep-ce7457dd.js');
require('../../_chunks/dep-4023d837.js');
require('../../_chunks/dep-cbaee605.js');
require('../../_chunks/dep-ba5948c9.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _defineProperty__default = /*#__PURE__*/_interopDefaultLegacy(_defineProperty);

var Panel = Vue.defineComponent({
  name: "TCascaderSubPanel",
  props: {
    option: cascader_props["default"].option,
    empty: cascader_props["default"].empty,
    trigger: cascader_props["default"].trigger,
    onChange: cascader_props["default"].onChange,
    loading: cascader_props["default"].loading,
    loadingText: cascader_props["default"].loadingText,
    cascaderContext: {
      type: Object
    }
  },
  setup: function setup(props) {
    var renderTNodeJSXDefault = index.useTNodeDefault();
    var COMPONENT_NAME = index$1.usePrefixClass("cascader");
    var _useConfig = configProvider_hooks_useConfig.useConfig("cascader"),
      globalConfig = _useConfig.globalConfig;
    var panels = Vue.computed(function () {
      return cascader_utils_helper.getPanels(props.cascaderContext.treeNodes);
    });
    var handleExpand = function handleExpand(node, trigger) {
      var propsTrigger = props.trigger,
        cascaderContext = props.cascaderContext;
      cascader_utils_effect.expendClickEffect(propsTrigger, trigger, node, cascaderContext);
    };
    var renderItem = function renderItem(node, index) {
      var optionChild = node.data.content ? renderTnode.getDefaultNode(node.data.content(Vue.h)) : renderTNodeJSXDefault("option", {
        params: {
          item: node.data,
          index: index
        }
      });
      return Vue.createVNode(cascader_components_Item["default"], {
        "key": node.value,
        "node": node,
        "optionChild": optionChild,
        "cascaderContext": props.cascaderContext,
        "onClick": function onClick() {
          handleExpand(node, "click");
        },
        "onMouseenter": function onMouseenter() {
          handleExpand(node, "hover");
        },
        "onChange": function onChange() {
          cascader_utils_effect.valueChangeEffect(node, props.cascaderContext);
        }
      }, null);
    };
    var renderList = function renderList(treeNodes) {
      var isFilter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      var segment = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
      var index = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;
      return Vue.createVNode("ul", {
        "class": ["".concat(COMPONENT_NAME.value, "__menu"), "narrow-scrollbar", _defineProperty__default["default"](_defineProperty__default["default"]({}, "".concat(COMPONENT_NAME.value, "__menu--segment"), segment), "".concat(COMPONENT_NAME.value, "__menu--filter"), isFilter)],
        "key": "".concat(COMPONENT_NAME, "__menu").concat(index)
      }, [treeNodes.map(function (node) {
        return renderItem(node, index);
      })]);
    };
    var renderPanels = function renderPanels() {
      var _props$cascaderContex = props.cascaderContext,
        inputVal = _props$cascaderContex.inputVal,
        treeNodes = _props$cascaderContex.treeNodes;
      return inputVal ? renderList(treeNodes, true) : panels.value.map(function (treeNodes2, index) {
        return renderList(treeNodes2, false, index !== panels.value.length - 1, index);
      });
    };
    return function () {
      var content;
      if (props.loading) {
        content = renderTNodeJSXDefault("loadingText", Vue.createVNode("div", {
          "class": "".concat(COMPONENT_NAME.value, "__panel--empty")
        }, [globalConfig.value.loadingText]));
      } else {
        content = panels.value.length ? renderPanels() : renderTNodeJSXDefault("empty", Vue.createVNode("div", {
          "class": "".concat(COMPONENT_NAME.value, "__panel--empty")
        }, [globalConfig.value.empty]));
      }
      return Vue.createVNode("div", {
        "class": ["".concat(COMPONENT_NAME.value, "__panel"), _defineProperty__default["default"]({}, "".concat(COMPONENT_NAME.value, "--normal"), panels.value.length && !props.loading)]
      }, [content]);
    };
  }
});

exports["default"] = Panel;
//# sourceMappingURL=Panel.js.map
