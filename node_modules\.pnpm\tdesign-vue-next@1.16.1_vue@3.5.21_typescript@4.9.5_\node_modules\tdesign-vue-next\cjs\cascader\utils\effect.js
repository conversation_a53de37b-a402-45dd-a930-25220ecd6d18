/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _slicedToArray = require('@babel/runtime/helpers/slicedToArray');
var cascader_utils_helper = require('./helper.js');
var isNumber = require('../../_chunks/dep-8c00290f.js');
var cloneDeep = require('../../_chunks/dep-6183bb4a.js');
var isFunction = require('../../_chunks/dep-94424a57.js');
var isArray = require('../../_chunks/dep-12e0aded.js');
require('../../_chunks/dep-ca39ce6d.js');
require('@babel/runtime/helpers/typeof');
require('../../_chunks/dep-ba5948c9.js');
require('../../_chunks/dep-53dbb954.js');
require('../../_chunks/dep-6a71c082.js');
require('../../_chunks/dep-febae5f4.js');
require('../../_chunks/dep-ef3df7aa.js');
require('../../_chunks/dep-3f3d49e4.js');
require('../../_chunks/dep-df581fcb.js');
require('../../_chunks/dep-6b54b4a5.js');
require('../../_chunks/dep-d1c7139a.js');
require('../../_chunks/dep-94ff6543.js');
require('../../_chunks/dep-fa8a400f.js');
require('../../_chunks/dep-eac47ca0.js');
require('../../_chunks/dep-6c0887f7.js');
require('../../_chunks/dep-8c0a3845.js');
require('../../_chunks/dep-ba035735.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _slicedToArray__default = /*#__PURE__*/_interopDefaultLegacy(_slicedToArray);

function expendClickEffect(propsTrigger, trigger, node, cascaderContext) {
  var checkStrictly = cascaderContext.checkStrictly,
    multiple = cascaderContext.multiple,
    treeStore = cascaderContext.treeStore,
    setVisible = cascaderContext.setVisible,
    setValue = cascaderContext.setValue,
    setTreeNodes = cascaderContext.setTreeNodes,
    setExpend = cascaderContext.setExpend,
    value = cascaderContext.value,
    max = cascaderContext.max,
    valueType = cascaderContext.valueType;
  var isDisabled = node.disabled || multiple && value.length >= max && max !== 0;
  if (isDisabled) return;
  if (propsTrigger === trigger) {
    var expanded = node.setExpanded(true);
    treeStore.refreshNodes();
    if (!cascaderContext.inputVal) {
      treeStore.replaceExpanded(expanded);
      var nodes = treeStore.getNodes().filter(function (node2) {
        return node2.visible;
      });
      setTreeNodes(nodes);
    }
    if (multiple) {
      setExpend(expanded);
    }
  }
  if (!multiple && (node.isLeaf() || checkStrictly) && trigger === "click") {
    treeStore.resetChecked();
    var checked = node.setChecked(!node.checked);
    var _checked = _slicedToArray__default["default"](checked, 1),
      value2 = _checked[0];
    setValue(valueType === "single" ? value2 : node.getPath().map(function (item) {
      return item.value;
    }), "check", node.getModel());
    if (!checkStrictly || propsTrigger === "hover") {
      setVisible(false, {});
    }
  }
}
function valueChangeEffect(node, cascaderContext) {
  var disabled = cascaderContext.disabled,
    max = cascaderContext.max,
    inputVal = cascaderContext.inputVal,
    multiple = cascaderContext.multiple,
    setVisible = cascaderContext.setVisible,
    setValue = cascaderContext.setValue,
    treeNodes = cascaderContext.treeNodes,
    treeStore = cascaderContext.treeStore,
    valueType = cascaderContext.valueType;
  if (!node || disabled || node.disabled) {
    return;
  }
  var checked = node.setChecked(!node.isChecked());
  if (isNumber.isNumber(max) && max < 0) {
    console.warn("TDesign Warn:", "max should > 0");
  }
  if (checked.length > max && isNumber.isNumber(max) && max > 0) {
    return;
  }
  if (checked.length === 0) {
    var expanded = treeStore.getExpanded();
    setTimeout(function () {
      treeStore.replaceExpanded(expanded);
      treeStore.refreshNodes();
    }, 0);
  }
  if (!multiple) {
    setVisible(false, {});
  }
  var isSelectAll = treeNodes.every(function (item) {
    return checked.indexOf(item.value) > -1;
  });
  if (inputVal && isSelectAll) {
    setVisible(false, {});
  }
  var resValue = valueType === "single" ? checked : checked.map(function (val) {
    return treeStore.getNode(val).getPath().map(function (item) {
      return item.value;
    });
  });
  setValue(resValue, node.checked ? "uncheck" : "check", node.getModel());
}
function closeIconClickEffect(cascaderContext) {
  var setVisible = cascaderContext.setVisible,
    multiple = cascaderContext.multiple,
    setValue = cascaderContext.setValue;
  setVisible(false, {});
  setValue(multiple ? [] : "", "clear");
}
function handleRemoveTagEffect(cascaderContext, index, onRemove) {
  var disabled = cascaderContext.disabled,
    setValue = cascaderContext.setValue,
    value = cascaderContext.value,
    valueType = cascaderContext.valueType,
    treeStore = cascaderContext.treeStore;
  if (disabled) return;
  if (index !== void 0) {
    var newValue = cloneDeep.cloneDeep(value);
    var res = newValue.splice(index, 1);
    var node = treeStore.getNodes(res[0])[0];
    var checked = node.setChecked(!node.isChecked());
    var resValue = valueType === "single" ? checked : checked.map(function (val) {
      return treeStore.getNode(val).getPath().map(function (item) {
        return item.value;
      });
    });
    setValue(resValue, "uncheck", node.getModel());
    if (isFunction.isFunction(onRemove)) {
      onRemove({
        value: checked,
        node: node
      });
    }
  } else {
    if (isFunction.isFunction(onRemove)) {
      onRemove({
        value: value,
        node: void 0
      });
    }
  }
}
var treeNodesEffect = function treeNodesEffect(inputVal, treeStore, setTreeNodes, filter) {
  if (!treeStore) return;
  var nodes = [];
  if (inputVal) {
    var filterMethods = function filterMethods(node) {
      if (!node.isLeaf()) return;
      if (isFunction.isFunction(filter)) {
        return filter("".concat(inputVal), node);
      }
      var fullPathLabel = cascader_utils_helper.getFullPathLabel(node, "");
      return fullPathLabel.indexOf("".concat(inputVal)) > -1;
    };
    nodes = treeStore.nodes.filter(filterMethods);
  } else {
    nodes = treeStore.getNodes().filter(function (node) {
      return node.visible;
    });
  }
  setTreeNodes(nodes);
};
var treeStoreExpendEffect = function treeStoreExpendEffect(treeStore, value, expend) {
  var treeValue = cascader_utils_helper.getTreeValue(value);
  if (!treeStore) return;
  if (isArray.isArray(treeValue) && expend.length === 0) {
    var expandedMap = /* @__PURE__ */new Map();
    var _treeValue = _slicedToArray__default["default"](treeValue, 1),
      val = _treeValue[0];
    if (!cascader_utils_helper.isEmptyValues(val)) {
      expandedMap.set(val, true);
      var node = treeStore.getNode(val);
      if (!node) {
        treeStore.refreshNodes();
        return;
      }
      node.getParents().forEach(function (tn) {
        expandedMap.set(tn.value, true);
      });
      var expandedArr = Array.from(expandedMap.keys());
      treeStore.replaceExpanded(expandedArr);
    }
  }
  if (treeStore.getExpanded() && expend.length) {
    treeStore.replaceExpanded(expend);
  }
  treeStore.refreshNodes();
};

exports.closeIconClickEffect = closeIconClickEffect;
exports.expendClickEffect = expendClickEffect;
exports.handleRemoveTagEffect = handleRemoveTagEffect;
exports.treeNodesEffect = treeNodesEffect;
exports.treeStoreExpendEffect = treeStoreExpendEffect;
exports.valueChangeEffect = valueChangeEffect;
//# sourceMappingURL=effect.js.map
