{"version": 3, "file": "useKeyboardEvent.js", "sources": ["../../../../components/checkbox/hooks/useKeyboardEvent.ts"], "sourcesContent": ["import { CHECKED_CODE_REG } from '@tdesign/common-js/common';\n\nexport function useKeyboardEvent(handleChange: (e: Event) => void) {\n  const keyboardEventListener = (e: KeyboardEvent) => {\n    const isCheckedCode = CHECKED_CODE_REG.test(e.key) || CHECKED_CODE_REG.test(e.code);\n    if (isCheckedCode) {\n      e.preventDefault();\n      const { disabled } = (e.currentTarget as HTMLElement).querySelector('input');\n      !disabled && handleChange(e);\n    }\n  };\n\n  const onCheckboxFocus = (e: FocusEvent) => {\n    e.currentTarget.addEventListener('keydown', keyboardEventListener);\n  };\n\n  const onCheckboxBlur = (e: FocusEvent) => {\n    e.currentTarget.removeEventListener('keydown', keyboardEventListener);\n  };\n\n  return {\n    onCheckboxFocus,\n    onCheckboxBlur,\n  };\n}\n\nexport default useKeyboardEvent;\n"], "names": ["useKeyboardEvent", "handleChange", "keyboardEventListener", "e", "isCheckedCode", "CHECKED_CODE_REG", "test", "key", "code", "preventDefault", "_e$currentTarget$quer", "currentTarget", "querySelector", "disabled", "onCheckboxFocus", "addEventListener", "onCheckboxBlur", "removeEventListener"], "mappings": ";;;;;;;;;;;;AAEO,SAASA,iBAAiBC,YAAkC,EAAA;AAC3D,EAAA,IAAAC,qBAAA,GAAwB,SAAxBA,qBAAAA,CAAyBC,CAAqB,EAAA;AAC5C,IAAA,IAAAC,aAAA,GAAgBC,wBAAiBC,IAAK,CAAAH,CAAA,CAAEI,GAAG,CAAK,IAAAF,uBAAA,CAAiBC,IAAK,CAAAH,CAAA,CAAEK,IAAI,CAAA,CAAA;AAClF,IAAA,IAAIJ,aAAe,EAAA;MACjBD,CAAA,CAAEM,cAAe,EAAA,CAAA;MACjB,IAAAC,qBAAA,GAAsBP,CAAE,CAAAQ,aAAA,CAA8BC,cAAc,OAAO,CAAA;QAAnEC,QAAS,GAAAH,qBAAA,CAATG,QAAS,CAAA;AAChB,MAAA,CAAAA,QAAA,IAAYZ,aAAaE,CAAC,CAAA,CAAA;AAC7B,KAAA;GACF,CAAA;AAEM,EAAA,IAAAW,eAAA,GAAkB,SAAlBA,eAAAA,CAAmBX,CAAkB,EAAA;IACvCA,CAAA,CAAAQ,aAAA,CAAcI,gBAAiB,CAAA,SAAA,EAAWb,qBAAqB,CAAA,CAAA;GACnE,CAAA;AAEM,EAAA,IAAAc,cAAA,GAAiB,SAAjBA,cAAAA,CAAkBb,CAAkB,EAAA;IACtCA,CAAA,CAAAQ,aAAA,CAAcM,mBAAoB,CAAA,SAAA,EAAWf,qBAAqB,CAAA,CAAA;GACtE,CAAA;EAEO,OAAA;AACLY,IAAAA,eAAA,EAAAA,eAAA;AACAE,IAAAA,cAAA,EAAAA,cAAAA;GACF,CAAA;AACF;;;;;"}