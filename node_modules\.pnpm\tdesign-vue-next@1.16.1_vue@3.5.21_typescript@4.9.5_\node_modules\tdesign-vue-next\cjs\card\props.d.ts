import { TdCardProps } from './type';
import { PropType } from 'vue';
declare const _default: {
    actions: {
        type: PropType<TdCardProps["actions"]>;
    };
    avatar: {
        type: PropType<TdCardProps["avatar"]>;
    };
    bordered: {
        type: BooleanConstructor;
        default: boolean;
    };
    bodyClassName: {
        type: PropType<TdCardProps["bodyClassName"]>;
    };
    bodyStyle: {
        type: PropType<TdCardProps["bodyStyle"]>;
    };
    content: {
        type: PropType<TdCardProps["content"]>;
    };
    cover: {
        type: PropType<TdCardProps["cover"]>;
    };
    default: {
        type: PropType<TdCardProps["default"]>;
    };
    description: {
        type: PropType<TdCardProps["description"]>;
    };
    footer: {
        type: PropType<TdCardProps["footer"]>;
    };
    footerClassName: {
        type: PropType<TdCardProps["footerClassName"]>;
    };
    footerStyle: {
        type: PropType<TdCardProps["footerStyle"]>;
    };
    header: {
        type: PropType<TdCardProps["header"]>;
    };
    headerClassName: {
        type: PropType<TdCardProps["headerClassName"]>;
    };
    headerStyle: {
        type: PropType<TdCardProps["headerStyle"]>;
    };
    headerBordered: BooleanConstructor;
    hoverShadow: BooleanConstructor;
    loading: {
        type: PropType<TdCardProps["loading"]>;
        default: TdCardProps["loading"];
    };
    loadingProps: {
        type: PropType<TdCardProps["loadingProps"]>;
    };
    shadow: BooleanConstructor;
    size: {
        type: PropType<TdCardProps["size"]>;
        default: TdCardProps["size"];
        validator(val: TdCardProps["size"]): boolean;
    };
    status: {
        type: StringConstructor;
        default: string;
    };
    subtitle: {
        type: PropType<TdCardProps["subtitle"]>;
    };
    theme: {
        type: PropType<TdCardProps["theme"]>;
        default: TdCardProps["theme"];
        validator(val: TdCardProps["theme"]): boolean;
    };
    title: {
        type: PropType<TdCardProps["title"]>;
    };
};
export default _default;
