#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/eslint-config-prettier@8.10.2_eslint@8.57.1/node_modules/eslint-config-prettier/bin/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/eslint-config-prettier@8.10.2_eslint@8.57.1/node_modules/eslint-config-prettier/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/eslint-config-prettier@8.10.2_eslint@8.57.1/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/eslint-config-prettier@8.10.2_eslint@8.57.1/node_modules/eslint-config-prettier/bin/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/eslint-config-prettier@8.10.2_eslint@8.57.1/node_modules/eslint-config-prettier/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/eslint-config-prettier@8.10.2_eslint@8.57.1/node_modules:/mnt/e/JTNYProject/configuration-project-1/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../eslint-config-prettier/bin/cli.js" "$@"
else
  exec node  "$basedir/../eslint-config-prettier/bin/cli.js" "$@"
fi
