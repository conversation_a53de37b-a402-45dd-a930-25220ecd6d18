{"version": 3, "file": "props.js", "sources": ["../../../components/card/props.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * 该文件为脚本自动生成文件，请勿随意修改。如需修改请联系 PMC\n * */\n\nimport { TdCardProps } from './type';\nimport { PropType } from 'vue';\n\nexport default {\n  /** 卡片操作区 */\n  actions: {\n    type: [String, Function] as PropType<TdCardProps['actions']>,\n  },\n  /** 卡片中的用户头像，仅在海报风格的卡片中有效 */\n  avatar: {\n    type: [String, Function] as PropType<TdCardProps['avatar']>,\n  },\n  /** 是否有边框 */\n  bordered: {\n    type: Boolean,\n    default: true,\n  },\n  /** 卡片内容区域自定义类名 */\n  bodyClassName: {\n    type: String as PropType<TdCardProps['bodyClassName']>,\n  },\n  /** body区域自定义样式 */\n  bodyStyle: {\n    type: Object as PropType<TdCardProps['bodyStyle']>,\n  },\n  /** 卡片内容 */\n  content: {\n    type: [String, Function] as PropType<TdCardProps['content']>,\n  },\n  /** 卡片封面图。值类型为字符串，会自动使用 `img` 标签输出封面图；也可以完全最定义封面图 */\n  cover: {\n    type: [String, Function] as PropType<TdCardProps['cover']>,\n  },\n  /** 卡片内容，同 content */\n  default: {\n    type: [String, Function] as PropType<TdCardProps['default']>,\n  },\n  /** 卡片描述文案 */\n  description: {\n    type: [String, Function] as PropType<TdCardProps['description']>,\n  },\n  /** 卡片底部内容，可完全自定义 */\n  footer: {\n    type: [String, Function] as PropType<TdCardProps['footer']>,\n  },\n  /** 卡片底部区域自定义类名 */\n  footerClassName: {\n    type: String as PropType<TdCardProps['footerClassName']>,\n  },\n  /** 卡片底部区域自定义样式 */\n  footerStyle: {\n    type: Object as PropType<TdCardProps['footerStyle']>,\n  },\n  /** 卡片顶部内容，优先级高于其他所有元素 */\n  header: {\n    type: [String, Function] as PropType<TdCardProps['header']>,\n  },\n  /** 卡片顶部区域自定义类名 */\n  headerClassName: {\n    type: String as PropType<TdCardProps['headerClassName']>,\n  },\n  /** 卡片顶部区域自定义样式 */\n  headerStyle: {\n    type: Object as PropType<TdCardProps['headerStyle']>,\n  },\n  /** 头部是否带分割线，仅在有header时有效 */\n  headerBordered: Boolean,\n  /** hover时是否有阴影 */\n  hoverShadow: Boolean,\n  /** 加载状态，值为 true 会根据不同的布局显示不同的加载状态，值为 false 则表示非加载状态。也可以使用 Skeleton 组件完全自定义加载态呈现内容 */\n  loading: {\n    type: [Boolean, Function] as PropType<TdCardProps['loading']>,\n    default: false as TdCardProps['loading'],\n  },\n  /** 透传加载组件(Loading)全部属性 */\n  loadingProps: {\n    type: Object as PropType<TdCardProps['loadingProps']>,\n  },\n  /** 是否显示卡片阴影，默认不显示 */\n  shadow: Boolean,\n  /** 尺寸 */\n  size: {\n    type: String as PropType<TdCardProps['size']>,\n    default: 'medium' as TdCardProps['size'],\n    validator(val: TdCardProps['size']): boolean {\n      if (!val) return true;\n      return ['medium', 'small'].includes(val);\n    },\n  },\n  /** 卡片状态内容，仅在操作区域不在顶部时有效（即 `theme=poster2` ） */\n  status: {\n    type: String,\n    default: '',\n  },\n  /** 卡片副标题 */\n  subtitle: {\n    type: [String, Function] as PropType<TdCardProps['subtitle']>,\n  },\n  /** 卡片风格：普通风格、海报风格1（操作区域在顶部）、海报风格2（操作区域在底部） */\n  theme: {\n    type: String as PropType<TdCardProps['theme']>,\n    default: 'normal' as TdCardProps['theme'],\n    validator(val: TdCardProps['theme']): boolean {\n      if (!val) return true;\n      return ['normal', 'poster1', 'poster2'].includes(val);\n    },\n  },\n  /** 卡片标题 */\n  title: {\n    type: [String, Function] as PropType<TdCardProps['title']>,\n  },\n};\n"], "names": ["actions", "type", "String", "Function", "avatar", "bordered", "Boolean", "bodyClassName", "bodyStyle", "Object", "content", "cover", "description", "footer", "footerClassName", "footerStyle", "header", "headerClassName", "headerStyle", "headerBordered", "hoverShadow", "loading", "loadingProps", "shadow", "size", "validator", "val", "includes", "status", "subtitle", "theme", "title"], "mappings": ";;;;;;;;;;AASA,YAAe;AAEbA,EAAAA,OAAS,EAAA;AACPC,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAC,EAAAA,MAAQ,EAAA;AACNH,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAE,EAAAA,QAAU,EAAA;AACRJ,IAAAA,IAAM,EAAAK,OAAA;IACN,SAAS,EAAA,IAAA;GACX;AAEAC,EAAAA,aAAe,EAAA;AACbN,IAAAA,IAAM,EAAAC,MAAAA;GACR;AAEAM,EAAAA,SAAW,EAAA;AACTP,IAAAA,IAAM,EAAAQ,MAAAA;GACR;AAEAC,EAAAA,OAAS,EAAA;AACPT,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAQ,EAAAA,KAAO,EAAA;AACLV,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;EAEA,SAAS,EAAA;AACPF,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAS,EAAAA,WAAa,EAAA;AACXX,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAU,EAAAA,MAAQ,EAAA;AACNZ,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAW,EAAAA,eAAiB,EAAA;AACfb,IAAAA,IAAM,EAAAC,MAAAA;GACR;AAEAa,EAAAA,WAAa,EAAA;AACXd,IAAAA,IAAM,EAAAQ,MAAAA;GACR;AAEAO,EAAAA,MAAQ,EAAA;AACNf,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEAc,EAAAA,eAAiB,EAAA;AACfhB,IAAAA,IAAM,EAAAC,MAAAA;GACR;AAEAgB,EAAAA,WAAa,EAAA;AACXjB,IAAAA,IAAM,EAAAQ,MAAAA;GACR;AAEAU,EAAAA,cAAgB,EAAAb,OAAA;AAEhBc,EAAAA,WAAa,EAAAd,OAAA;AAEbe,EAAAA,OAAS,EAAA;AACPpB,IAAAA,IAAA,EAAM,CAACK,OAAA,EAASH,QAAQ,CAAA;IACxB,SAAS,EAAA,KAAA;GACX;AAEAmB,EAAAA,YAAc,EAAA;AACZrB,IAAAA,IAAM,EAAAQ,MAAAA;GACR;AAEAc,EAAAA,MAAQ,EAAAjB,OAAA;AAERkB,EAAAA,IAAM,EAAA;AACJvB,IAAAA,IAAM,EAAAC,MAAA;AACN,IAAA,SAAA,EAAS,QAAA;AACTuB,IAAAA,WAAAA,SAAAA,UAAUC,GAAmC,EAAA;AAC3C,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;MACjB,OAAO,CAAC,QAAA,EAAU,OAAO,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AACzC,KAAA;GACF;AAEAE,EAAAA,MAAQ,EAAA;AACN3B,IAAAA,IAAM,EAAAC,MAAA;IACN,SAAS,EAAA,EAAA;GACX;AAEA2B,EAAAA,QAAU,EAAA;AACR5B,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;GACzB;AAEA2B,EAAAA,KAAO,EAAA;AACL7B,IAAAA,IAAM,EAAAC,MAAA;AACN,IAAA,SAAA,EAAS,QAAA;AACTuB,IAAAA,WAAAA,SAAAA,UAAUC,GAAoC,EAAA;AAC5C,MAAA,IAAI,CAACA,GAAA,EAAY,OAAA,IAAA,CAAA;MACjB,OAAO,CAAC,QAAU,EAAA,SAAA,EAAW,SAAS,CAAA,CAAEC,SAASD,GAAG,CAAA,CAAA;AACtD,KAAA;GACF;AAEAK,EAAAA,KAAO,EAAA;AACL9B,IAAAA,IAAA,EAAM,CAACC,MAAA,EAAQC,QAAQ,CAAA;AACzB,GAAA;AACF,CAAA;;;;"}