{"version": 3, "file": "useCheckboxLazyLoad.js", "sources": ["../../../../components/checkbox/hooks/useCheckboxLazyLoad.ts"], "sourcesContent": ["import { onBeforeUnmount, onMounted, Ref, ref, watch } from 'vue';\nimport observe from '@tdesign/common-js/utils/observe';\n\nexport function useCheckboxLazyLoad(labelRef: Ref<HTMLElement>, lazyLoad: Ref<boolean>) {\n  const ioObserver = ref<IntersectionObserver>();\n  const showCheckbox = ref(true);\n  const handleLazyLoad = () => {\n    if (!lazyLoad.value) return;\n    showCheckbox.value = false;\n    const io = observe(\n      labelRef.value,\n      null,\n      () => {\n        showCheckbox.value = true;\n      },\n      0,\n    );\n    ioObserver.value = io;\n  };\n\n  onMounted(handleLazyLoad);\n\n  watch([lazyLoad, labelRef], handleLazyLoad);\n\n  onBeforeUnmount(() => {\n    if (!lazyLoad.value) return;\n    ioObserver.value.unobserve(labelRef.value);\n  });\n\n  return {\n    showCheckbox,\n  };\n}\n\nexport default useCheckboxLazyLoad;\n"], "names": ["useCheckboxLazyLoad", "labelRef", "lazyLoad", "ioObserver", "ref", "showCheckbox", "handleLazyLoad", "value", "io", "observe", "onMounted", "watch", "onBeforeUnmount", "unobserve"], "mappings": ";;;;;;;;;;;;;AAGgB,SAAAA,mBAAAA,CAAoBC,UAA4BC,QAAwB,EAAA;AACtF,EAAA,IAAMC,aAAaC,OAA0B,EAAA,CAAA;AACvC,EAAA,IAAAC,YAAA,GAAeD,QAAI,IAAI,CAAA,CAAA;AAC7B,EAAA,IAAME,iBAAiB,SAAjBA,iBAAuB;AAC3B,IAAA,IAAI,CAACJ,QAAS,CAAAK,KAAA,EAAO,OAAA;IACrBF,YAAA,CAAaE,KAAQ,GAAA,KAAA,CAAA;IACrB,IAAMC,EAAK,GAAAC,eAAA,CACTR,QAAS,CAAAM,KAAA,EACT,IAAA,EACA,YAAM;MACJF,YAAA,CAAaE,KAAQ,GAAA,IAAA,CAAA;KACvB,EACA,CACF,CAAA,CAAA;IACAJ,UAAA,CAAWI,KAAQ,GAAAC,EAAA,CAAA;GACrB,CAAA;EAEAE,aAAA,CAAUJ,cAAc,CAAA,CAAA;EAExBK,SAAA,CAAM,CAACT,QAAA,EAAUD,QAAQ,CAAA,EAAGK,cAAc,CAAA,CAAA;AAE1CM,EAAAA,mBAAA,CAAgB,YAAM;AACpB,IAAA,IAAI,CAACV,QAAS,CAAAK,KAAA,EAAO,OAAA;IACVJ,UAAA,CAAAI,KAAA,CAAMM,SAAU,CAAAZ,QAAA,CAASM,KAAK,CAAA,CAAA;AAC3C,GAAC,CAAA,CAAA;EAEM,OAAA;AACLF,IAAAA,YAAA,EAAAA,YAAAA;GACF,CAAA;AACF;;;;;"}