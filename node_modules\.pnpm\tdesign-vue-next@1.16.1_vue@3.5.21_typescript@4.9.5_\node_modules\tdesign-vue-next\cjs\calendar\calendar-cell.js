/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var Vue = require('vue');
var _defineProperty = require('@babel/runtime/helpers/defineProperty');
var _toConsumableArray = require('@babel/runtime/helpers/toConsumableArray');
var dayjs = require('dayjs');
require('@babel/runtime/helpers/typeof');
require('../_chunks/dep-086ab407.js');
var index = require('../_chunks/dep-059461d7.js');
var index$1 = require('../_chunks/dep-6666de7f.js');
require('../config-provider/hooks/useConfig.js');
require('@babel/runtime/helpers/slicedToArray');
require('../_chunks/dep-b15ee9eb.js');
var calendar_hooks_useCalendarClass = require('./hooks/useCalendarClass.js');
require('../_chunks/dep-d5654a4e.js');
require('../_chunks/dep-2748f688.js');
require('../_chunks/dep-94424a57.js');
require('../_chunks/dep-febae5f4.js');
require('../_chunks/dep-ca39ce6d.js');
require('../_chunks/dep-914897c8.js');
require('../_chunks/dep-12e0aded.js');
require('../_chunks/dep-3f3d49e4.js');
require('../_chunks/dep-6ae67bab.js');
require('../_chunks/dep-324af0df.js');
require('../_chunks/dep-cabb6240.js');
require('../_chunks/dep-306b2e72.js');
require('../_chunks/dep-76847e8b.js');
require('../_chunks/dep-f8224a9c.js');
require('../_chunks/dep-3bb82c67.js');
require('../_chunks/dep-7c14108f.js');
require('../_chunks/dep-f9e26c60.js');
require('../_chunks/dep-714d992c.js');
require('../_chunks/dep-94ff6543.js');
require('../_chunks/dep-fa8a400f.js');
require('../_chunks/dep-6b54b4a5.js');
require('../_chunks/dep-eac47ca0.js');
require('../_chunks/dep-df581fcb.js');
require('../_chunks/dep-6a71c082.js');
require('../_chunks/dep-ef3df7aa.js');
require('../_chunks/dep-6c0887f7.js');
require('../_chunks/dep-8c0a3845.js');
require('../_chunks/dep-4c75812c.js');
require('../_chunks/dep-ca4c3e97.js');
require('../_chunks/dep-6183bb4a.js');
require('../_chunks/dep-53dbb954.js');
require('../_chunks/dep-d1c7139a.js');
require('../_chunks/dep-ba035735.js');
require('@babel/runtime/helpers/createClass');
require('@babel/runtime/helpers/classCallCheck');
require('../_chunks/dep-e2298443.js');
require('./consts/index.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _defineProperty__default = /*#__PURE__*/_interopDefaultLegacy(_defineProperty);
var _toConsumableArray__default = /*#__PURE__*/_interopDefaultLegacy(_toConsumableArray);
var dayjs__default = /*#__PURE__*/_interopDefaultLegacy(dayjs);

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty__default["default"](e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
var clickTypeEmitEventMap = {
  click: "click",
  dblclick: "dblclick",
  contextmenu: "rightclick"
};
var CalendarCellItem = Vue.defineComponent({
  name: "TCalendarCell",
  inheritAttrs: false,
  props: {
    item: {
      type: Object,
      "default": function _default() {
        return null;
      }
    },
    fillWithZero: {
      type: Boolean,
      "default": void 0
    },
    theme: {
      type: String,
      "default": function _default() {
        return null;
      }
    },
    t: Function,
    global: Object,
    cell: [String, Function],
    cellAppend: [String, Function]
  },
  emits: _toConsumableArray__default["default"](Object.values(clickTypeEmitEventMap)),
  setup: function setup(props, _ref) {
    var emit = _ref.emit;
    var renderContent = index.useContent();
    var cls = calendar_hooks_useCalendarClass.useCalendarCellClass();
    var _useCommonClassName = index$1.useCommonClassName(),
      STATUS = _useCommonClassName.STATUS;
    var valueDisplay = Vue.computed(function () {
      if (props.item.mode === "month") {
        var _ref2, _props$fillWithZero;
        var dateNum = props.item.date.getDate();
        var fillZero = dateNum < 10 && ((_ref2 = (_props$fillWithZero = props.fillWithZero) !== null && _props$fillWithZero !== void 0 ? _props$fillWithZero : props.global.fillWithZero) !== null && _ref2 !== void 0 ? _ref2 : true);
        return fillZero ? "0".concat(dateNum) : dateNum;
      }
      var map = props.t(props.global.cellMonth).split(",");
      return map[props.item.date.getMonth().toString()];
    });
    var allowSlot = Vue.computed(function () {
      return props.theme === "full";
    });
    var disabled = Vue.computed(function () {
      return props.item.mode === "month" && props.item.belongTo !== 0;
    });
    var cellCls = Vue.computed(function () {
      var _props$item = props.item,
        mode = _props$item.mode,
        date = _props$item.date,
        formattedDate = _props$item.formattedDate,
        isCurrent = _props$item.isCurrent;
      var now = new Date();
      var isNow = mode === "year" ? now.getMonth() === date.getMonth() && now.getFullYear() === date.getFullYear() : formattedDate === dayjs__default["default"]().format("YYYY-MM-DD");
      return [cls.tableBodyCell.value, _defineProperty__default["default"](_defineProperty__default["default"](_defineProperty__default["default"]({}, STATUS.value.disabled, disabled.value), STATUS.value.checked, isCurrent), cls.tableBodyCell4Now.value, isNow)];
    });
    var clickCell = function clickCell(e) {
      if (disabled.value) return;
      var emitName = clickTypeEmitEventMap[e.type];
      emit(emitName, e);
    };
    var renderDefaultNode = function renderDefaultNode() {
      return Vue.createVNode(Vue.Fragment, null, [Vue.createVNode("div", {
        "class": cls.tableBodyCellDisplay.value
      }, [valueDisplay.value]), Vue.createVNode("div", {
        "class": cls.tableBodyCellCsontent.value
      }, [allowSlot.value && renderContent("cellAppend", void 0, {
        params: _objectSpread({}, props.item)
      })])]);
    };
    return function () {
      return props.item && Vue.createVNode("td", {
        "class": cellCls.value,
        "onClick": clickCell,
        "onDblclick": clickCell,
        "onContextmenu": clickCell
      }, [renderContent("cell", void 0, {
        defaultNode: renderDefaultNode(),
        params: _objectSpread({}, props.item)
      })]);
    };
  }
});

exports["default"] = CalendarCellItem;
//# sourceMappingURL=calendar-cell.js.map
