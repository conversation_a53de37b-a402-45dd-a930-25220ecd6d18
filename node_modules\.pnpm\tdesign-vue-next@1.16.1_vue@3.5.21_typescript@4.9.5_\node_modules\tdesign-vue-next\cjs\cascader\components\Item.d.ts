import { PropType } from 'vue';
import { CascaderContextType, TreeNode, TdCascaderProps } from '../types';
declare const _default: import("vue").DefineComponent<{
    node: {
        type: PropType<TreeNode>;
        default(): {};
    };
    optionChild: {
        type: PropType<TdCascaderProps["option"]>;
    };
    cascaderContext: {
        type: PropType<CascaderContextType>;
    };
    onChange: PropType<() => void>;
    onClick: PropType<() => void>;
    onMouseenter: PropType<() => void>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    node: {
        type: PropType<TreeNode>;
        default(): {};
    };
    optionChild: {
        type: PropType<TdCascaderProps["option"]>;
    };
    cascaderContext: {
        type: PropType<CascaderContextType>;
    };
    onChange: PropType<() => void>;
    onClick: PropType<() => void>;
    onMouseenter: PropType<() => void>;
}>>, {
    node: TreeNode;
}, {}>;
export default _default;
