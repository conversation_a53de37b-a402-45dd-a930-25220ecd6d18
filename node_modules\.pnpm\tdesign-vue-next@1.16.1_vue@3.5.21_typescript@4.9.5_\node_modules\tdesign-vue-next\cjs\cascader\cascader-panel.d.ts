declare const _default: import("vue").DefineComponent<{
    autofocus: BooleanConstructor;
    borderless: BooleanConstructor;
    checkProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["checkProps"]>;
    };
    checkStrictly: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: import("vue").PropType<import("./type").TdCascaderProps["collapsedItems"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    empty: {
        type: import("vue").PropType<import("./type").TdCascaderProps["empty"]>;
    };
    filter: {
        type: import("vue").PropType<import("./type").TdCascaderProps["filter"]>;
    };
    filterable: BooleanConstructor;
    inputProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["inputProps"]>;
    };
    keys: {
        type: import("vue").PropType<import("./type").TdCascaderProps["keys"]>;
    };
    label: {
        type: import("vue").PropType<import("./type").TdCascaderProps["label"]>;
    };
    lazy: {
        type: BooleanConstructor;
        default: boolean;
    };
    load: {
        type: import("vue").PropType<import("./type").TdCascaderProps["load"]>;
    };
    loading: BooleanConstructor;
    loadingText: {
        type: import("vue").PropType<import("./type").TdCascaderProps["loadingText"]>;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    option: {
        type: import("vue").PropType<import("./type").TdCascaderProps["option"]>;
    };
    options: {
        type: import("vue").PropType<import("./type").TdCascaderProps["options"]>;
        default: () => import("./type").TdCascaderProps["options"];
    };
    panelBottomContent: {
        type: import("vue").PropType<import("./type").TdCascaderProps["panelBottomContent"]>;
    };
    panelTopContent: {
        type: import("vue").PropType<import("./type").TdCascaderProps["panelTopContent"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["popupProps"]>;
    };
    popupVisible: BooleanConstructor;
    prefixIcon: {
        type: import("vue").PropType<import("./type").TdCascaderProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    reserveKeyword: BooleanConstructor;
    selectInputProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["selectInputProps"]>;
    };
    showAllLevels: {
        type: BooleanConstructor;
        default: boolean;
    };
    size: {
        type: import("vue").PropType<import("./type").TdCascaderProps["size"]>;
        default: import("./type").TdCascaderProps["size"];
        validator(val: import("./type").TdCascaderProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<import("./type").TdCascaderProps["status"]>;
        default: import("./type").TdCascaderProps["status"];
        validator(val: import("./type").TdCascaderProps["status"]): boolean;
    };
    suffix: {
        type: import("vue").PropType<import("./type").TdCascaderProps["suffix"]>;
    };
    suffixIcon: {
        type: import("vue").PropType<import("./type").TdCascaderProps["suffixIcon"]>;
    };
    tagInputProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["tagInputProps"]>;
    };
    tagProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["tagProps"]>;
    };
    tips: {
        type: import("vue").PropType<import("./type").TdCascaderProps["tips"]>;
    };
    trigger: {
        type: import("vue").PropType<import("./type").TdCascaderProps["trigger"]>;
        default: import("./type").TdCascaderProps["trigger"];
        validator(val: import("./type").TdCascaderProps["trigger"]): boolean;
    };
    value: {
        type: import("vue").PropType<import("./type").TdCascaderProps["value"]>;
        default: import("./type").TdCascaderProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdCascaderProps["value"]>;
        default: import("./type").TdCascaderProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdCascaderProps["defaultValue"]>;
        default: () => import("./type").TdCascaderProps["defaultValue"];
    };
    valueDisplay: {
        type: import("vue").PropType<import("./type").TdCascaderProps["valueDisplay"]>;
    };
    valueMode: {
        type: import("vue").PropType<import("./type").TdCascaderProps["valueMode"]>;
        default: import("./type").TdCascaderProps["valueMode"];
        validator(val: import("./type").TdCascaderProps["valueMode"]): boolean;
    };
    valueType: {
        type: import("vue").PropType<import("./type").TdCascaderProps["valueType"]>;
        default: import("./type").TdCascaderProps["valueType"];
        validator(val: import("./type").TdCascaderProps["valueType"]): boolean;
    };
    onBlur: import("vue").PropType<import("./type").TdCascaderProps["onBlur"]>;
    onChange: import("vue").PropType<import("./type").TdCascaderProps["onChange"]>;
    onFocus: import("vue").PropType<import("./type").TdCascaderProps["onFocus"]>;
    onPopupVisibleChange: import("vue").PropType<import("./type").TdCascaderProps["onPopupVisibleChange"]>;
    onRemove: import("vue").PropType<import("./type").TdCascaderProps["onRemove"]>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    autofocus: BooleanConstructor;
    borderless: BooleanConstructor;
    checkProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["checkProps"]>;
    };
    checkStrictly: BooleanConstructor;
    clearable: BooleanConstructor;
    collapsedItems: {
        type: import("vue").PropType<import("./type").TdCascaderProps["collapsedItems"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    empty: {
        type: import("vue").PropType<import("./type").TdCascaderProps["empty"]>;
    };
    filter: {
        type: import("vue").PropType<import("./type").TdCascaderProps["filter"]>;
    };
    filterable: BooleanConstructor;
    inputProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["inputProps"]>;
    };
    keys: {
        type: import("vue").PropType<import("./type").TdCascaderProps["keys"]>;
    };
    label: {
        type: import("vue").PropType<import("./type").TdCascaderProps["label"]>;
    };
    lazy: {
        type: BooleanConstructor;
        default: boolean;
    };
    load: {
        type: import("vue").PropType<import("./type").TdCascaderProps["load"]>;
    };
    loading: BooleanConstructor;
    loadingText: {
        type: import("vue").PropType<import("./type").TdCascaderProps["loadingText"]>;
    };
    max: {
        type: NumberConstructor;
        default: number;
    };
    minCollapsedNum: {
        type: NumberConstructor;
        default: number;
    };
    multiple: BooleanConstructor;
    option: {
        type: import("vue").PropType<import("./type").TdCascaderProps["option"]>;
    };
    options: {
        type: import("vue").PropType<import("./type").TdCascaderProps["options"]>;
        default: () => import("./type").TdCascaderProps["options"];
    };
    panelBottomContent: {
        type: import("vue").PropType<import("./type").TdCascaderProps["panelBottomContent"]>;
    };
    panelTopContent: {
        type: import("vue").PropType<import("./type").TdCascaderProps["panelTopContent"]>;
    };
    placeholder: {
        type: StringConstructor;
        default: any;
    };
    popupProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["popupProps"]>;
    };
    popupVisible: BooleanConstructor;
    prefixIcon: {
        type: import("vue").PropType<import("./type").TdCascaderProps["prefixIcon"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    reserveKeyword: BooleanConstructor;
    selectInputProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["selectInputProps"]>;
    };
    showAllLevels: {
        type: BooleanConstructor;
        default: boolean;
    };
    size: {
        type: import("vue").PropType<import("./type").TdCascaderProps["size"]>;
        default: import("./type").TdCascaderProps["size"];
        validator(val: import("./type").TdCascaderProps["size"]): boolean;
    };
    status: {
        type: import("vue").PropType<import("./type").TdCascaderProps["status"]>;
        default: import("./type").TdCascaderProps["status"];
        validator(val: import("./type").TdCascaderProps["status"]): boolean;
    };
    suffix: {
        type: import("vue").PropType<import("./type").TdCascaderProps["suffix"]>;
    };
    suffixIcon: {
        type: import("vue").PropType<import("./type").TdCascaderProps["suffixIcon"]>;
    };
    tagInputProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["tagInputProps"]>;
    };
    tagProps: {
        type: import("vue").PropType<import("./type").TdCascaderProps["tagProps"]>;
    };
    tips: {
        type: import("vue").PropType<import("./type").TdCascaderProps["tips"]>;
    };
    trigger: {
        type: import("vue").PropType<import("./type").TdCascaderProps["trigger"]>;
        default: import("./type").TdCascaderProps["trigger"];
        validator(val: import("./type").TdCascaderProps["trigger"]): boolean;
    };
    value: {
        type: import("vue").PropType<import("./type").TdCascaderProps["value"]>;
        default: import("./type").TdCascaderProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<import("./type").TdCascaderProps["value"]>;
        default: import("./type").TdCascaderProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<import("./type").TdCascaderProps["defaultValue"]>;
        default: () => import("./type").TdCascaderProps["defaultValue"];
    };
    valueDisplay: {
        type: import("vue").PropType<import("./type").TdCascaderProps["valueDisplay"]>;
    };
    valueMode: {
        type: import("vue").PropType<import("./type").TdCascaderProps["valueMode"]>;
        default: import("./type").TdCascaderProps["valueMode"];
        validator(val: import("./type").TdCascaderProps["valueMode"]): boolean;
    };
    valueType: {
        type: import("vue").PropType<import("./type").TdCascaderProps["valueType"]>;
        default: import("./type").TdCascaderProps["valueType"];
        validator(val: import("./type").TdCascaderProps["valueType"]): boolean;
    };
    onBlur: import("vue").PropType<import("./type").TdCascaderProps["onBlur"]>;
    onChange: import("vue").PropType<import("./type").TdCascaderProps["onChange"]>;
    onFocus: import("vue").PropType<import("./type").TdCascaderProps["onFocus"]>;
    onPopupVisibleChange: import("vue").PropType<import("./type").TdCascaderProps["onPopupVisibleChange"]>;
    onRemove: import("vue").PropType<import("./type").TdCascaderProps["onRemove"]>;
}>>, {
    disabled: boolean;
    options: import("..").TreeOptionData[];
    value: string | number | import("..").TreeOptionData | import("./type").CascaderValue<import("..").TreeOptionData>[];
    loading: boolean;
    valueType: "single" | "full";
    multiple: boolean;
    max: number;
    size: import("..").SizeEnum;
    status: "error" | "default" | "success" | "warning";
    checkStrictly: boolean;
    lazy: boolean;
    valueMode: "all" | "parentFirst" | "onlyLeaf";
    defaultValue: import("./type").CascaderValue<import("..").TreeOptionData>;
    placeholder: string;
    modelValue: string | number | import("..").TreeOptionData | import("./type").CascaderValue<import("..").TreeOptionData>[];
    readonly: boolean;
    trigger: "click" | "hover";
    autofocus: boolean;
    borderless: boolean;
    clearable: boolean;
    minCollapsedNum: number;
    popupVisible: boolean;
    reserveKeyword: boolean;
    filterable: boolean;
    showAllLevels: boolean;
}, {}>;
export default _default;
