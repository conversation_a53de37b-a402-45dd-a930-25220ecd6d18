/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var Vue = require('vue');
var tdesignIconsVueNext = require('tdesign-icons-vue-next');
var cascader_utils_className = require('../utils/className.js');
require('@babel/runtime/helpers/slicedToArray');
var cascader_utils_helper = require('../utils/helper.js');
var checkbox_index = require('../../checkbox/index.js');
var loading_index = require('../../loading/index.js');
require('@babel/runtime/helpers/toConsumableArray');
require('@babel/runtime/helpers/typeof');
require('../../_chunks/dep-086ab407.js');
var index$3 = require('../../_chunks/dep-6666de7f.js');
var index$1 = require('../../_chunks/dep-e2298443.js');
var index$2 = require('../../_chunks/dep-cbaee605.js');
require('../../_chunks/dep-b15ee9eb.js');
var index = require('../../_chunks/dep-988704d0.js');
require('@babel/runtime/helpers/defineProperty');
require('../../_chunks/dep-12e0aded.js');
require('../../_chunks/dep-ca39ce6d.js');
require('../../_chunks/dep-8c00290f.js');
require('../../_chunks/dep-febae5f4.js');
require('../../_chunks/dep-3f3d49e4.js');
require('../../_chunks/dep-ba5948c9.js');
require('../../_chunks/dep-53dbb954.js');
require('../../_chunks/dep-6a71c082.js');
require('../../_chunks/dep-ef3df7aa.js');
require('../../_chunks/dep-94424a57.js');
require('../../_chunks/dep-df581fcb.js');
require('../../_chunks/dep-6b54b4a5.js');
require('../../_chunks/dep-d1c7139a.js');
require('../../checkbox/checkbox.js');
require('../../checkbox/props.js');
require('../../_chunks/dep-059461d7.js');
require('../../_chunks/dep-2748f688.js');
require('../../_chunks/dep-914897c8.js');
require('../../_chunks/dep-6ae67bab.js');
require('../../_chunks/dep-324af0df.js');
require('../../_chunks/dep-cabb6240.js');
require('../../_chunks/dep-306b2e72.js');
require('../../_chunks/dep-76847e8b.js');
require('../../_chunks/dep-f8224a9c.js');
require('../../_chunks/dep-3bb82c67.js');
require('../../_chunks/dep-7c14108f.js');
require('../../_chunks/dep-b09565a1.js');
require('../../_chunks/dep-756628ef.js');
require('../../_chunks/dep-7e5fc00e.js');
require('../../_chunks/dep-baa72039.js');
require('../../checkbox/consts/index.js');
require('../../checkbox/hooks/useCheckboxLazyLoad.js');
require('../../_chunks/dep-c9fd0e5d.js');
require('../../checkbox/hooks/useKeyboardEvent.js');
require('../../_chunks/dep-01642c17.js');
require('../../_chunks/dep-d5654a4e.js');
require('../../config-provider/hooks/useConfig.js');
require('../../_chunks/dep-f9e26c60.js');
require('../../_chunks/dep-714d992c.js');
require('dayjs');
require('../../_chunks/dep-94ff6543.js');
require('../../_chunks/dep-fa8a400f.js');
require('../../_chunks/dep-eac47ca0.js');
require('../../_chunks/dep-6c0887f7.js');
require('../../_chunks/dep-8c0a3845.js');
require('../../_chunks/dep-4c75812c.js');
require('../../_chunks/dep-ca4c3e97.js');
require('../../_chunks/dep-6183bb4a.js');
require('../../_chunks/dep-ba035735.js');
require('@babel/runtime/helpers/createClass');
require('@babel/runtime/helpers/classCallCheck');
require('../../_chunks/dep-076bd726.js');
require('../../checkbox/group.js');
require('../../checkbox/checkbox-group-props.js');
require('../../_chunks/dep-872d0152.js');
require('../../_chunks/dep-cbee8f46.js');
require('../../_chunks/dep-59fa52ec.js');
require('../../_chunks/dep-a271f384.js');
require('../../_chunks/dep-2105b21f.js');
require('../../_chunks/dep-ac7dea19.js');
require('../../_chunks/dep-24c31f97.js');
require('../../loading/plugin.js');
require('../../_chunks/dep-faade8bd.js');
require('../../loading/icon/gradient.js');
require('../../_chunks/dep-fd4183e8.js');
require('@babel/runtime/helpers/objectWithoutProperties');
require('../../_chunks/dep-f20572a3.js');
require('../../loading/props.js');
require('../../_chunks/dep-605cf2e8.js');
require('../../_chunks/dep-925e8207.js');
require('../../_chunks/dep-66442631.js');
require('../../_chunks/dep-156361a6.js');
require('../../_chunks/dep-6d27c874.js');
require('../../_chunks/dep-31eb9b48.js');
require('../../_chunks/dep-ce7457dd.js');
require('../../_chunks/dep-4023d837.js');

function _isSlot(s) {
  return typeof s === 'function' || Object.prototype.toString.call(s) === '[object Object]' && !Vue.isVNode(s);
}
var props = {
  node: {
    type: Object,
    "default": function _default() {
      return {};
    }
  },
  optionChild: {
    type: [Object, Array]
  },
  cascaderContext: {
    type: Object
  },
  onChange: Function,
  onClick: Function,
  onMouseenter: Function
};
var Item = Vue.defineComponent({
  name: "TCascaderItem",
  props: props,
  setup: function setup(props2) {
    var liRef = Vue.ref();
    index.useRipple(liRef);
    var COMPONENT_NAME = index$1.usePrefixClass("cascader__item");
    var classPrefix = index$1.usePrefixClass();
    var _useGlobalIcon = index$2.useGlobalIcon({
        ChevronRightIcon: tdesignIconsVueNext.ChevronRightIcon
      }),
      ChevronRightIcon = _useGlobalIcon.ChevronRightIcon;
    var _useCommonClassName = index$3.useCommonClassName(),
      STATUS = _useCommonClassName.STATUS,
      SIZE = _useCommonClassName.SIZE;
    var itemClass = Vue.computed(function () {
      return cascader_utils_className.getCascaderItemClass(classPrefix.value, props2.node, SIZE.value, STATUS.value, props2.cascaderContext);
    });
    var iconClass = Vue.computed(function () {
      return cascader_utils_className.getCascaderItemIconClass(classPrefix.value, props2.node, STATUS.value, props2.cascaderContext);
    });
    function RenderLabelInner(node, cascaderContext) {
      var inputVal = cascaderContext.inputVal;
      var labelText = inputVal ? cascader_utils_helper.getFullPathLabel(node) : node.label;
      if (inputVal) {
        var texts = labelText.split(inputVal);
        var doms = [];
        for (var index = 0; index < texts.length; index++) {
          doms.push(Vue.createVNode("span", {
            "key": index
          }, [texts[index]]));
          if (index === texts.length - 1) break;
          doms.push(Vue.createVNode("span", {
            "key": "".concat(index, "filter"),
            "class": "".concat(COMPONENT_NAME.value, "-label--filter")
          }, [inputVal]));
        }
        return doms;
      }
      return labelText;
    }
    var renderTitle = function renderTitle(node) {
      if (typeof node.label === "string") return node.label;
      return null;
    };
    function RenderLabelContent(node, cascaderContext) {
      var label = RenderLabelInner(node, cascaderContext);
      var labelCont = Vue.createVNode("span", {
        "title": cascaderContext.inputVal ? cascader_utils_helper.getFullPathLabel(node) : renderTitle(node),
        "class": ["".concat(COMPONENT_NAME.value, "-label"), "".concat(COMPONENT_NAME.value, "-label--ellipsis")],
        "role": "label"
      }, [label]);
      return labelCont;
    }
    function RenderCheckBox(node, cascaderContext) {
      var checkProps = cascaderContext.checkProps,
        value = cascaderContext.value,
        max = cascaderContext.max,
        inputVal = cascaderContext.inputVal;
      var label = RenderLabelInner(node, cascaderContext);
      return Vue.createVNode(checkbox_index.Checkbox, Vue.mergeProps({
        "checked": node.checked,
        "indeterminate": node.indeterminate,
        "disabled": node.isDisabled() || value.length >= max && max !== 0,
        "name": String(node.value),
        "stopLabelTrigger": !!node.children,
        "title": inputVal ? cascader_utils_helper.getFullPathLabel(node) : renderTitle(node),
        "onChange": function onChange() {
          props2.onChange();
        }
      }, checkProps), _isSlot(label) ? label : {
        "default": function _default() {
          return [label];
        }
      });
    }
    return function () {
      var cascaderContext = props2.cascaderContext,
        node = props2.node,
        optionChild = props2.optionChild;
      var isOptionChildAndMultiple = optionChild && cascaderContext.multiple;
      return Vue.createVNode("li", {
        "ref": liRef,
        "class": itemClass.value,
        "onClick": function onClick() {
          return isOptionChildAndMultiple ? props2.onChange() : props2.onClick();
        },
        "onMouseenter": props2.onMouseenter
      }, [optionChild || (cascaderContext.multiple ? RenderCheckBox(node, cascaderContext) : RenderLabelContent(node, cascaderContext)), node.children && (node.loading ? Vue.createVNode(loading_index.Loading, {
        "class": iconClass.value,
        "size": "small"
      }, null) : Vue.createVNode(ChevronRightIcon, {
        "class": iconClass.value
      }, null))]);
    };
  }
});

exports["default"] = Item;
//# sourceMappingURL=Item.js.map
