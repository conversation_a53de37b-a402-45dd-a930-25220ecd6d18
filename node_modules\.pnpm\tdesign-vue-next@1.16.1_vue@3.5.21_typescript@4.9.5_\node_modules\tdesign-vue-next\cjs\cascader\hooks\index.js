/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _slicedToArray = require('@babel/runtime/helpers/slicedToArray');
var _defineProperty = require('@babel/runtime/helpers/defineProperty');
var Vue = require('vue');
var treeStore = require('../../_chunks/dep-ce2ce57a.js');
require('@babel/runtime/helpers/toConsumableArray');
require('@babel/runtime/helpers/typeof');
require('../../_chunks/dep-086ab407.js');
require('../../config-provider/hooks/useConfig.js');
var index$2 = require('../../_chunks/dep-1189d7e7.js');
var index = require('../../_chunks/dep-b09565a1.js');
require('../../_chunks/dep-b15ee9eb.js');
var index$1 = require('../../_chunks/dep-baa72039.js');
var cascader_utils_effect = require('../utils/effect.js');
var cascader_utils_helper = require('../utils/helper.js');
var isFunction = require('../../_chunks/dep-94424a57.js');
var isString = require('../../_chunks/dep-914897c8.js');
var isEqual = require('../../_chunks/dep-605cf2e8.js');
require('@babel/runtime/helpers/classCallCheck');
require('@babel/runtime/helpers/createClass');
require('mitt');
require('../../_chunks/dep-8491f97a.js');
require('@babel/runtime/helpers/asyncToGenerator');
require('@babel/runtime/regenerator');
require('../../_chunks/dep-1e028e1c.js');
require('../../_chunks/dep-756628ef.js');
require('../../_chunks/dep-febae5f4.js');
require('../../_chunks/dep-3f3d49e4.js');
require('../../_chunks/dep-74372b45.js');
require('../../_chunks/dep-cabb6240.js');
require('../../_chunks/dep-12e0aded.js');
require('../../_chunks/dep-306b2e72.js');
require('../../_chunks/dep-6183bb4a.js');
require('../../_chunks/dep-94ff6543.js');
require('../../_chunks/dep-fa8a400f.js');
require('../../_chunks/dep-6b54b4a5.js');
require('../../_chunks/dep-ca39ce6d.js');
require('../../_chunks/dep-eac47ca0.js');
require('../../_chunks/dep-df581fcb.js');
require('../../_chunks/dep-6a71c082.js');
require('../../_chunks/dep-ef3df7aa.js');
require('../../_chunks/dep-6c0887f7.js');
require('../../_chunks/dep-8c0a3845.js');
require('../../_chunks/dep-53dbb954.js');
require('../../_chunks/dep-d1c7139a.js');
require('../../_chunks/dep-ba035735.js');
require('../../_chunks/dep-31eb9b48.js');
require('../../_chunks/dep-b2fa5076.js');
require('../../_chunks/dep-f8224a9c.js');
require('../../_chunks/dep-f9e26c60.js');
require('../../_chunks/dep-714d992c.js');
require('../../_chunks/dep-d5654a4e.js');
require('dayjs');
require('../../_chunks/dep-4c75812c.js');
require('../../_chunks/dep-ca4c3e97.js');
require('../../_chunks/dep-4fe7c57c.js');
require('../../_chunks/dep-6d27c874.js');
require('../../_chunks/dep-2105b21f.js');
require('../../_chunks/dep-8c00290f.js');
require('../../_chunks/dep-f20572a3.js');
require('../../_chunks/dep-a4f1ef30.js');
require('../../_chunks/dep-30cf92fa.js');
require('../../_chunks/dep-59fa52ec.js');
require('../../_chunks/dep-a271f384.js');
require('../../_chunks/dep-324af0df.js');
require('../../_chunks/dep-76847e8b.js');
require('../../_chunks/dep-3bb82c67.js');
require('../../_chunks/dep-7c14108f.js');
require('../../_chunks/dep-ba5948c9.js');
require('../../_chunks/dep-925e8207.js');
require('../../_chunks/dep-66442631.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var _slicedToArray__default = /*#__PURE__*/_interopDefaultLegacy(_slicedToArray);
var _defineProperty__default = /*#__PURE__*/_interopDefaultLegacy(_defineProperty);

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty__default["default"](e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
var useContext = function useContext(props, setInnerValue, innerPopupVisible, setPopupVisible) {
  var statusContext = Vue.reactive({
    treeStore: null,
    inputVal: null,
    scopeVal: void 0,
    treeNodes: [],
    expend: []
  });
  return {
    statusContext: statusContext,
    cascaderContext: Vue.computed(function () {
      var size = props.size,
        checkStrictly = props.checkStrictly,
        lazy = props.lazy,
        multiple = props.multiple,
        filterable = props.filterable,
        clearable = props.clearable,
        checkProps = props.checkProps,
        max = props.max,
        disabled = props.disabled,
        showAllLevels = props.showAllLevels,
        minCollapsedNum = props.minCollapsedNum,
        valueType = props.valueType,
        modelValue = props.modelValue;
      return _objectSpread(_objectSpread({
        value: statusContext.scopeVal,
        size: size,
        checkStrictly: checkStrictly,
        lazy: lazy,
        multiple: multiple,
        filterable: filterable,
        clearable: clearable,
        checkProps: checkProps,
        max: max,
        disabled: disabled,
        showAllLevels: showAllLevels,
        minCollapsedNum: minCollapsedNum,
        valueType: valueType,
        visible: innerPopupVisible.value
      }, statusContext), {}, {
        setTreeNodes: function setTreeNodes(nodes) {
          statusContext.treeNodes = nodes;
        },
        setValue: function setValue(val, source, node) {
          if (isEqual.isEqual(val, modelValue)) return;
          setInnerValue(val, {
            source: source,
            node: node
          });
        },
        setVisible: setPopupVisible,
        setInputVal: function setInputVal(val) {
          statusContext.inputVal = val;
        },
        setExpend: function setExpend(val) {
          statusContext.expend = val;
        }
      });
    })
  };
};
var useCascaderContext = function useCascaderContext(props) {
  var disabled = index.useDisabled();
  var _toRefs = Vue.toRefs(props),
    value = _toRefs.value,
    modelValue = _toRefs.modelValue,
    popupVisible = _toRefs.popupVisible;
  var _useVModel = index$1.useVModel(value, modelValue, props.defaultValue, props.onChange),
    _useVModel2 = _slicedToArray__default["default"](_useVModel, 2),
    innerValue = _useVModel2[0],
    setInnerValue = _useVModel2[1];
  var _useDefaultValue = index$2.useDefaultValue(popupVisible, false, props.onPopupVisibleChange, "popupVisible"),
    _useDefaultValue2 = _slicedToArray__default["default"](_useDefaultValue, 2),
    innerPopupVisible = _useDefaultValue2[0],
    setPopupVisible = _useDefaultValue2[1];
  var _useContext = useContext(props, setInnerValue, innerPopupVisible, setPopupVisible),
    cascaderContext = _useContext.cascaderContext,
    statusContext = _useContext.statusContext;
  var isFilterable = Vue.computed(function () {
    return Boolean(props.filterable || isFunction.isFunction(props.filter));
  });
  var updatedTreeNodes = function updatedTreeNodes() {
    var _cascaderContext$valu = cascaderContext.value,
      inputVal = _cascaderContext$valu.inputVal,
      treeStore = _cascaderContext$valu.treeStore,
      setTreeNodes = _cascaderContext$valu.setTreeNodes;
    cascader_utils_effect.treeNodesEffect(inputVal, treeStore, setTreeNodes, props.filter);
  };
  var updateExpend = function updateExpend() {
    var _cascaderContext$valu2 = cascaderContext.value,
      value2 = _cascaderContext$valu2.value,
      treeStore = _cascaderContext$valu2.treeStore;
    var expend = statusContext.expend;
    cascader_utils_effect.treeStoreExpendEffect(treeStore, value2, expend);
    treeStore.replaceChecked(cascader_utils_helper.getTreeValue(value2));
  };
  Vue.watch(function () {
    return props.options;
  }, function () {
    var options = props.options,
      _props$keys = props.keys,
      keys = _props$keys === void 0 ? {} : _props$keys,
      checkStrictly = props.checkStrictly,
      lazy = props.lazy,
      load = props.load,
      valueMode = props.valueMode;
    var treeStore$1 = statusContext.treeStore;
    if (!options.length && !treeStore$1) return;
    if (!treeStore$1) {
      var store = new treeStore.TreeStore({
        keys: _objectSpread(_objectSpread({}, keys), {}, {
          children: isString.isString(keys.children) ? keys.children : "children"
        }),
        checkable: true,
        expandMutex: true,
        expandParent: true,
        lazy: lazy,
        load: load,
        valueMode: valueMode,
        checkStrictly: checkStrictly,
        onLoad: function onLoad() {
          Vue.nextTick(function () {
            store.refreshNodes();
            updatedTreeNodes();
          });
        }
      });
      store.append(options);
      statusContext.treeStore = store;
    } else {
      treeStore$1.reload(options);
      treeStore$1.refreshNodes();
    }
    updateExpend();
    updatedTreeNodes();
  }, {
    immediate: true,
    deep: true
  });
  Vue.watch(function () {
    var checkStrictly = props.checkStrictly,
      lazy = props.lazy,
      load = props.load,
      valueMode = props.valueMode;
    return JSON.stringify({
      valueMode: valueMode,
      checkStrictly: checkStrictly,
      lazy: lazy,
      load: load
    });
  }, function () {
    var treeStore = statusContext.treeStore;
    if (!treeStore) return;
    var checkStrictly = props.checkStrictly,
      lazy = props.lazy,
      load = props.load,
      valueMode = props.valueMode;
    var treeProps = {
      checkStrictly: checkStrictly,
      disabled: disabled,
      load: load,
      lazy: lazy,
      valueMode: valueMode
    };
    treeStore.setConfig(treeProps);
  }, {
    immediate: true
  });
  Vue.watch(innerValue, function () {
    var _cascaderContext$valu3 = cascaderContext.value,
      setValue = _cascaderContext$valu3.setValue,
      multiple = _cascaderContext$valu3.multiple,
      valueType = _cascaderContext$valu3.valueType;
    if (cascader_utils_helper.isValueInvalid(innerValue.value, cascaderContext.value)) {
      setValue(multiple ? [] : "", "invalid-value");
    }
    if (!cascader_utils_helper.isEmptyValues(innerValue.value)) {
      statusContext.scopeVal = cascader_utils_helper.getCascaderValue(innerValue.value, valueType, multiple);
    } else {
      statusContext.scopeVal = multiple ? [] : "";
    }
    if (!statusContext.treeStore) return;
    updateExpend();
    updatedTreeNodes();
  }, {
    immediate: true
  });
  Vue.watch(function () {
    return innerPopupVisible.value && isFilterable.value;
  }, function (visible) {
    var setInputVal = cascaderContext.value.setInputVal;
    if (visible) {
      setInputVal("");
    }
  });
  Vue.watch(function () {
    return statusContext.inputVal;
  }, function () {
    updatedTreeNodes();
  });
  var getCascaderItems = function getCascaderItems(arrValue) {
    var options = [];
    arrValue.forEach(function (value2) {
      var _statusContext$treeSt;
      var nodes = (_statusContext$treeSt = statusContext.treeStore) === null || _statusContext$treeSt === void 0 ? void 0 : _statusContext$treeSt.getNodes(value2);
      nodes && nodes[0] && options.push(nodes[0].data);
    });
    return options;
  };
  return {
    cascaderContext: cascaderContext,
    isFilterable: isFilterable,
    innerValue: innerValue,
    getCascaderItems: getCascaderItems
  };
};

exports.useCascaderContext = useCascaderContext;
exports.useContext = useContext;
//# sourceMappingURL=index.js.map
