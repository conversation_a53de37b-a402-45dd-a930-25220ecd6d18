import { TdCalendarProps } from './type';
import { PropType } from 'vue';
declare const _default: {
    cell: {
        type: PropType<TdCalendarProps["cell"]>;
    };
    cellAppend: {
        type: PropType<TdCalendarProps["cellAppend"]>;
    };
    controllerConfig: {
        type: PropType<TdCalendarProps["controllerConfig"]>;
        default: TdCalendarProps["controllerConfig"];
    };
    fillWithZero: {
        type: BooleanConstructor;
        default: any;
    };
    firstDayOfWeek: {
        type: NumberConstructor;
        validator(val: TdCalendarProps["firstDayOfWeek"]): boolean;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    head: {
        type: PropType<TdCalendarProps["head"]>;
    };
    isShowWeekendDefault: {
        type: BooleanConstructor;
        default: boolean;
    };
    mode: {
        type: PropType<TdCalendarProps["mode"]>;
        default: TdCalendarProps["mode"];
        validator(val: TdCalendarProps["mode"]): boolean;
    };
    month: {
        type: PropType<TdCalendarProps["month"]>;
    };
    multiple: BooleanConstructor;
    preventCellContextmenu: BooleanConstructor;
    range: {
        type: PropType<TdCalendarProps["range"]>;
    };
    theme: {
        type: PropType<TdCalendarProps["theme"]>;
        default: TdCalendarProps["theme"];
        validator(val: TdCalendarProps["theme"]): boolean;
    };
    value: {
        type: PropType<TdCalendarProps["value"]>;
    };
    week: {
        type: PropType<TdCalendarProps["week"]>;
    };
    year: {
        type: PropType<TdCalendarProps["year"]>;
    };
    onCellClick: PropType<TdCalendarProps["onCellClick"]>;
    onCellDoubleClick: PropType<TdCalendarProps["onCellDoubleClick"]>;
    onCellRightClick: PropType<TdCalendarProps["onCellRightClick"]>;
    onControllerChange: PropType<TdCalendarProps["onControllerChange"]>;
    onMonthChange: PropType<TdCalendarProps["onMonthChange"]>;
};
export default _default;
