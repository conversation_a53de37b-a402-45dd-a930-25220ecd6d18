{"version": 3, "file": "index.js", "sources": ["../../../../components/cascader/hooks/index.ts"], "sourcesContent": ["import { Ref, reactive, computed, toRefs, watch, nextTick } from 'vue';\nimport { isEqual, isString, isFunction } from 'lodash-es';\n\nimport TreeStore from '@tdesign/common-js/tree/tree-store';\nimport { useVModel, useDisabled, useDefaultValue } from '@tdesign/shared-hooks';\n\nimport {\n  getTreeValue,\n  getCascaderValue,\n  isEmptyValues,\n  isValueInvalid,\n  treeNodesEffect,\n  treeStoreExpendEffect,\n} from '../utils';\n\nimport {\n  TreeNode,\n  TreeNodeValue,\n  TdCascaderProps,\n  TreeNodeModel,\n  CascaderChangeSource,\n  CascaderValue,\n  TreeOptionData,\n} from '../types';\n\n// 全局状态\nexport const useContext = (\n  props: TdCascaderProps,\n  setInnerValue: TdCascaderProps['onChange'],\n  innerPopupVisible: Ref<TdCascaderProps['popupVisible']>,\n  setPopupVisible: TdCascaderProps['onPopupVisibleChange'],\n) => {\n  const statusContext = reactive({\n    treeStore: null,\n    inputVal: null,\n    scopeVal: undefined,\n    treeNodes: [],\n    expend: [],\n  });\n\n  return {\n    statusContext,\n    cascaderContext: computed(() => {\n      const {\n        size,\n        checkStrictly,\n        lazy,\n        multiple,\n        filterable,\n        clearable,\n        checkProps,\n        max,\n        disabled,\n        showAllLevels,\n        minCollapsedNum,\n        valueType,\n        modelValue,\n      } = props;\n      return {\n        value: statusContext.scopeVal,\n        size,\n        checkStrictly,\n        lazy,\n        multiple,\n        filterable,\n        clearable,\n        checkProps,\n        max,\n        disabled,\n        showAllLevels,\n        minCollapsedNum,\n        valueType,\n        visible: innerPopupVisible.value,\n        ...statusContext,\n        setTreeNodes: (nodes: TreeNode[]) => {\n          statusContext.treeNodes = nodes;\n        },\n        setValue: (val: CascaderValue, source: CascaderChangeSource, node?: TreeNodeModel) => {\n          if (isEqual(val, modelValue)) return;\n          setInnerValue(val, { source, node });\n        },\n        setVisible: setPopupVisible,\n        setInputVal: (val: string) => {\n          statusContext.inputVal = val;\n        },\n        setExpend: (val: TreeNodeValue[]) => {\n          statusContext.expend = val;\n        },\n      };\n    }),\n  };\n};\n\n// 内聚组件核心的副作用与状态处理\nexport const useCascaderContext = (props: TdCascaderProps) => {\n  const disabled = useDisabled();\n  const { value, modelValue, popupVisible } = toRefs(props);\n  const [innerValue, setInnerValue] = useVModel(value, modelValue, props.defaultValue, props.onChange);\n  const [innerPopupVisible, setPopupVisible] = useDefaultValue(\n    popupVisible,\n    false,\n    props.onPopupVisibleChange,\n    'popupVisible',\n  );\n  const { cascaderContext, statusContext } = useContext(props, setInnerValue, innerPopupVisible, setPopupVisible);\n\n  const isFilterable = computed(() => {\n    return Boolean(props.filterable || isFunction(props.filter));\n  });\n\n  // 更新treeNodes\n  const updatedTreeNodes = () => {\n    const { inputVal, treeStore, setTreeNodes } = cascaderContext.value;\n    treeNodesEffect(inputVal, treeStore, setTreeNodes, props.filter);\n  };\n\n  // 更新节点展开状态\n  const updateExpend = () => {\n    const { value, treeStore } = cascaderContext.value;\n    const { expend } = statusContext;\n    treeStoreExpendEffect(treeStore, value, expend);\n    treeStore.replaceChecked(getTreeValue(value));\n  };\n\n  watch(\n    () => props.options,\n    () => {\n      const { options, keys = {}, checkStrictly, lazy, load, valueMode } = props;\n      const { treeStore } = statusContext;\n\n      if (!options.length && !treeStore) return;\n\n      if (!treeStore) {\n        const store = new TreeStore({\n          keys: {\n            ...keys,\n            children: isString(keys.children) ? keys.children : 'children',\n          },\n          checkable: true,\n          expandMutex: true,\n          expandParent: true,\n          lazy,\n          load,\n          valueMode,\n          checkStrictly,\n          onLoad: () => {\n            nextTick(() => {\n              store.refreshNodes();\n              updatedTreeNodes();\n            });\n          },\n        });\n        store.append(options);\n        statusContext.treeStore = store;\n      } else {\n        treeStore.reload(options);\n        treeStore.refreshNodes();\n      }\n      updateExpend();\n      updatedTreeNodes();\n    },\n    { immediate: true, deep: true },\n  );\n\n  // tree插件配置变化\n  watch(\n    () => {\n      const { checkStrictly, lazy, load, valueMode } = props;\n      return JSON.stringify({\n        valueMode,\n        checkStrictly,\n        lazy,\n        load,\n      });\n    },\n    () => {\n      const { treeStore } = statusContext;\n      if (!treeStore) return;\n      const { checkStrictly, lazy, load, valueMode } = props;\n      const treeProps = {\n        checkStrictly,\n        disabled,\n        load,\n        lazy,\n        valueMode,\n      };\n      treeStore.setConfig(treeProps);\n    },\n    { immediate: true },\n  );\n\n  watch(\n    innerValue,\n    () => {\n      // 初始化判断 value 逻辑\n      const { setValue, multiple, valueType } = cascaderContext.value;\n\n      if (isValueInvalid(innerValue.value, cascaderContext.value)) {\n        setValue(multiple ? [] : '', 'invalid-value');\n      }\n\n      if (!isEmptyValues(innerValue.value)) {\n        statusContext.scopeVal = getCascaderValue(innerValue.value, valueType, multiple);\n      } else {\n        statusContext.scopeVal = multiple ? [] : '';\n      }\n\n      if (!statusContext.treeStore) return;\n      updateExpend();\n      updatedTreeNodes();\n    },\n    { immediate: true },\n  );\n\n  watch(\n    () => innerPopupVisible.value && isFilterable.value,\n    (visible) => {\n      const { setInputVal } = cascaderContext.value;\n      if (visible) {\n        setInputVal('');\n      }\n    },\n  );\n\n  watch(\n    () => statusContext.inputVal,\n    () => {\n      updatedTreeNodes();\n    },\n  );\n\n  const getCascaderItems = (arrValue: CascaderValue[]) => {\n    const options: TreeOptionData[] = [];\n    arrValue.forEach((value) => {\n      const nodes = statusContext.treeStore?.getNodes(value);\n      nodes && nodes[0] && options.push(nodes[0].data);\n    });\n    return options;\n  };\n\n  return {\n    cascaderContext,\n    isFilterable,\n    innerValue,\n    getCascaderItems,\n  };\n};\n"], "names": ["useContext", "props", "setInnerValue", "innerPopupVisible", "setPopupVisible", "statusContext", "reactive", "treeStore", "inputVal", "scopeVal", "treeNodes", "expend", "cascaderContext", "computed", "size", "checkStrictly", "lazy", "multiple", "filterable", "clearable", "checkProps", "max", "disabled", "showAllLevels", "minCollapsedNum", "valueType", "modelValue", "_objectSpread", "value", "visible", "setTreeNodes", "nodes", "setValue", "val", "source", "node", "isEqual", "setVisible", "setInputVal", "setExpend", "useCascaderContext", "useDisabled", "_toRefs", "toRefs", "popupVisible", "_useVModel", "useVModel", "defaultValue", "onChange", "_useVModel2", "_slicedToArray", "innerValue", "_useDefaultValue", "useDefaultValue", "onPopupVisibleChange", "_useDefaultValue2", "_useContext", "isFilterable", "Boolean", "isFunction", "filter", "updatedTreeNodes", "_cascaderContext$valu", "treeNodesEffect", "updateExpend", "_cascaderContext$valu2", "treeStoreExpendEffect", "replaceChecked", "getTreeValue", "watch", "options", "keys", "_props$keys", "load", "valueMode", "length", "store", "TreeStore", "children", "isString", "checkable", "expandMutex", "expandParent", "onLoad", "nextTick", "refreshNodes", "append", "reload", "immediate", "deep", "JSON", "stringify", "treeProps", "setConfig", "_cascaderContext$valu3", "isValueInvalid", "isEmptyValues", "getCascaderValue", "getCascaderItems", "arrV<PERSON>ue", "for<PERSON>ach", "_statusContext$treeSt", "getNodes", "push", "data"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BaA,IAAAA,UAAa,GAAA,SAAbA,UAAaA,CACxBC,KACA,EAAAC,aAAA,EACAC,mBACAC,eACG,EAAA;EACH,IAAMC,gBAAgBC,YAAS,CAAA;AAC7BC,IAAAA,SAAW,EAAA,IAAA;AACXC,IAAAA,QAAU,EAAA,IAAA;IACVC,QAAU,EAAA,KAAA,CAAA;AACVC,IAAAA,WAAW,EAAC;AACZC,IAAAA,QAAQ,EAAA;AACV,GAAC,CAAA,CAAA;EAEM,OAAA;AACLN,IAAAA,aAAA,EAAAA,aAAA;IACAO,eAAA,EAAiBC,aAAS,YAAM;AACxB,MAAA,IACJC,IAAA,GAaEb,KAAA,CAbFa,IAAA;QACAC,aAAA,GAYEd,KAAA,CAZFc,aAAA;QACAC,IAAA,GAWEf,KAAA,CAXFe,IAAA;QACAC,QAAA,GAUEhB,KAAA,CAVFgB,QAAA;QACAC,UAAA,GASEjB,KAAA,CATFiB,UAAA;QACAC,SAAA,GAQElB,KAAA,CARFkB,SAAA;QACAC,UAAA,GAOEnB,KAAA,CAPFmB,UAAA;QACAC,GAAA,GAMEpB,KAAA,CANFoB,GAAA;QACAC,QAAA,GAKErB,KAAA,CALFqB,QAAA;QACAC,aAAA,GAIEtB,KAAA,CAJFsB,aAAA;QACAC,eAAA,GAGEvB,KAAA,CAHFuB,eAAA;QACAC,SAAA,GAEExB,KAAA,CAFFwB,SAAA;QACAC,UAAA,GACEzB,KAAA,CADFyB,UAAA,CAAA;MAEK,OAAAC,aAAA,CAAAA,aAAA,CAAA;QACLC,OAAOvB,aAAc,CAAAI,QAAA;AACrBK,QAAAA,IAAA,EAAAA,IAAA;AACAC,QAAAA,aAAA,EAAAA,aAAA;AACAC,QAAAA,IAAA,EAAAA,IAAA;AACAC,QAAAA,QAAA,EAAAA,QAAA;AACAC,QAAAA,UAAA,EAAAA,UAAA;AACAC,QAAAA,SAAA,EAAAA,SAAA;AACAC,QAAAA,UAAA,EAAAA,UAAA;AACAC,QAAAA,GAAA,EAAAA,GAAA;AACAC,QAAAA,QAAA,EAAAA,QAAA;AACAC,QAAAA,aAAA,EAAAA,aAAA;AACAC,QAAAA,eAAA,EAAAA,eAAA;AACAC,QAAAA,SAAA,EAAAA,SAAA;QACAI,SAAS1B,iBAAkB,CAAAyB,KAAAA;AAAA,OAAA,EACxBvB,aAAA,CAAA,EAAA,EAAA,EAAA;AACHyB,QAAAA,YAAA,EAAc,SAAdA,YAAAA,CAAeC,KAAsB,EAAA;UACnC1B,aAAA,CAAcK,SAAY,GAAAqB,KAAA,CAAA;SAC5B;QACAC,QAAU,EAAA,SAAVA,QAAUA,CAACC,GAAoB,EAAAC,MAAA,EAA8BC,IAAyB,EAAA;AAChF,UAAA,IAAAC,eAAA,CAAQH,KAAKP,UAAU,CAAA,EAAG,OAAA;UAC9BxB,aAAA,CAAc+B,GAAK,EAAA;AAAEC,YAAAA,MAAQ,EAARA,MAAQ;AAAAC,YAAAA,IAAA,EAAAA,IAAAA;AAAK,WAAC,CAAA,CAAA;SACrC;AACAE,QAAAA,UAAY,EAAAjC,eAAA;AACZkC,QAAAA,WAAA,EAAa,SAAbA,WAAAA,CAAcL,GAAgB,EAAA;UAC5B5B,aAAA,CAAcG,QAAW,GAAAyB,GAAA,CAAA;SAC3B;AACAM,QAAAA,SAAA,EAAW,SAAXA,SAAAA,CAAYN,GAAyB,EAAA;UACnC5B,aAAA,CAAcM,MAAS,GAAAsB,GAAA,CAAA;AACzB,SAAA;AAAA,OAAA,CAAA,CAAA;KAEH,CAAA;GACH,CAAA;AACF,EAAA;IAGaO,kBAAA,GAAqB,SAArBA,kBAAAA,CAAsBvC,KAA2B,EAAA;AAC5D,EAAA,IAAMqB,WAAWmB,iBAAY,EAAA,CAAA;AAC7B,EAAA,IAAAC,OAAA,GAA4CC,WAAO1C,KAAK,CAAA;IAAhD2B,KAAO,GAAAc,OAAA,CAAPd,KAAO;IAAAF,UAAA,GAAAgB,OAAA,CAAAhB,UAAA;IAAYkB,YAAa,GAAAF,OAAA,CAAbE,YAAa,CAAA;AAClC,EAAA,IAAAC,UAAA,GAA8BC,iBAAA,CAAUlB,OAAOF,UAAY,EAAAzB,KAAA,CAAM8C,YAAc,EAAA9C,KAAA,CAAM+C,QAAQ,CAAA;IAAAC,WAAA,GAAAC,kCAAA,CAAAL,UAAA,EAAA,CAAA,CAAA;AAA5FM,IAAAA,UAAY,GAAAF,WAAA,CAAA,CAAA,CAAA;AAAA/C,IAAAA,aAAa,GAAA+C,WAAA,CAAA,CAAA,CAAA,CAAA;AAC1B,EAAA,IAAAG,gBAAA,GAAuCC,uBAAA,CAC3CT,YAAA,EACA,KAAA,EACA3C,KAAM,CAAAqD,oBAAA,EACN,cACF,CAAA;IAAAC,iBAAA,GAAAL,kCAAA,CAAAE,gBAAA,EAAA,CAAA,CAAA;AALOjD,IAAAA,iBAAmB,GAAAoD,iBAAA,CAAA,CAAA,CAAA;AAAAnD,IAAAA,eAAe,GAAAmD,iBAAA,CAAA,CAAA,CAAA,CAAA;EAMnC,IAAAC,WAAA,GAAqCxD,WAAWC,KAAO,EAAAC,aAAA,EAAeC,mBAAmBC,eAAe,CAAA;IAAtGQ,8BAAAA;IAAiBP,aAAc,GAAAmD,WAAA,CAAdnD,aAAc,CAAA;AAEjC,EAAA,IAAAoD,YAAA,GAAe5C,aAAS,YAAM;AAClC,IAAA,OAAO6C,QAAQzD,KAAM,CAAAiB,UAAA,IAAcyC,qBAAW,CAAA1D,KAAA,CAAM2D,MAAM,CAAC,CAAA,CAAA;AAC7D,GAAC,CAAA,CAAA;AAGD,EAAA,IAAMC,mBAAmB,SAAnBA,mBAAyB;AAC7B,IAAA,IAAAC,qBAAA,GAA8ClD,eAAgB,CAAAgB,KAAA;MAAtDpB,QAAA,GAAAsD,qBAAA,CAAAtD,QAAA;MAAUD,SAAW,GAAAuD,qBAAA,CAAXvD,SAAW;MAAAuB,YAAA,GAAAgC,qBAAA,CAAAhC,YAAA,CAAA;IAC7BiC,qCAAA,CAAgBvD,QAAU,EAAAD,SAAA,EAAWuB,YAAc,EAAA7B,KAAA,CAAM2D,MAAM,CAAA,CAAA;GACjE,CAAA;AAGA,EAAA,IAAMI,eAAe,SAAfA,eAAqB;AACzB,IAAA,IAAAC,sBAAA,GAA6BrD,eAAgB,CAAAgB,KAAA;MAArCA,MAAO,GAAAqC,sBAAA,CAAPrC,KAAA;MAAOrB,SAAA,GAAA0D,sBAAA,CAAA1D,SAAA,CAAA;AACT,IAAA,IAAEI,SAAWN,aAAA,CAAXM;AACcuD,IAAAA,2CAAA,CAAA3D,SAAA,EAAWqB,QAAOjB,MAAM,CAAA,CAAA;AACpCJ,IAAAA,SAAA,CAAA4D,cAAA,CAAeC,kCAAaxC,CAAAA,MAAK,CAAC,CAAA,CAAA;GAC9C,CAAA;AAEAyC,EAAAA,SAAA,CACE,YAAA;IAAA,OAAMpE,KAAM,CAAAqE,OAAA,CAAA;AAAA,GAAA,EACZ,YAAM;AACE,IAAA,IAAEA,UAA6DrE,KAAA,CAA7DqE;oBAA6DrE,KAAA,CAApDsE,IAAO;AAAPA,MAAAA,IAAO,GAAAC,WAAA,KAAA,KAAA,CAAA,GAAA;MAAIzD,aAAe,GAA0Bd,KAAA,CAAzCc,aAAe;MAAAC,IAAA,GAA0Bf,KAAA,CAA1Be,IAAA;MAAMyD,IAAM,GAAcxE,KAAA,CAApBwE,IAAM;MAAAC,SAAA,GAAczE,KAAA,CAAdyE,SAAA,CAAA;AACjD,IAAA,IAAEnE,cAAcF,aAAA,CAAdE;AAEJ,IAAA,IAAA,CAAC+D,OAAQ,CAAAK,MAAA,IAAU,CAACpE,WAAA,EAAW,OAAA;IAEnC,IAAI,CAACA,WAAW,EAAA;AACR,MAAA,IAAAqE,KAAA,GAAQ,IAAIC,mBAAU,CAAA;AAC1BN,QAAAA,IAAM,EAAA5C,aAAA,CAAAA,aAAA,KACD4C,IAAA,CAAA,EAAA,EAAA,EAAA;UACHO,UAAUC,iBAAS,CAAAR,IAAA,CAAKO,QAAQ,CAAA,GAAIP,KAAKO,QAAW,GAAA,UAAA;SACtD,CAAA;AACAE,QAAAA,SAAW,EAAA,IAAA;AACXC,QAAAA,WAAa,EAAA,IAAA;AACbC,QAAAA,YAAc,EAAA,IAAA;AACdlE,QAAAA,IAAA,EAAAA,IAAA;AACAyD,QAAAA,IAAA,EAAAA,IAAA;AACAC,QAAAA,SAAA,EAAAA,SAAA;AACA3D,QAAAA,aAAA,EAAAA,aAAA;AACAoE,QAAAA,QAAQ,SAARA,SAAc;AACZC,UAAAA,YAAA,CAAS,YAAM;YACbR,KAAA,CAAMS,YAAa,EAAA,CAAA;AACFxB,YAAAA,gBAAA,EAAA,CAAA;AACnB,WAAC,CAAA,CAAA;AACH,SAAA;AACF,OAAC,CAAA,CAAA;AACDe,MAAAA,KAAA,CAAMU,OAAOhB,OAAO,CAAA,CAAA;MACpBjE,aAAA,CAAcE,SAAY,GAAAqE,KAAA,CAAA;AAC5B,KAAO,MAAA;AACLrE,MAAAA,WAAA,CAAUgF,OAAOjB,OAAO,CAAA,CAAA;MACxB/D,WAAA,CAAU8E,YAAa,EAAA,CAAA;AACzB,KAAA;AACarB,IAAAA,YAAA,EAAA,CAAA;AACIH,IAAAA,gBAAA,EAAA,CAAA;AACnB,GAAA,EACA;AAAE2B,IAAAA,SAAA,EAAW,IAAM;AAAAC,IAAAA,IAAA,EAAM,IAAA;AAAK,GAChC,CAAA,CAAA;AAGApB,EAAAA,SAAA,CACE,YAAM;AACJ,IAAA,IAAQtD,aAAA,GAAyCd,KAAA,CAAzCc,aAAA;MAAeC,IAAM,GAAoBf,KAAA,CAA1Be,IAAM;MAAAyD,IAAA,GAAoBxE,KAAA,CAApBwE,IAAA;MAAMC,YAAczE,KAAA,CAAdyE;IACnC,OAAOgB,KAAKC,SAAU,CAAA;AACpBjB,MAAAA,SAAA,EAAAA,SAAA;AACA3D,MAAAA,aAAA,EAAAA,aAAA;AACAC,MAAAA,IAAA,EAAAA,IAAA;AACAyD,MAAAA,IAAA,EAAAA,IAAAA;AACF,KAAC,CAAA,CAAA;AACH,GAAA,EACA,YAAM;AACE,IAAA,IAAElE,YAAcF,aAAA,CAAdE;IACR,IAAI,CAACA,SAAA,EAAW,OAAA;AAChB,IAAA,IAAQQ,aAAA,GAAyCd,KAAA,CAAzCc,aAAA;MAAeC,IAAM,GAAoBf,KAAA,CAA1Be,IAAM;MAAAyD,IAAA,GAAoBxE,KAAA,CAApBwE,IAAA;MAAMC,YAAczE,KAAA,CAAdyE;AACnC,IAAA,IAAMkB,SAAY,GAAA;AAChB7E,MAAAA,aAAA,EAAAA,aAAA;AACAO,MAAAA,QAAA,EAAAA,QAAA;AACAmD,MAAAA,IAAA,EAAAA,IAAA;AACAzD,MAAAA,IAAA,EAAAA,IAAA;AACA0D,MAAAA,SAAA,EAAAA,SAAAA;KACF,CAAA;AACAnE,IAAAA,SAAA,CAAUsF,UAAUD,SAAS,CAAA,CAAA;AAC/B,GAAA,EACA;AAAEJ,IAAAA,WAAW,IAAA;AAAK,GACpB,CAAA,CAAA;EAEAnB,SAAA,CACElB,UAAA,EACA,YAAM;AAEJ,IAAA,IAAA2C,sBAAA,GAA0ClF,eAAgB,CAAAgB,KAAA;MAAlDI,QAAA,GAAA8D,sBAAA,CAAA9D,QAAA;MAAUf,QAAU,GAAA6E,sBAAA,CAAV7E,QAAU;MAAAQ,SAAA,GAAAqE,sBAAA,CAAArE,SAAA,CAAA;IAE5B,IAAIsE,oCAAe,CAAA5C,UAAA,CAAWvB,KAAO,EAAAhB,eAAA,CAAgBgB,KAAK,CAAG,EAAA;MAC3DI,QAAA,CAASf,QAAW,GAAA,EAAK,GAAA,EAAA,EAAI,eAAe,CAAA,CAAA;AAC9C,KAAA;AAEA,IAAA,IAAI,CAAC+E,mCAAA,CAAc7C,UAAW,CAAAvB,KAAK,CAAG,EAAA;AACpCvB,MAAAA,aAAA,CAAcI,QAAW,GAAAwF,sCAAA,CAAiB9C,UAAW,CAAAvB,KAAA,EAAOH,WAAWR,QAAQ,CAAA,CAAA;AACjF,KAAO,MAAA;AACSZ,MAAAA,aAAA,CAAAI,QAAA,GAAWQ,QAAW,GAAA,EAAK,GAAA,EAAA,CAAA;AAC3C,KAAA;AAEA,IAAA,IAAI,CAACZ,aAAc,CAAAE,SAAA,EAAW,OAAA;AACjByD,IAAAA,YAAA,EAAA,CAAA;AACIH,IAAAA,gBAAA,EAAA,CAAA;AACnB,GAAA,EACA;AAAE2B,IAAAA,WAAW,IAAA;AAAK,GACpB,CAAA,CAAA;AAEAnB,EAAAA,SAAA,CACE,YAAA;AAAA,IAAA,OAAMlE,iBAAkB,CAAAyB,KAAA,IAAS6B,YAAa,CAAA7B,KAAA,CAAA;GAC9C,EAAA,UAACC,OAAY,EAAA;AACL,IAAA,IAAES,WAAY,GAAI1B,eAAgB,CAAAgB,KAAA,CAAhCU,WAAY,CAAA;AACpB,IAAA,IAAIT,OAAS,EAAA;MACXS,WAAA,CAAY,EAAE,CAAA,CAAA;AAChB,KAAA;AACF,GACF,CAAA,CAAA;AAEA+B,EAAAA,SAAA,CACE,YAAA;IAAA,OAAMhE,aAAc,CAAAG,QAAA,CAAA;AAAA,GAAA,EACpB,YAAM;AACaqD,IAAAA,gBAAA,EAAA,CAAA;AACnB,GACF,CAAA,CAAA;AAEM,EAAA,IAAAqC,gBAAA,GAAmB,SAAnBA,gBAAAA,CAAoBC,QAA8B,EAAA;IACtD,IAAM7B,UAA4B,EAAC,CAAA;AAC1B6B,IAAAA,QAAA,CAAAC,OAAA,CAAQ,UAACxE,MAAU,EAAA;AAAA,MAAA,IAAAyE,qBAAA,CAAA;AAC1B,MAAA,IAAMtE,KAAQ,GAAA,CAAAsE,qBAAA,GAAAhG,aAAA,CAAcE,SAAW,MAAA8F,IAAAA,IAAAA,qBAAA,uBAAzBA,qBAAA,CAAyBC,QAAA,CAAS1E,MAAK,CAAA,CAAA;AACrDG,MAAAA,KAAA,IAASA,MAAM,CAAM,CAAA,IAAAuC,OAAA,CAAQiC,IAAK,CAAAxE,KAAA,CAAM,GAAGyE,IAAI,CAAA,CAAA;AACjD,KAAC,CAAA,CAAA;AACM,IAAA,OAAAlC,OAAA,CAAA;GACT,CAAA;EAEO,OAAA;AACL1D,IAAAA,eAAA,EAAAA,eAAA;AACA6C,IAAAA,YAAA,EAAAA,YAAA;AACAN,IAAAA,UAAA,EAAAA,UAAA;AACA+C,IAAAA,gBAAA,EAAAA,gBAAAA;GACF,CAAA;AACF;;;;;"}