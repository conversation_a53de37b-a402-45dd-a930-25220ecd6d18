{"version": 3, "file": "checkbox.js", "sources": ["../../../components/checkbox/checkbox.tsx"], "sourcesContent": ["import { defineComponent, ref, toRefs, inject, watch, computed } from 'vue';\nimport { isString } from 'lodash-es';\nimport props from './props';\nimport {\n  useVModel,\n  useRipple,\n  useContent,\n  useDisabled,\n  useReadonly,\n  usePrefixClass,\n  useCommonClassName,\n} from '@tdesign/shared-hooks';\n\nimport { CheckboxGroupInjectionKey } from './consts';\nimport useCheckboxLazyLoad from './hooks/useCheckboxLazyLoad';\nimport useKeyboardEvent from './hooks/useKeyboardEvent';\n\nexport default defineComponent({\n  name: 'TCheckbox',\n  props: {\n    ...props,\n    needRipple: Boolean,\n    stopLabelTrigger: Boolean,\n    index: Number,\n    // 传递给 Checkbox 组件额外的数据\n    data: Object,\n  },\n  setup(props) {\n    const labelRef = ref<HTMLElement>();\n    if (props.needRipple) {\n      useRipple(labelRef);\n    }\n    const { STATUS } = useCommonClassName();\n\n    const { checked, modelValue, lazyLoad } = toRefs(props);\n    const [innerChecked, setInnerChecked] = useVModel(\n      checked,\n      modelValue,\n      props.defaultChecked,\n      props.onChange,\n      'checked',\n    );\n\n    const checkboxGroupData = inject(CheckboxGroupInjectionKey, undefined);\n\n    /**\n     * Warn: Do not use computed to set tName,\n     * otherwise checkbox group will render all checkbox items on every checked or unchecked.\n     */\n    const tName = ref<string>();\n    watch(\n      () => [props.name, checkboxGroupData?.value.name].join('_'),\n      () => {\n        const name = props.name || checkboxGroupData?.value.name;\n        if (name) {\n          tName.value = name;\n        }\n      },\n      { immediate: true },\n    );\n\n    // checked\n    const tChecked = ref(false);\n    const getChecked = () => {\n      const { value, checkAll } = props;\n      if (checkAll) return checkboxGroupData?.value.isCheckAll;\n      return checkboxGroupData?.value ? checkboxGroupData.value.checkedValues.includes(value) : innerChecked.value;\n    };\n    watch(\n      () => [\n        innerChecked.value,\n        checkboxGroupData?.value.isCheckAll,\n        checkboxGroupData?.value.checkedValues?.join(','),\n      ],\n      () => {\n        tChecked.value = getChecked();\n      },\n      { immediate: true },\n    );\n\n    //  Checkbox.disabled > CheckboxGroup.disabled > Form.disabled\n    const beforeDisabled = computed(() => {\n      if (!props.checkAll && !tChecked.value && checkboxGroupData?.value.maxExceeded) {\n        return true;\n      }\n      return null;\n    });\n    const afterDisabled = computed(() => {\n      return checkboxGroupData?.value.disabled;\n    });\n    const isDisabled = useDisabled({ beforeDisabled, afterDisabled });\n\n    //  Checkbox.readonly > CheckboxGroup.readonly > Form.readonly\n    const afterReadonly = computed(() => {\n      return checkboxGroupData?.value.readonly;\n    });\n    const isReadonly = useReadonly({ afterReadonly });\n\n    const tIndeterminate = ref(false);\n    watch(\n      () => [props.checkAll, props.indeterminate, checkboxGroupData?.value.indeterminate],\n      () => {\n        tIndeterminate.value = props.checkAll ? checkboxGroupData?.value.indeterminate : props.indeterminate;\n      },\n      { immediate: true },\n    );\n\n    /** update labelClasses, do not use computed to get labelClasses */\n    const COMPONENT_NAME = usePrefixClass('checkbox');\n    const labelClasses = ref({});\n    watch(\n      [tChecked, isDisabled, tIndeterminate],\n      () => {\n        labelClasses.value = [\n          `${COMPONENT_NAME.value}`,\n          {\n            [STATUS.value.checked]: tChecked.value,\n            [STATUS.value.disabled]: isDisabled.value,\n            [STATUS.value.indeterminate]: tIndeterminate.value,\n          },\n        ];\n      },\n      { immediate: true },\n    );\n\n    const handleChange = (e: Event) => {\n      if (isReadonly.value) return;\n      const checked = !tChecked.value;\n      setInnerChecked(checked, { e });\n      if (checkboxGroupData?.value.handleCheckboxChange) {\n        checkboxGroupData.value.onCheckedChange({ checked, checkAll: props.checkAll, e, option: props });\n      }\n    };\n\n    const renderContent = useContent();\n\n    const handleLabelClick = (e: MouseEvent) => {\n      // 在 Tree、Cascader 等组件中使用  阻止 label触发 checked 与非叶子节点的 expand 冲突\n      if (props.stopLabelTrigger) e.preventDefault();\n    };\n\n    const { showCheckbox } = useCheckboxLazyLoad(labelRef, lazyLoad);\n    const { onCheckboxFocus, onCheckboxBlur } = useKeyboardEvent(handleChange);\n\n    return () => {\n      const titleAttr = isString(props.title) && props.title ? props.title : null;\n      return (\n        <label\n          ref={labelRef}\n          class={labelClasses.value}\n          tabindex={isDisabled.value ? undefined : '0'}\n          onFocus={onCheckboxFocus}\n          onBlur={onCheckboxBlur}\n          onClick={handleLabelClick}\n          title={titleAttr}\n        >\n          {!showCheckbox.value\n            ? null\n            : [\n                <input\n                  type=\"checkbox\"\n                  tabindex=\"-1\"\n                  class={`${COMPONENT_NAME.value}__former`}\n                  disabled={isDisabled.value}\n                  readonly={isReadonly.value}\n                  indeterminate={tIndeterminate.value}\n                  name={tName.value}\n                  value={props.value ? props.value : undefined}\n                  checked={tChecked.value}\n                  onChange={handleChange}\n                  onClick={(e: MouseEvent) => e.stopPropagation()}\n                  key=\"input\"\n                ></input>,\n                <span\n                  class={`${COMPONENT_NAME.value}__input`}\n                  key=\"input-span\"\n                  onClick={props.stopLabelTrigger && handleChange} // stopLabelTrigger 情况下，仍需保证真正的 input 触发 change\n                />,\n                <span class={`${COMPONENT_NAME.value}__label`} key=\"label\">\n                  {renderContent('default', 'label')}\n                </span>,\n              ]}\n        </label>\n      );\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "props", "_objectSpread", "needRipple", "Boolean", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "Number", "data", "Object", "setup", "labelRef", "ref", "useRipple", "_useCommonClassName", "useCommonClassName", "STATUS", "_toRefs", "toRefs", "checked", "modelValue", "lazyLoad", "_useVModel", "useVModel", "defaultChecked", "onChange", "_useVModel2", "_slicedToArray", "innerChecked", "setInnerChecked", "checkboxGroupData", "inject", "CheckboxGroupInjectionKey", "tName", "watch", "value", "join", "immediate", "tChecked", "getChecked", "checkAll", "isCheckAll", "checkedValues", "includes", "_checkboxGroupData$va", "beforeDisabled", "computed", "maxExceeded", "afterDisabled", "disabled", "isDisabled", "useDisabled", "after<PERSON><PERSON><PERSON><PERSON>", "readonly", "is<PERSON><PERSON><PERSON>ly", "useReadonly", "tIndeterminate", "indeterminate", "COMPONENT_NAME", "usePrefixClass", "labelClasses", "concat", "_defineProperty", "handleChange", "e", "handleCheckboxChange", "onCheckedChange", "option", "renderContent", "useContent", "handleLabelClick", "preventDefault", "_useCheckboxLazyLoad", "useCheckboxLazyLoad", "showCheckbox", "_useKeyboardEvent", "useKeyboardEvent", "onCheckboxFocus", "onCheckboxBlur", "titleAttr", "isString", "title", "_createVNode", "onClick", "stopPropagation"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,gBAAeA,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,WAAA;AACNC,EAAAA,KAAO,EAAAC,aAAA,CAAAA,aAAA,KACFD,yBAAA,CAAA,EAAA,EAAA,EAAA;AACHE,IAAAA,UAAY,EAAAC,OAAA;AACZC,IAAAA,gBAAkB,EAAAD,OAAA;AAClBE,IAAAA,KAAO,EAAAC,MAAA;AAEPC,IAAAA,IAAM,EAAAC,MAAAA;GACR,CAAA;AACAC,EAAAA,OAAAA,SAAAA,MAAMT,MAAO,EAAA;AACX,IAAA,IAAMU,WAAWC,OAAiB,EAAA,CAAA;IAClC,IAAIX,OAAME,UAAY,EAAA;MACpBU,eAAA,CAAUF,QAAQ,CAAA,CAAA;AACpB,KAAA;AACM,IAAA,IAAAG,mBAAA,GAAaC,0BAAmB,EAAA;MAA9BC,MAAO,GAAAF,mBAAA,CAAPE,MAAO,CAAA;AAEf,IAAA,IAAAC,OAAA,GAA0CC,WAAOjB,MAAK,CAAA;MAA9CkB,OAAS,GAAAF,OAAA,CAATE,OAAS;MAAAC,UAAA,GAAAH,OAAA,CAAAG,UAAA;MAAYC,QAAS,GAAAJ,OAAA,CAATI,QAAS,CAAA;AAChC,IAAA,IAAAC,UAAA,GAAkCC,iBAAA,CACtCJ,OAAA,EACAC,UAAA,EACAnB,MAAM,CAAAuB,cAAA,EACNvB,MAAM,CAAAwB,QAAA,EACN,SACF,CAAA;MAAAC,WAAA,GAAAC,kCAAA,CAAAL,UAAA,EAAA,CAAA,CAAA;AANOM,MAAAA,YAAc,GAAAF,WAAA,CAAA,CAAA,CAAA;AAAAG,MAAAA,eAAe,GAAAH,WAAA,CAAA,CAAA,CAAA,CAAA;IAQ9B,IAAAI,iBAAA,GAAoBC,UAAO,CAAAC,+CAAA,EAA2B,KAAS,CAAA,CAAA,CAAA;AAMrE,IAAA,IAAMC,QAAQrB,OAAY,EAAA,CAAA;AAC1BsB,IAAAA,SAAA,CACE,YAAA;AAAA,MAAA,OAAM,CAACjC,MAAM,CAAAD,IAAA,EAAM8B,8BAAAA,wCAAAA,kBAAmBK,KAAM,CAAAnC,IAAI,CAAE,CAAAoC,IAAA,CAAK,GAAG,CAAA,CAAA;AAAA,KAAA,EAC1D,YAAM;AACJ,MAAA,IAAMpC,IAAOC,GAAAA,MAAAA,CAAMD,IAAQ,KAAA8B,iBAAA,KAAAA,IAAAA,IAAAA,iBAAA,KAAAA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,iBAAA,CAAmBK,KAAM,CAAAnC,IAAA,CAAA,CAAA;AACpD,MAAA,IAAIA,IAAM,EAAA;QACRiC,KAAA,CAAME,KAAQ,GAAAnC,IAAA,CAAA;AAChB,OAAA;AACF,KAAA,EACA;AAAEqC,MAAAA,WAAW,IAAA;AAAK,KACpB,CAAA,CAAA;AAGM,IAAA,IAAAC,QAAA,GAAW1B,QAAI,KAAK,CAAA,CAAA;AAC1B,IAAA,IAAM2B,aAAa,SAAbA,aAAmB;AACjB,MAAA,IAAEJ,KAAO,GAAalC,MAAAA,CAApBkC,KAAO;QAAAK,QAAA,GAAavC,MAAAA,CAAbuC,QAAA,CAAA;MACX,IAAAA,QAAA,EAAU,OAAOV,8BAAAA,wCAAAA,kBAAmBK,KAAM,CAAAM,UAAA,CAAA;MACvC,OAAAX,iBAAA,aAAAA,iBAAA,KAAA,KAAA,CAAA,IAAAA,iBAAA,CAAmBK,QAAQL,iBAAkB,CAAAK,KAAA,CAAMO,cAAcC,QAAS,CAAAR,KAAK,IAAIP,YAAa,CAAAO,KAAA,CAAA;KACzG,CAAA;AACAD,IAAAA,SAAA,CACE,YAAA;AAAA,MAAA,IAAAU,qBAAA,CAAA;AAAA,MAAA,OAAM,CACJhB,YAAa,CAAAO,KAAA,EACbL,8BAAAA,wCAAAA,kBAAmBK,KAAM,CAAAM,UAAA,EACzBX,iBAAmB,KAAA,IAAA,IAAnBA,iBAAmB,KAAA,KAAA,CAAA,IAAA,CAAAc,qBAAA,GAAnBd,iBAAmB,CAAAK,KAAA,CAAMO,aAAe,MAAA,IAAA,IAAAE,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAxCA,qBAAA,CAAwCR,IAAA,CAAK,GAAG,CAAA,CAClD,CAAA;AAAA,KAAA,EACA,YAAM;AACJE,MAAAA,QAAA,CAASH,QAAQI,UAAW,EAAA,CAAA;AAC9B,KAAA,EACA;AAAEF,MAAAA,WAAW,IAAA;AAAK,KACpB,CAAA,CAAA;AAGM,IAAA,IAAAQ,cAAA,GAAiBC,aAAS,YAAM;AAChC,MAAA,IAAA,CAAC7C,OAAMuC,QAAY,IAAA,CAACF,SAASH,KAAS,IAAAL,iBAAA,KAAA,IAAA,IAAAA,iBAAA,KAAAA,KAAAA,CAAAA,IAAAA,iBAAA,CAAmBK,MAAMY,WAAa,EAAA;AACvE,QAAA,OAAA,IAAA,CAAA;AACT,OAAA;AACO,MAAA,OAAA,IAAA,CAAA;AACT,KAAC,CAAA,CAAA;AACK,IAAA,IAAAC,aAAA,GAAgBF,aAAS,YAAM;MACnC,OAAOhB,8BAAAA,wCAAAA,kBAAmBK,KAAM,CAAAc,QAAA,CAAA;AAClC,KAAC,CAAA,CAAA;IACD,IAAMC,UAAa,GAAAC,mBAAA,CAAY;AAAEN,MAAAA,cAAA,EAAAA,cAAA;AAAgBG,MAAAA,eAAAA,aAAAA;AAAc,KAAC,CAAA,CAAA;AAG1D,IAAA,IAAAI,aAAA,GAAgBN,aAAS,YAAM;MACnC,OAAOhB,8BAAAA,wCAAAA,kBAAmBK,KAAM,CAAAkB,QAAA,CAAA;AAClC,KAAC,CAAA,CAAA;IACD,IAAMC,UAAa,GAAAC,mBAAA,CAAY;AAAEH,MAAAA,aAAA,EAAAA,aAAAA;AAAc,KAAC,CAAA,CAAA;AAE1C,IAAA,IAAAI,cAAA,GAAiB5C,QAAI,KAAK,CAAA,CAAA;AAChCsB,IAAAA,SAAA,CACE,YAAA;AAAA,MAAA,OAAM,CAACjC,MAAM,CAAAuC,QAAA,EAAUvC,OAAMwD,aAAe,EAAA3B,iBAAA,KAAA,IAAA,IAAAA,iBAAA,KAAAA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,iBAAA,CAAmBK,MAAMsB,aAAa,CAAA,CAAA;AAAA,KAAA,EAClF,YAAM;AACJD,MAAAA,cAAA,CAAerB,QAAQlC,MAAM,CAAAuC,QAAA,GAAWV,iBAAmB,KAAA,IAAA,IAAnBA,iBAAmB,KAAnBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,iBAAmB,CAAAK,KAAA,CAAMsB,gBAAgBxD,MAAM,CAAAwD,aAAA,CAAA;AACzF,KAAA,EACA;AAAEpB,MAAAA,WAAW,IAAA;AAAK,KACpB,CAAA,CAAA;AAGM,IAAA,IAAAqB,cAAA,GAAiBC,uBAAe,UAAU,CAAA,CAAA;AAC1C,IAAA,IAAAC,YAAA,GAAehD,OAAI,CAAA,EAAE,CAAA,CAAA;IAC3BsB,SAAA,CACE,CAACI,QAAU,EAAAY,UAAA,EAAYM,cAAc,CAAA,EACrC,YAAM;MACJI,YAAA,CAAazB,KAAQ,GAAA,CAAA,EAAA,CAAA0B,MAAA,CAChBH,cAAe,CAAAvB,KAAA,CAAA,EAAA2B,mCAAA,CAAAA,mCAAA,CAAAA,mCAAA,CAAA,EAAA,EAEf9C,MAAA,CAAOmB,KAAM,CAAAhB,OAAA,EAAUmB,QAAS,CAAAH,KAAA,GAChCnB,MAAA,CAAOmB,KAAM,CAAAc,QAAA,EAAWC,UAAW,CAAAf,KAAA,CACnCnB,EAAAA,MAAA,CAAOmB,KAAM,CAAAsB,aAAA,EAAgBD,cAAe,CAAArB,KAAA,CAEjD,CAAA,CAAA;AACF,KAAA,EACA;AAAEE,MAAAA,WAAW,IAAA;AAAK,KACpB,CAAA,CAAA;AAEM,IAAA,IAAA0B,YAAA,GAAe,SAAfA,YAAAA,CAAgBC,CAAa,EAAA;MACjC,IAAIV,UAAW,CAAAnB,KAAA,EAAO,OAAA;AAChBhB,MAAAA,IAAAA,QAAAA,GAAU,CAACmB,QAAS,CAAAH,KAAA,CAAA;MACVhB,eAAAA,CAAAA,QAAAA,EAAS;AAAE6C,QAAAA,CAAA,EAAAA,CAAAA;AAAE,OAAC,CAAA,CAAA;MAC1B,IAAAlC,iBAAA,aAAAA,iBAAA,KAAA,KAAA,CAAA,IAAAA,iBAAA,CAAmBK,MAAM8B,oBAAsB,EAAA;AAC/BnC,QAAAA,iBAAA,CAAAK,KAAA,CAAM+B,eAAgB,CAAA;AAAE/C,UAAAA,OAAAA,EAAAA,QAAAA;UAASqB,QAAUvC,EAAAA,MAAAA,CAAMuC,QAAU;AAAAwB,UAAAA,CAAA,EAAAA,CAAA;AAAGG,UAAAA,MAAQlE,EAAAA,MAAAA;AAAM,SAAC,CAAA,CAAA;AACjG,OAAA;KACF,CAAA;AAEA,IAAA,IAAMmE,gBAAgBC,kBAAW,EAAA,CAAA;AAE3B,IAAA,IAAAC,gBAAA,GAAmB,SAAnBA,gBAAAA,CAAoBN,CAAkB,EAAA;MAE1C,IAAI/D,MAAM,CAAAI,gBAAA,EAAkB2D,CAAA,CAAEO,cAAe,EAAA,CAAA;KAC/C,CAAA;AAEA,IAAA,IAAAC,oBAAA,GAAyBC,sDAAA,CAAoB9D,UAAUU,QAAQ,CAAA;MAAvDqD,YAAA,GAAAF,oBAAA,CAAAE,YAAA,CAAA;AACR,IAAA,IAAAC,iBAAA,GAA4CC,iDAAiBb,YAAY,CAAA;MAAjEc,eAAA,GAAAF,iBAAA,CAAAE,eAAA;MAAiBC,cAAe,GAAAH,iBAAA,CAAfG,cAAe,CAAA;AAExC,IAAA,OAAO,YAAM;AACL,MAAA,IAAAC,SAAA,GAAYC,kBAAS/E,MAAM,CAAAgF,KAAK,KAAKhF,MAAM,CAAAgF,KAAA,GAAQhF,OAAMgF,KAAQ,GAAA,IAAA,CAAA;AAErE,MAAA,OAAAC,eAAA,CAAA,OAAA,EAAA;AAAA,QAAA,KAAA,EACOvE,QAAA;QAAA,OACEiD,EAAAA,YAAa,CAAAzB,KAAA;AAAA,QAAA,UAAA,EACVe,UAAA,CAAWf,KAAQ,GAAA,KAAA,CAAA,GAAY;mBAChC0C,eAAA;AAAA,QAAA,QAAA,EACDC,cAAA;AAAA,QAAA,SAAA,EACCR,gBAAA;QAAA,OACFS,EAAAA,SAAAA;OAEN,EAAA,CAAA,CAACL,YAAa,CAAAvC,KAAA,GACX,IACA,GAAA,CAAA+C,eAAA,CAAA,OAAA,EAAA;AAAA,QAAA,MAAA,EAAA,UAAA;AAAA,QAAA,UAAA,EAAA,IAAA;AAAA,QAAA,OAAA,EAAA,EAAA,CAAArB,MAAA,CAIcH,cAAA,CAAevB,KACzB,EAAA,UAAA,CAAA;QAAA,UAAUe,EAAAA,WAAWf,KACrB;QAAA,UAAUmB,EAAAA,WAAWnB,KACrB;QAAA,eAAeqB,EAAAA,eAAerB,KAC9B;QAAA,MAAMF,EAAAA,KAAM,CAAAE,KAAA;QAAA,OACLlC,EAAAA,MAAM,CAAAkC,KAAA,GAAQlC,OAAMkC,KAAQ,GAAA,KAAA,CAAA;QAAA,SAC1BG,EAAAA,QAAS,CAAAH,KAAA;AAAA,QAAA,UAAA,EACR4B,YAAA;QAAA,SACD,EAAA,SAAAoB,QAACnB,CAAA,EAAA;AAAA,UAAA,OAAkBA,EAAEoB,eAAgB,EAAA,CAAA;AAAA,SAAA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,OAAA,EAAA,IAAA,CAAA,EAAAF,eAAA,CAAA,MAAA,EAAA;AAAA,QAAA,OAAA,EAAA,EAAA,CAAArB,MAAA,CAIpCH,cAAA,CAAevB,KACzB,EAAA,SAAA,CAAA;AAAA,QAAA,KAAA,EAAA,YAAA;QAAA,SACSlC,EAAAA,MAAM,CAAAI,gBAAA,IAAoB0D,YAAAA;AACrC,OAAA,EAAA,IAAA,CAAA,EAAAmB,eAAA,CAAA,MAAA,EAAA;AAAA,QAAA,OAAA,EAAA,EAAA,CAAArB,MAAA,CACgBH,cAAe,CAAAvB,KAAA,EAAA,SAAA,CAAA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,OAAA,EAAA,CAC5BiC,aAAc,CAAA,SAAA,EAAW,OAAO,CAAA;KAK/C,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}