import { TdCheckboxProps, TdCheckboxGroupProps } from './type';
import './style';
export * from './type';
export type CheckboxProps = TdCheckboxProps;
export type CheckboxGroupProps = TdCheckboxGroupProps;
export declare const Checkbox: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        needRipple: BooleanConstructor;
        stopLabelTrigger: BooleanConstructor;
        index: NumberConstructor;
        data: ObjectConstructor;
        checkAll: BooleanConstructor;
        checked: {
            type: BooleanConstructor;
            default: any;
        };
        modelValue: {
            type: BooleanConstructor;
            default: any;
        };
        defaultChecked: BooleanConstructor;
        default: {
            type: import("vue").PropType<TdCheckboxProps["default"]>;
        };
        disabled: {
            type: BooleanConstructor;
            default: any;
        };
        indeterminate: BooleanConstructor;
        label: {
            type: import("vue").PropType<TdCheckboxProps["label"]>;
        };
        lazyLoad: BooleanConstructor;
        name: {
            type: StringConstructor;
            default: string;
        };
        readonly: {
            type: BooleanConstructor;
            default: any;
        };
        title: {
            type: StringConstructor;
            default: string;
        };
        value: {
            type: import("vue").PropType<TdCheckboxProps["value"]>;
        };
        onChange: import("vue").PropType<TdCheckboxProps["onChange"]>;
    }>>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        needRipple: BooleanConstructor;
        stopLabelTrigger: BooleanConstructor;
        index: NumberConstructor;
        data: ObjectConstructor;
        checkAll: BooleanConstructor;
        checked: {
            type: BooleanConstructor;
            default: any;
        };
        modelValue: {
            type: BooleanConstructor;
            default: any;
        };
        defaultChecked: BooleanConstructor;
        default: {
            type: import("vue").PropType<TdCheckboxProps["default"]>;
        };
        disabled: {
            type: BooleanConstructor;
            default: any;
        };
        indeterminate: BooleanConstructor;
        label: {
            type: import("vue").PropType<TdCheckboxProps["label"]>;
        };
        lazyLoad: BooleanConstructor;
        name: {
            type: StringConstructor;
            default: string;
        };
        readonly: {
            type: BooleanConstructor;
            default: any;
        };
        title: {
            type: StringConstructor;
            default: string;
        };
        value: {
            type: import("vue").PropType<TdCheckboxProps["value"]>;
        };
        onChange: import("vue").PropType<TdCheckboxProps["onChange"]>;
    }>>, {
        disabled: boolean;
        name: string;
        checked: boolean;
        indeterminate: boolean;
        title: string;
        defaultChecked: boolean;
        modelValue: boolean;
        readonly: boolean;
        needRipple: boolean;
        stopLabelTrigger: boolean;
        checkAll: boolean;
        lazyLoad: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        needRipple: BooleanConstructor;
        stopLabelTrigger: BooleanConstructor;
        index: NumberConstructor;
        data: ObjectConstructor;
        checkAll: BooleanConstructor;
        checked: {
            type: BooleanConstructor;
            default: any;
        };
        modelValue: {
            type: BooleanConstructor;
            default: any;
        };
        defaultChecked: BooleanConstructor;
        default: {
            type: import("vue").PropType<TdCheckboxProps["default"]>;
        };
        disabled: {
            type: BooleanConstructor;
            default: any;
        };
        indeterminate: BooleanConstructor;
        label: {
            type: import("vue").PropType<TdCheckboxProps["label"]>;
        };
        lazyLoad: BooleanConstructor;
        name: {
            type: StringConstructor;
            default: string;
        };
        readonly: {
            type: BooleanConstructor;
            default: any;
        };
        title: {
            type: StringConstructor;
            default: string;
        };
        value: {
            type: import("vue").PropType<TdCheckboxProps["value"]>;
        };
        onChange: import("vue").PropType<TdCheckboxProps["onChange"]>;
    }>>, () => JSX.Element, {}, {}, {}, {
        disabled: boolean;
        name: string;
        checked: boolean;
        indeterminate: boolean;
        title: string;
        defaultChecked: boolean;
        modelValue: boolean;
        readonly: boolean;
        needRipple: boolean;
        stopLabelTrigger: boolean;
        checkAll: boolean;
        lazyLoad: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    needRipple: BooleanConstructor;
    stopLabelTrigger: BooleanConstructor;
    index: NumberConstructor;
    data: ObjectConstructor;
    checkAll: BooleanConstructor;
    checked: {
        type: BooleanConstructor;
        default: any;
    };
    modelValue: {
        type: BooleanConstructor;
        default: any;
    };
    defaultChecked: BooleanConstructor;
    default: {
        type: import("vue").PropType<TdCheckboxProps["default"]>;
    };
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    indeterminate: BooleanConstructor;
    label: {
        type: import("vue").PropType<TdCheckboxProps["label"]>;
    };
    lazyLoad: BooleanConstructor;
    name: {
        type: StringConstructor;
        default: string;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    title: {
        type: StringConstructor;
        default: string;
    };
    value: {
        type: import("vue").PropType<TdCheckboxProps["value"]>;
    };
    onChange: import("vue").PropType<TdCheckboxProps["onChange"]>;
}>>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, {
    disabled: boolean;
    name: string;
    checked: boolean;
    indeterminate: boolean;
    title: string;
    defaultChecked: boolean;
    modelValue: boolean;
    readonly: boolean;
    needRipple: boolean;
    stopLabelTrigger: boolean;
    checkAll: boolean;
    lazyLoad: boolean;
}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("vue").Plugin;
export declare const CheckboxGroup: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        disabled: {
            type: BooleanConstructor;
            default: any;
        };
        lazyLoad: BooleanConstructor;
        max: {
            type: NumberConstructor;
            default: any;
        };
        name: {
            type: StringConstructor;
            default: string;
        };
        options: {
            type: import("vue").PropType<TdCheckboxGroupProps["options"]>;
        };
        readonly: {
            type: BooleanConstructor;
            default: any;
        };
        value: {
            type: import("vue").PropType<TdCheckboxGroupProps["value"]>;
            default: TdCheckboxGroupProps["value"];
        };
        modelValue: {
            type: import("vue").PropType<TdCheckboxGroupProps["value"]>;
            default: TdCheckboxGroupProps["value"];
        };
        defaultValue: {
            type: import("vue").PropType<TdCheckboxGroupProps["defaultValue"]>;
            default: () => TdCheckboxGroupProps["defaultValue"];
        };
        onChange: import("vue").PropType<TdCheckboxGroupProps["onChange"]>;
    }>>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        disabled: {
            type: BooleanConstructor;
            default: any;
        };
        lazyLoad: BooleanConstructor;
        max: {
            type: NumberConstructor;
            default: any;
        };
        name: {
            type: StringConstructor;
            default: string;
        };
        options: {
            type: import("vue").PropType<TdCheckboxGroupProps["options"]>;
        };
        readonly: {
            type: BooleanConstructor;
            default: any;
        };
        value: {
            type: import("vue").PropType<TdCheckboxGroupProps["value"]>;
            default: TdCheckboxGroupProps["value"];
        };
        modelValue: {
            type: import("vue").PropType<TdCheckboxGroupProps["value"]>;
            default: TdCheckboxGroupProps["value"];
        };
        defaultValue: {
            type: import("vue").PropType<TdCheckboxGroupProps["defaultValue"]>;
            default: () => TdCheckboxGroupProps["defaultValue"];
        };
        onChange: import("vue").PropType<TdCheckboxGroupProps["onChange"]>;
    }>>, {
        disabled: boolean;
        name: string;
        value: import("./type").CheckboxGroupValue;
        max: number;
        defaultValue: import("./type").CheckboxGroupValue;
        modelValue: import("./type").CheckboxGroupValue;
        readonly: boolean;
        lazyLoad: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        disabled: {
            type: BooleanConstructor;
            default: any;
        };
        lazyLoad: BooleanConstructor;
        max: {
            type: NumberConstructor;
            default: any;
        };
        name: {
            type: StringConstructor;
            default: string;
        };
        options: {
            type: import("vue").PropType<TdCheckboxGroupProps["options"]>;
        };
        readonly: {
            type: BooleanConstructor;
            default: any;
        };
        value: {
            type: import("vue").PropType<TdCheckboxGroupProps["value"]>;
            default: TdCheckboxGroupProps["value"];
        };
        modelValue: {
            type: import("vue").PropType<TdCheckboxGroupProps["value"]>;
            default: TdCheckboxGroupProps["value"];
        };
        defaultValue: {
            type: import("vue").PropType<TdCheckboxGroupProps["defaultValue"]>;
            default: () => TdCheckboxGroupProps["defaultValue"];
        };
        onChange: import("vue").PropType<TdCheckboxGroupProps["onChange"]>;
    }>>, () => JSX.Element, {}, {}, {}, {
        disabled: boolean;
        name: string;
        value: import("./type").CheckboxGroupValue;
        max: number;
        defaultValue: import("./type").CheckboxGroupValue;
        modelValue: import("./type").CheckboxGroupValue;
        readonly: boolean;
        lazyLoad: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    lazyLoad: BooleanConstructor;
    max: {
        type: NumberConstructor;
        default: any;
    };
    name: {
        type: StringConstructor;
        default: string;
    };
    options: {
        type: import("vue").PropType<TdCheckboxGroupProps["options"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    value: {
        type: import("vue").PropType<TdCheckboxGroupProps["value"]>;
        default: TdCheckboxGroupProps["value"];
    };
    modelValue: {
        type: import("vue").PropType<TdCheckboxGroupProps["value"]>;
        default: TdCheckboxGroupProps["value"];
    };
    defaultValue: {
        type: import("vue").PropType<TdCheckboxGroupProps["defaultValue"]>;
        default: () => TdCheckboxGroupProps["defaultValue"];
    };
    onChange: import("vue").PropType<TdCheckboxGroupProps["onChange"]>;
}>>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, {
    disabled: boolean;
    name: string;
    value: import("./type").CheckboxGroupValue;
    max: number;
    defaultValue: import("./type").CheckboxGroupValue;
    modelValue: import("./type").CheckboxGroupValue;
    readonly: boolean;
    lazyLoad: boolean;
}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("vue").Plugin;
export default Checkbox;
