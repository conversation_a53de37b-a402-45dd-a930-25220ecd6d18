import { LoadingProps } from '../loading';
import { TNode, Styles } from '../common';
export interface TdCardProps {
    actions?: string | TNode;
    avatar?: string | TNode;
    bordered?: boolean;
    bodyClassName?: string;
    bodyStyle?: Styles;
    content?: string | TNode;
    cover?: string | TNode;
    default?: string | TNode;
    description?: string | TNode;
    footer?: string | TNode;
    footerClassName?: string;
    footerStyle?: Styles;
    header?: string | TNode;
    headerClassName?: string;
    headerStyle?: Styles;
    headerBordered?: boolean;
    hoverShadow?: boolean;
    loading?: boolean | TNode;
    loadingProps?: LoadingProps;
    shadow?: boolean;
    size?: 'medium' | 'small';
    status?: string;
    subtitle?: string | TNode;
    theme?: 'normal' | 'poster1' | 'poster2';
    title?: string | TNode;
}
