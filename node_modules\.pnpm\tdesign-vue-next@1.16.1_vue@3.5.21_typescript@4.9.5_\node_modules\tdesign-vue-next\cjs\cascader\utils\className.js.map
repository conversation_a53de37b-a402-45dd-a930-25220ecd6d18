{"version": 3, "file": "className.js", "sources": ["../../../../components/cascader/utils/className.ts"], "sourcesContent": ["import { CascaderContextType, TreeNode, TreeNodeValue } from '../types';\n\n/**\n * icon Class\n * @param prefix\n * @param STATUS\n * @param cascaderContext\n * @returns\n */\nexport function getFakeArrowIconClass(\n  prefix: string,\n  STATUS: Record<string, string>,\n  cascaderContext: CascaderContextType,\n) {\n  const { disabled } = cascaderContext;\n  return [\n    `${prefix}-cascader__icon`,\n    {\n      [STATUS.disabled]: disabled,\n    },\n  ];\n}\n\n/**\n * 通用状态\n * @param node\n * @param STATUS\n * @param cascaderContext\n * @returns\n */\nexport function getNodeStatusClass(\n  node: TreeNode,\n  STATUS: Record<string, string>,\n  cascaderContext: CascaderContextType,\n) {\n  const { checkStrictly, multiple, value, max } = cascaderContext;\n  const expandedActive =\n    (!checkStrictly && node.expanded && (multiple ? !node.isLeaf() : true)) || (checkStrictly && node.expanded);\n\n  const isLeaf = node.isLeaf();\n\n  const isDisabled = node.disabled || (multiple && (value as TreeNodeValue[]).length >= max && max !== 0);\n\n  let isSelected = node.checked || (multiple && !checkStrictly && node.expanded && !isLeaf);\n  // 处理单选非叶子节点的选中逻辑\n  if (!multiple && !checkStrictly && !isLeaf) {\n    isSelected = node.expanded;\n  }\n\n  return [\n    {\n      [STATUS.selected]: !isDisabled && isSelected,\n      [STATUS.expanded]: !isDisabled && expandedActive,\n      [STATUS.disabled]: isDisabled,\n    },\n  ];\n}\n\n/**\n * 子节点状态\n * @param prefix\n * @param node\n * @param SIZE\n * @param STATUS\n * @param cascaderContext\n * @returns\n */\nexport function getCascaderItemClass(\n  prefix: string,\n  node: TreeNode,\n  SIZE: Record<string, string>,\n  STATUS: Record<string, string>,\n  cascaderContext: CascaderContextType,\n) {\n  const { size } = cascaderContext;\n  return [\n    `${prefix}-cascader__item`,\n    ...getNodeStatusClass(node, STATUS, cascaderContext),\n    SIZE[size],\n    {\n      [`${prefix}-cascader__item--with-icon`]: !!node.children,\n      [`${prefix}-cascader__item--leaf`]: node.isLeaf(),\n    },\n  ];\n}\n\n/**\n * 子节点icon状态\n * @param prefix\n * @param node\n * @param STATUS\n * @param cascaderContext\n * @returns\n */\nexport function getCascaderItemIconClass(\n  prefix: string,\n  node: TreeNode,\n  STATUS: Record<string, string>,\n  cascaderContext: CascaderContextType,\n) {\n  return [`${prefix}-cascader__item-icon`, `${prefix}-icon`, ...getNodeStatusClass(node, STATUS, cascaderContext)];\n}\n"], "names": ["getFakeArrowIconClass", "prefix", "STATUS", "cascaderContext", "disabled", "concat", "_defineProperty", "getNodeStatusClass", "node", "checkStrictly", "multiple", "value", "max", "expandedActive", "expanded", "<PERSON><PERSON><PERSON><PERSON>", "isDisabled", "length", "isSelected", "checked", "selected", "getCascaderItemClass", "SIZE", "size", "_toConsumableArray", "children", "getCascaderItemIconClass"], "mappings": ";;;;;;;;;;;;;;;;;;AASgB,SAAAA,qBAAAA,CACdC,MACA,EAAAC,MAAA,EACAC,eACA,EAAA;AACM,EAAA,IAAEC,WAAaD,eAAA,CAAbC;AACD,EAAA,OAAA,CAAAC,EAAAA,CAAAA,MAAA,CACFJ,MAAA,EAAAK,iBAAAA,CAAAA,EAAAA,mCAAA,CAEAJ,EAAAA,EAAAA,OAAOE,QAAW,EAAAA,QAAA,CAEvB,CAAA,CAAA;AACF,CAAA;AASgB,SAAAG,kBAAAA,CACdC,IACA,EAAAN,MAAA,EACAC,eACA,EAAA;AACA,EAAA,IAAQM,aAAA,GAAwCN,eAAA,CAAxCM,aAAA;IAAeC,QAAU,GAAeP,eAAA,CAAzBO,QAAU;IAAAC,KAAA,GAAeR,eAAA,CAAfQ,KAAA;IAAOC,MAAQT,eAAA,CAARS;EACxC,IAAMC,cACH,GAAA,CAACJ,aAAiB,IAAAD,IAAA,CAAKM,QAAa,KAAAJ,QAAA,GAAW,CAACF,IAAA,CAAKO,MAAO,EAAA,GAAI,IAAW,CAAA,IAAAN,aAAA,IAAiBD,IAAK,CAAAM,QAAA,CAAA;AAE9F,EAAA,IAAAC,MAAA,GAASP,KAAKO,MAAO,EAAA,CAAA;AAE3B,EAAA,IAAMC,aAAaR,IAAK,CAAAJ,QAAA,IAAaM,YAAaC,KAA0B,CAAAM,MAAA,IAAUL,OAAOA,GAAQ,KAAA,CAAA,CAAA;AAEjG,EAAA,IAAAM,UAAA,GAAaV,KAAKW,OAAY,IAAAT,QAAA,IAAY,CAACD,aAAiB,IAAAD,IAAA,CAAKM,YAAY,CAACC,MAAA,CAAA;EAElF,IAAI,CAACL,QAAA,IAAY,CAACD,aAAA,IAAiB,CAACM,MAAQ,EAAA;IAC1CG,UAAA,GAAaV,IAAK,CAAAM,QAAA,CAAA;AACpB,GAAA;AAEO,EAAA,OAAA,CAAAR,mCAAA,CAAAA,mCAAA,CAAAA,mCAAA,CAEFJ,EAAAA,EAAAA,MAAA,CAAOkB,QAAW,EAAA,CAACJ,UAAc,IAAAE,UAAA,CACjChB,EAAAA,MAAA,CAAOY,QAAW,EAAA,CAACE,UAAc,IAAAH,cAAA,CAAA,EACjCX,OAAOE,QAAW,EAAAY,UAAA,CAEvB,CAAA,CAAA;AACF,CAAA;AAWO,SAASK,oBACdA,CAAApB,MAAA,EACAO,IACA,EAAAc,IAAA,EACApB,QACAC,eACA,EAAA;AACM,EAAA,IAAEoB,OAASpB,eAAA,CAAToB;EACD,OAAAlB,CAAAA,EAAAA,CAAAA,MAAA,CACFJ,MAAA,EAAA,iBAAA,CAAA,CAAA,CAAAI,MAAA,CAAAmB,sCAAA,CACAjB,kBAAA,CAAmBC,IAAM,EAAAN,MAAA,EAAQC,eAAe,CAAA,IACnDmB,IAAK,CAAAC,IAAA,CAAA,EAAAjB,mCAAA,CAAAA,mCAAA,QAAAD,MAAA,CAECJ,MAAqC,EAAA,4BAAA,CAAA,EAAA,CAAC,CAACO,IAAK,CAAAiB,QAAA,CAAApB,EAAAA,EAAAA,CAAAA,MAAA,CAC5CJ,MAAgC,EAAA,uBAAA,CAAA,EAAAO,IAAA,CAAKO,MAAO,EAAA,CAAA,CAAA,CAAA,CAAA;AAGtD,CAAA;AAUO,SAASW,wBACdA,CAAAzB,MAAA,EACAO,IACA,EAAAN,MAAA,EACAC,eACA,EAAA;AACO,EAAA,OAAA,CAAA,EAAA,CAAAE,MAAA,CAAIJ,MAAA,8BAAAI,MAAA,CAAiCJ,MAAA,EAAAI,OAAAA,CAAAA,CAAAA,CAAAA,MAAA,CAAAmB,sCAAA,CAAkBjB,kBAAmB,CAAAC,IAAA,EAAMN,MAAQ,EAAAC,eAAe,CAAC,CAAA,CAAA,CAAA;AACjH;;;;;;;"}