/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  disabled: {
    type: Boolean,
    "default": void 0
  },
  lazyLoad: <PERSON>olean,
  max: {
    type: Number,
    "default": void 0
  },
  name: {
    type: String,
    "default": ""
  },
  options: {
    type: Array
  },
  readonly: {
    type: Boolean,
    "default": void 0
  },
  value: {
    type: Array,
    "default": void 0
  },
  modelValue: {
    type: Array,
    "default": void 0
  },
  defaultValue: {
    type: Array,
    "default": function _default() {
      return [];
    }
  },
  onChange: Function
};

exports["default"] = props;
//# sourceMappingURL=checkbox-group-props.js.map
