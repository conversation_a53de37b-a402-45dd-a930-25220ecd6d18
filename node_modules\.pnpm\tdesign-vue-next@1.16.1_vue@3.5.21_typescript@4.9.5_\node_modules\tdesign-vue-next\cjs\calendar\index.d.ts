import { TdCalendarProps } from './type';
import './style';
export * from './type';
export type CalendarProps = TdCalendarProps;
export declare const Calendar: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        cell: {
            type: import("vue").PropType<TdCalendarProps["cell"]>;
        };
        cellAppend: {
            type: import("vue").PropType<TdCalendarProps["cellAppend"]>;
        };
        controllerConfig: {
            type: import("vue").PropType<TdCalendarProps["controllerConfig"]>;
            default: TdCalendarProps["controllerConfig"];
        };
        fillWithZero: {
            type: BooleanConstructor;
            default: any;
        };
        firstDayOfWeek: {
            type: NumberConstructor;
            validator(val: TdCalendarProps["firstDayOfWeek"]): boolean;
        };
        format: {
            type: StringConstructor;
            default: string;
        };
        head: {
            type: import("vue").PropType<TdCalendarProps["head"]>;
        };
        isShowWeekendDefault: {
            type: BooleanConstructor;
            default: boolean;
        };
        mode: {
            type: import("vue").PropType<TdCalendarProps["mode"]>;
            default: TdCalendarProps["mode"];
            validator(val: TdCalendarProps["mode"]): boolean;
        };
        month: {
            type: import("vue").PropType<TdCalendarProps["month"]>;
        };
        multiple: BooleanConstructor;
        preventCellContextmenu: BooleanConstructor;
        range: {
            type: import("vue").PropType<TdCalendarProps["range"]>;
        };
        theme: {
            type: import("vue").PropType<TdCalendarProps["theme"]>;
            default: TdCalendarProps["theme"];
            validator(val: TdCalendarProps["theme"]): boolean;
        };
        value: {
            type: import("vue").PropType<TdCalendarProps["value"]>;
        };
        week: {
            type: import("vue").PropType<TdCalendarProps["week"]>;
        };
        year: {
            type: import("vue").PropType<TdCalendarProps["year"]>;
        };
        onCellClick: import("vue").PropType<TdCalendarProps["onCellClick"]>;
        onCellDoubleClick: import("vue").PropType<TdCalendarProps["onCellDoubleClick"]>;
        onCellRightClick: import("vue").PropType<TdCalendarProps["onCellRightClick"]>;
        onControllerChange: import("vue").PropType<TdCalendarProps["onControllerChange"]>;
        onMonthChange: import("vue").PropType<TdCalendarProps["onMonthChange"]>;
    }>>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        cell: {
            type: import("vue").PropType<TdCalendarProps["cell"]>;
        };
        cellAppend: {
            type: import("vue").PropType<TdCalendarProps["cellAppend"]>;
        };
        controllerConfig: {
            type: import("vue").PropType<TdCalendarProps["controllerConfig"]>;
            default: TdCalendarProps["controllerConfig"];
        };
        fillWithZero: {
            type: BooleanConstructor;
            default: any;
        };
        firstDayOfWeek: {
            type: NumberConstructor;
            validator(val: TdCalendarProps["firstDayOfWeek"]): boolean;
        };
        format: {
            type: StringConstructor;
            default: string;
        };
        head: {
            type: import("vue").PropType<TdCalendarProps["head"]>;
        };
        isShowWeekendDefault: {
            type: BooleanConstructor;
            default: boolean;
        };
        mode: {
            type: import("vue").PropType<TdCalendarProps["mode"]>;
            default: TdCalendarProps["mode"];
            validator(val: TdCalendarProps["mode"]): boolean;
        };
        month: {
            type: import("vue").PropType<TdCalendarProps["month"]>;
        };
        multiple: BooleanConstructor;
        preventCellContextmenu: BooleanConstructor;
        range: {
            type: import("vue").PropType<TdCalendarProps["range"]>;
        };
        theme: {
            type: import("vue").PropType<TdCalendarProps["theme"]>;
            default: TdCalendarProps["theme"];
            validator(val: TdCalendarProps["theme"]): boolean;
        };
        value: {
            type: import("vue").PropType<TdCalendarProps["value"]>;
        };
        week: {
            type: import("vue").PropType<TdCalendarProps["week"]>;
        };
        year: {
            type: import("vue").PropType<TdCalendarProps["year"]>;
        };
        onCellClick: import("vue").PropType<TdCalendarProps["onCellClick"]>;
        onCellDoubleClick: import("vue").PropType<TdCalendarProps["onCellDoubleClick"]>;
        onCellRightClick: import("vue").PropType<TdCalendarProps["onCellRightClick"]>;
        onControllerChange: import("vue").PropType<TdCalendarProps["onControllerChange"]>;
        onMonthChange: import("vue").PropType<TdCalendarProps["onMonthChange"]>;
    }>>, {
        format: string;
        mode: "month" | "year";
        multiple: boolean;
        theme: "card" | "full";
        controllerConfig: boolean | import("./type").CalendarController;
        isShowWeekendDefault: boolean;
        fillWithZero: boolean;
        preventCellContextmenu: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        cell: {
            type: import("vue").PropType<TdCalendarProps["cell"]>;
        };
        cellAppend: {
            type: import("vue").PropType<TdCalendarProps["cellAppend"]>;
        };
        controllerConfig: {
            type: import("vue").PropType<TdCalendarProps["controllerConfig"]>;
            default: TdCalendarProps["controllerConfig"];
        };
        fillWithZero: {
            type: BooleanConstructor;
            default: any;
        };
        firstDayOfWeek: {
            type: NumberConstructor;
            validator(val: TdCalendarProps["firstDayOfWeek"]): boolean;
        };
        format: {
            type: StringConstructor;
            default: string;
        };
        head: {
            type: import("vue").PropType<TdCalendarProps["head"]>;
        };
        isShowWeekendDefault: {
            type: BooleanConstructor;
            default: boolean;
        };
        mode: {
            type: import("vue").PropType<TdCalendarProps["mode"]>;
            default: TdCalendarProps["mode"];
            validator(val: TdCalendarProps["mode"]): boolean;
        };
        month: {
            type: import("vue").PropType<TdCalendarProps["month"]>;
        };
        multiple: BooleanConstructor;
        preventCellContextmenu: BooleanConstructor;
        range: {
            type: import("vue").PropType<TdCalendarProps["range"]>;
        };
        theme: {
            type: import("vue").PropType<TdCalendarProps["theme"]>;
            default: TdCalendarProps["theme"];
            validator(val: TdCalendarProps["theme"]): boolean;
        };
        value: {
            type: import("vue").PropType<TdCalendarProps["value"]>;
        };
        week: {
            type: import("vue").PropType<TdCalendarProps["week"]>;
        };
        year: {
            type: import("vue").PropType<TdCalendarProps["year"]>;
        };
        onCellClick: import("vue").PropType<TdCalendarProps["onCellClick"]>;
        onCellDoubleClick: import("vue").PropType<TdCalendarProps["onCellDoubleClick"]>;
        onCellRightClick: import("vue").PropType<TdCalendarProps["onCellRightClick"]>;
        onControllerChange: import("vue").PropType<TdCalendarProps["onControllerChange"]>;
        onMonthChange: import("vue").PropType<TdCalendarProps["onMonthChange"]>;
    }>>, () => JSX.Element, {}, {}, {}, {
        format: string;
        mode: "month" | "year";
        multiple: boolean;
        theme: "card" | "full";
        controllerConfig: boolean | import("./type").CalendarController;
        isShowWeekendDefault: boolean;
        fillWithZero: boolean;
        preventCellContextmenu: boolean;
    }>;
    __isFragment?: never;
    __isTeleport?: never;
    __isSuspense?: never;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    cell: {
        type: import("vue").PropType<TdCalendarProps["cell"]>;
    };
    cellAppend: {
        type: import("vue").PropType<TdCalendarProps["cellAppend"]>;
    };
    controllerConfig: {
        type: import("vue").PropType<TdCalendarProps["controllerConfig"]>;
        default: TdCalendarProps["controllerConfig"];
    };
    fillWithZero: {
        type: BooleanConstructor;
        default: any;
    };
    firstDayOfWeek: {
        type: NumberConstructor;
        validator(val: TdCalendarProps["firstDayOfWeek"]): boolean;
    };
    format: {
        type: StringConstructor;
        default: string;
    };
    head: {
        type: import("vue").PropType<TdCalendarProps["head"]>;
    };
    isShowWeekendDefault: {
        type: BooleanConstructor;
        default: boolean;
    };
    mode: {
        type: import("vue").PropType<TdCalendarProps["mode"]>;
        default: TdCalendarProps["mode"];
        validator(val: TdCalendarProps["mode"]): boolean;
    };
    month: {
        type: import("vue").PropType<TdCalendarProps["month"]>;
    };
    multiple: BooleanConstructor;
    preventCellContextmenu: BooleanConstructor;
    range: {
        type: import("vue").PropType<TdCalendarProps["range"]>;
    };
    theme: {
        type: import("vue").PropType<TdCalendarProps["theme"]>;
        default: TdCalendarProps["theme"];
        validator(val: TdCalendarProps["theme"]): boolean;
    };
    value: {
        type: import("vue").PropType<TdCalendarProps["value"]>;
    };
    week: {
        type: import("vue").PropType<TdCalendarProps["week"]>;
    };
    year: {
        type: import("vue").PropType<TdCalendarProps["year"]>;
    };
    onCellClick: import("vue").PropType<TdCalendarProps["onCellClick"]>;
    onCellDoubleClick: import("vue").PropType<TdCalendarProps["onCellDoubleClick"]>;
    onCellRightClick: import("vue").PropType<TdCalendarProps["onCellRightClick"]>;
    onControllerChange: import("vue").PropType<TdCalendarProps["onControllerChange"]>;
    onMonthChange: import("vue").PropType<TdCalendarProps["onMonthChange"]>;
}>>, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, {
    format: string;
    mode: "month" | "year";
    multiple: boolean;
    theme: "card" | "full";
    controllerConfig: boolean | import("./type").CalendarController;
    isShowWeekendDefault: boolean;
    fillWithZero: boolean;
    preventCellContextmenu: boolean;
}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & import("vue").Plugin;
export default Calendar;
