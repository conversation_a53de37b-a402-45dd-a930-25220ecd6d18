{"version": 3, "file": "checkbox-group-props.js", "sources": ["../../../components/checkbox/checkbox-group-props.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * 该文件为脚本自动生成文件，请勿随意修改。如需修改请联系 PMC\n * */\n\nimport { TdCheckboxGroupProps } from '../checkbox/type';\nimport { PropType } from 'vue';\n\nexport default {\n  /** 是否禁用组件。优先级：Form.disabled < CheckboxGroup.disabled < Checkbox.disabled */\n  disabled: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** 是否启用懒加载。子组件 Checkbox 数据量大时建议开启；加载复杂内容或大量图片时建议开启 */\n  lazyLoad: Boolean,\n  /** 支持最多选中的数量 */\n  max: {\n    type: Number,\n    default: undefined,\n  },\n  /** 统一设置内部复选框 HTML 属性 */\n  name: {\n    type: String,\n    default: '',\n  },\n  /** 以配置形式设置子元素。示例1：`['北京', '上海']` ，示例2: `[{ label: '全选', checkAll: true }, { label: '上海', value: 'shanghai' }]`。checkAll 值为 true 表示当前选项为「全选选项」 */\n  options: {\n    type: Array as PropType<TdCheckboxGroupProps['options']>,\n  },\n  /** 只读状态 */\n  readonly: {\n    type: Boolean,\n    default: undefined,\n  },\n  /** 选中值 */\n  value: {\n    type: Array as PropType<TdCheckboxGroupProps['value']>,\n    default: undefined as TdCheckboxGroupProps['value'],\n  },\n  modelValue: {\n    type: Array as PropType<TdCheckboxGroupProps['value']>,\n    default: undefined as TdCheckboxGroupProps['value'],\n  },\n  /** 选中值，非受控属性 */\n  defaultValue: {\n    type: Array as PropType<TdCheckboxGroupProps['defaultValue']>,\n    default: (): TdCheckboxGroupProps['defaultValue'] => [],\n  },\n  /** 值变化时触发。`context.current` 表示当前变化的数据项，如果是全选则为空；`context.type` 表示引起选中数据变化的是选中或是取消选中，`context.option` 表示当前变化的数据项 */\n  onChange: Function as PropType<TdCheckboxGroupProps['onChange']>,\n};\n"], "names": ["disabled", "type", "Boolean", "lazyLoad", "max", "Number", "name", "String", "options", "Array", "readonly", "value", "modelValue", "defaultValue", "default", "onChange", "Function"], "mappings": ";;;;;;;;;;AASA,YAAe;AAEbA,EAAAA,QAAU,EAAA;AACRC,IAAAA,IAAM,EAAAC,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAC,EAAAA,QAAU,EAAAD,OAAA;AAEVE,EAAAA,GAAK,EAAA;AACHH,IAAAA,IAAM,EAAAI,MAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAC,EAAAA,IAAM,EAAA;AACJL,IAAAA,IAAM,EAAAM,MAAA;IACN,SAAS,EAAA,EAAA;GACX;AAEAC,EAAAA,OAAS,EAAA;AACPP,IAAAA,IAAM,EAAAQ,KAAAA;GACR;AAEAC,EAAAA,QAAU,EAAA;AACRT,IAAAA,IAAM,EAAAC,OAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAS,EAAAA,KAAO,EAAA;AACLV,IAAAA,IAAM,EAAAQ,KAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AACAG,EAAAA,UAAY,EAAA;AACVX,IAAAA,IAAM,EAAAQ,KAAA;AACN,IAAA,SAAA,EAAS,KAAA,CAAA;GACX;AAEAI,EAAAA,YAAc,EAAA;AACZZ,IAAAA,IAAM,EAAAQ,KAAA;IACN,SAAS,EAAA,SAATK,QAAAA,GAAA;AAAA,MAAA,OAAqD,EAAC,CAAA;AAAA,KAAA;GACxD;AAEAC,EAAAA,QAAU,EAAAC,QAAAA;AACZ,CAAA;;;;"}