{"version": 3, "file": "calendar.js", "sources": ["../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_basePullAt.js", "../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/remove.js", "../../../components/calendar/calendar.tsx"], "sourcesContent": ["import baseUnset from './_baseUnset.js';\nimport isIndex from './_isIndex.js';\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * The base implementation of `_.pullAt` without support for individual\n * indexes or capturing the removed elements.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {number[]} indexes The indexes of elements to remove.\n * @returns {Array} Returns `array`.\n */\nfunction basePullAt(array, indexes) {\n  var length = array ? indexes.length : 0,\n      lastIndex = length - 1;\n\n  while (length--) {\n    var index = indexes[length];\n    if (length == lastIndex || index !== previous) {\n      var previous = index;\n      if (isIndex(index)) {\n        splice.call(array, index, 1);\n      } else {\n        baseUnset(array, index);\n      }\n    }\n  }\n  return array;\n}\n\nexport default basePullAt;\n", "import baseIteratee from './_baseIteratee.js';\nimport basePullAt from './_basePullAt.js';\n\n/**\n * Removes all elements from `array` that `predicate` returns truthy for\n * and returns an array of the removed elements. The predicate is invoked\n * with three arguments: (value, index, array).\n *\n * **Note:** Unlike `_.filter`, this method mutates `array`. Use `_.pull`\n * to pull elements from an array by value.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new array of removed elements.\n * @example\n *\n * var array = [1, 2, 3, 4];\n * var evens = _.remove(array, function(n) {\n *   return n % 2 == 0;\n * });\n *\n * console.log(array);\n * // => [1, 3]\n *\n * console.log(evens);\n * // => [2, 4]\n */\nfunction remove(array, predicate) {\n  var result = [];\n  if (!(array && array.length)) {\n    return result;\n  }\n  var index = -1,\n      indexes = [],\n      length = array.length;\n\n  predicate = baseIteratee(predicate, 3);\n  while (++index < length) {\n    var value = array[index];\n    if (predicate(value, index, array)) {\n      result.push(value);\n      indexes.push(index);\n    }\n  }\n  basePullAt(array, indexes);\n  return result;\n}\n\nexport default remove;\n", "import { defineComponent, computed, watch } from 'vue';\n// 通用库\nimport dayjs from 'dayjs';\nimport { remove, isArray, isFunction } from 'lodash-es';\n\nimport props from './props';\nimport * as utils from './utils';\nimport { useConfig, useContent } from '@tdesign/shared-hooks';\n\nimport { useState, useCalendarClass, userController, useColHeaders } from './hooks';\n\n// 组件的一些常量\nimport {\n  COMPONENT_NAME,\n  MIN_YEAR,\n  FIRST_MONTH_OF_YEAR,\n  LAST_MONTH_OF_YEAR,\n  DEFAULT_YEAR_CELL_NUMINROW,\n} from './consts';\n\n// 子组件\nimport { Select as TSelect } from '../select';\nimport { RadioGroup as TRadioGroup, RadioButton as TRadioButton } from '../radio';\nimport { Button as TButton } from '../button';\nimport { CheckTag as TCheckTag } from '../tag';\nimport CalendarCellItem from './calendar-cell';\n\n// 组件相关类型\nimport { CalendarCell } from './type';\nimport { CalendarRange, YearMonthOption, ModeOption, CellEventOption } from './types';\n\n// 组件逻辑\nexport default defineComponent({\n  name: 'TCalendar',\n  props,\n  setup(props, { slots }) {\n    const renderContent = useContent();\n    const { t, globalConfig } = useConfig(COMPONENT_NAME);\n    // 组件内部状态管理\n    const { state, toToday, checkDayVisible } = useState(props);\n\n    // 样式\n    const cls = useCalendarClass(props, state);\n\n    // 日历主体头部（日历模式下使用）\n    const { cellColHeaders } = useColHeaders(props, state);\n\n    // 日历控件栏（右上角）\n    const controller = userController(props, state);\n\n    // 年\\月份下拉框\n    const rangeFromTo = computed<CalendarRange>(() => {\n      if (!props.range || props.range.length < 2) {\n        return null;\n      }\n      const [v1, v2] = props.range;\n      if (dayjs(v1).isBefore(dayjs(v2))) {\n        return {\n          from: v1,\n          to: v2,\n        };\n      }\n      return {\n        from: v2,\n        to: v1,\n      };\n    });\n    function checkMonthAndYearSelectedDisabled(year: number, month: number): boolean {\n      let disabled = false;\n      if (rangeFromTo.value && rangeFromTo.value.from && rangeFromTo.value.to) {\n        const beginYear = dayjs(rangeFromTo.value.from).year();\n        const endYear = dayjs(rangeFromTo.value.to).year();\n        if (year === beginYear) {\n          const beginMon = parseInt(dayjs(rangeFromTo.value.from).format('M'), 10);\n          disabled = month < beginMon;\n        } else if (year === endYear) {\n          const endMon = parseInt(dayjs(rangeFromTo.value.to).format('M'), 10);\n          disabled = month > endMon;\n        }\n      }\n      return disabled;\n    }\n    // 当存在日期范围限制时，改变年份后应将月份调整为合法月份\n    function adjustMonth(): void {\n      if (rangeFromTo.value?.from && rangeFromTo.value?.to) {\n        const beginYear = dayjs(rangeFromTo.value.from).year();\n        const endYear = dayjs(rangeFromTo.value.to).year();\n        const beginMon = parseInt(dayjs(rangeFromTo.value.from).format('M'), 10);\n        if (checkMonthAndYearSelectedDisabled(state.curSelectedYear, state.curSelectedMonth)) {\n          state.curSelectedMonth =\n            state.curSelectedYear === beginYear\n              ? beginMon\n              : state.curSelectedYear === endYear\n              ? 1\n              : state.curSelectedMonth;\n        }\n      }\n    }\n    watch(\n      () => {\n        return {\n          year: `${state.curSelectedYear}`,\n          month: `${state.curSelectedMonth}`,\n        };\n      },\n      (v: { month: string; year: string }) => {\n        isFunction(props.onMonthChange) && props.onMonthChange({ ...v });\n        controller.emitControllerChange();\n      },\n    );\n    const dateSelect = {\n      yearSelectOptionList: computed<YearMonthOption[]>(() => {\n        const re: YearMonthOption[] = [];\n        let begin: number = state.curSelectedYear - 10;\n        let end: number = state.curSelectedYear + 10;\n        if (rangeFromTo.value && rangeFromTo.value.from && rangeFromTo.value.to) {\n          begin = dayjs(rangeFromTo.value.from).year();\n          end = dayjs(rangeFromTo.value.to).year();\n        }\n\n        if (begin < MIN_YEAR) {\n          begin = MIN_YEAR;\n        }\n        if (end < MIN_YEAR) {\n          end = MIN_YEAR;\n        }\n\n        for (let i = begin; i <= end; i++) {\n          re.push({\n            value: i,\n            label: t(globalConfig.value.yearSelection, { year: i }),\n            disabled: false,\n          });\n        }\n        return re;\n      }),\n      isYearSelectVisible: computed<boolean>(() => {\n        return controller.checkControllerVisible('year');\n      }),\n      isYearSelectDisabled: computed<boolean>(() => {\n        return controller.checkControllerDisabled('year', 'selectProps');\n      }),\n      monthSelectOptionList: computed<YearMonthOption[]>(() => {\n        adjustMonth();\n        const re: YearMonthOption[] = [];\n        for (let i = FIRST_MONTH_OF_YEAR; i <= LAST_MONTH_OF_YEAR; i++) {\n          const disabled = checkMonthAndYearSelectedDisabled(state.curSelectedYear, i);\n          re.push({\n            value: i,\n            label: t(globalConfig.value.monthSelection, { month: i }),\n            disabled,\n          });\n        }\n        return re;\n      }),\n      isMonthSelectVisible: computed<boolean>(() => {\n        return state.curSelectedMode === 'month' && controller.checkControllerVisible('month');\n      }),\n      isMonthSelectDisabled: computed<boolean>(() => {\n        return controller.checkControllerDisabled('month', 'selectProps');\n      }),\n    };\n    // 模式选项\n    const modeSelect = {\n      optionList: computed<ModeOption[]>(() => {\n        return [\n          { value: 'month', label: t(globalConfig.value.monthRadio) },\n          { value: 'year', label: t(globalConfig.value.yearRadio) },\n        ];\n      }),\n      isVisible: computed<boolean>(() => {\n        return controller.checkControllerVisible('mode');\n      }),\n      isDisabled: computed<boolean>(() => {\n        return controller.checkControllerDisabled('mode', 'radioGroupProps');\n      }),\n    };\n    // 显示\\隐藏周末按钮\n    const weekendBtn = {\n      text: computed<string>(() => {\n        return state.isShowWeekend ? t(globalConfig.value.hideWeekend) : t(globalConfig.value.showWeekend);\n      }),\n      vBind: computed<object>(() => {\n        const c = controller.configData.value.weekend;\n        return state.isShowWeekend ? c.hideWeekendButtonProps : c.showWeekendButtonProps;\n      }),\n      isVisible: computed<boolean>(() => {\n        return (\n          props.theme === 'full' &&\n          controller.checkControllerVisible('current') &&\n          controller.checkControllerVisible('weekend')\n        );\n      }),\n      isDisabled: computed<boolean>(() => {\n        const p = state.isShowWeekend ? 'hideWeekendButtonProps' : 'showWeekendButtonProps';\n        return controller.checkControllerDisabled('weekend', p);\n      }),\n    };\n    // 今天\\本月按钮\n    const currentBtn = {\n      text: computed<string>(() => {\n        return state.curSelectedMode === 'month' ? t(globalConfig.value.today) : t(globalConfig.value.thisMonth);\n      }),\n      vBind: computed<object>(() => {\n        const c = controller.configData.value.current;\n        return state.curSelectedMode === 'month' ? c.currentDayButtonProps : c.currentMonthButtonProps;\n      }),\n      isVisible: computed<boolean>(() => {\n        return props.theme === 'full' && controller.checkControllerVisible('current');\n      }),\n      isDisabled: computed(() => {\n        const p = state.curSelectedMode === 'month' ? 'currentDayButtonProps' : 'currentMonthButtonProps';\n        return controller.checkControllerDisabled('current', p);\n      }),\n    };\n    const renderControl = () => {\n      return (\n        <div class={cls.control.value}>\n          <div class={cls.title.value}>\n            {renderContent('head', undefined, {\n              params: { ...controller.options.value },\n            })}\n          </div>\n          <div class={cls.controlSection.value}>\n            {dateSelect.isYearSelectVisible.value && (\n              <div class={cls.controlSectionCell.value}>\n                <TSelect\n                  v-model={state.curSelectedYear}\n                  size={state.controlSize}\n                  autoWidth={true}\n                  {...controller.configData.value.year.selectProps}\n                  disabled={dateSelect.isYearSelectDisabled.value}\n                  options={dateSelect.yearSelectOptionList.value}\n                ></TSelect>\n              </div>\n            )}\n            {dateSelect.isMonthSelectVisible.value && (\n              <div class={cls.controlSectionCell.value}>\n                <TSelect\n                  autoWidth={true}\n                  v-model={state.curSelectedMonth}\n                  size={state.controlSize}\n                  {...controller.configData.value.month.selectProps}\n                  disabled={dateSelect.isMonthSelectDisabled.value}\n                  options={dateSelect.monthSelectOptionList.value}\n                ></TSelect>\n              </div>\n            )}\n            {modeSelect.isVisible.value && (\n              <div class={cls.controlSectionCell.value} style=\"height: auto\">\n                <TRadioGroup\n                  v-model={state.curSelectedMode}\n                  variant=\"default-filled\"\n                  size={state.controlSize}\n                  {...controller.configData.value.mode.radioGroupProps}\n                  disabled={modeSelect.isDisabled.value}\n                  onChange={controller.emitControllerChange}\n                >\n                  {modeSelect.optionList.value.map((item) => (\n                    <TRadioButton key={item.value} value={item.value}>\n                      {item.label}\n                    </TRadioButton>\n                  ))}\n                </TRadioGroup>\n              </div>\n            )}\n\n            {weekendBtn.isVisible.value && (\n              <div class={cls.controlSectionCell.value}>\n                <TCheckTag\n                  class={cls.controlTag.value}\n                  theme={state.isShowWeekend ? 'default' : 'primary'}\n                  size=\"large\"\n                  {...weekendBtn.vBind.value}\n                  disabled={weekendBtn.isDisabled.value}\n                  onClick={() => {\n                    state.isShowWeekend = !state.isShowWeekend;\n                    controller.emitControllerChange();\n                  }}\n                >\n                  {weekendBtn.text.value}\n                </TCheckTag>\n              </div>\n            )}\n            {currentBtn.isVisible.value && (\n              <div class={cls.controlSectionCell.value}>\n                <TButton\n                  size={state.controlSize}\n                  {...currentBtn.vBind.value}\n                  disabled={currentBtn.isDisabled.value}\n                  onClick={() => {\n                    toToday();\n                  }}\n                >\n                  {currentBtn.text.value}\n                </TButton>\n              </div>\n            )}\n          </div>\n        </div>\n      );\n    };\n\n    const cellClickEmit = (eventPropsName: keyof typeof props, e: MouseEvent, cellData: CalendarCell): void => {\n      if (isFunction(props[eventPropsName])) {\n        const options: CellEventOption = {\n          cell: {\n            ...cellData,\n            ...controller.options.value,\n          },\n          e,\n        };\n        (props[eventPropsName] as Function)(options);\n      }\n    };\n    const clickCell = (e: MouseEvent, cellData: CalendarCell): void => {\n      const d = dayjs(cellData.date);\n      if (props.multiple) {\n        if (state.curDateList.find((item) => item.isSame(d))) {\n          // @ts-ignore @types/lodash 4.17.18\n          state.curDateList = remove(state.curDateList, (item) => !item.isSame(d));\n        } else {\n          state.curDateList.push(d);\n        }\n      } else {\n        state.curDate = d;\n      }\n      cellClickEmit('onCellClick', e, cellData);\n    };\n    const doubleClickCell = (e: MouseEvent, cellData: CalendarCell): void => {\n      cellClickEmit('onCellDoubleClick', e, cellData);\n    };\n    const rightClickCell = (e: MouseEvent, cellData: CalendarCell): void => {\n      if (props.preventCellContextmenu) {\n        e.preventDefault();\n      }\n      cellClickEmit('onCellRightClick', e, cellData);\n    };\n\n    const monthCellsData = computed<CalendarCell[][]>(() => {\n      const daysArr: CalendarCell[][] = utils.createMonthCellsData(props, state);\n      return daysArr;\n    });\n    const renderMonthBody = () => {\n      return (\n        <table class={cls.table.value}>\n          <thead class={cls.tableHead.value}>\n            <tr class={cls.tableHeadRow.value}>\n              {cellColHeaders.value.map(\n                (item, index) =>\n                  checkDayVisible(item.num) && (\n                    <th class={cls.tableHeadCell.value}>\n                      {isArray(props.week)\n                        ? props.week[index]\n                        : renderContent('week', undefined, {\n                            defaultNode: <span>{item.display}</span>,\n                            params: { day: item.num },\n                          })}\n                    </th>\n                  ),\n              )}\n            </tr>\n          </thead>\n\n          <tbody class={cls.tableBody.value}>\n            {monthCellsData.value.map((week, weekIndex) => (\n              <tr class={cls.tableBodyRow.value}>\n                {week.map(\n                  (item, itemIndex) =>\n                    (state.isShowWeekend || item.day < 6) && (\n                      <CalendarCellItem\n                        key={`d-${weekIndex}-${itemIndex}`}\n                        item={item}\n                        theme={props.theme}\n                        t={t}\n                        global={globalConfig.value}\n                        cell={props.cell}\n                        cellAppend={props.cellAppend}\n                        fillWithZero={props.fillWithZero}\n                        onClick={(e: MouseEvent) => clickCell(e, item)}\n                        onDblclick={(e: MouseEvent) => doubleClickCell(e, item)}\n                        onRightclick={(e: MouseEvent) => rightClickCell(e, item)}\n                        v-slots={{ ...slots }}\n                      ></CalendarCellItem>\n                    ),\n                )}\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      );\n    };\n\n    const yearCellsData = computed<CalendarCell[][]>(() => {\n      const re: CalendarCell[][] = [];\n      const monthsArr: CalendarCell[] = utils.createYearCellsData(props, state);\n      const rowCount = Math.ceil(monthsArr.length / DEFAULT_YEAR_CELL_NUMINROW);\n      let index = 0;\n      for (let i = 1; i <= rowCount; i++) {\n        const row: CalendarCell[] = [];\n        for (let j = 1; j <= DEFAULT_YEAR_CELL_NUMINROW; j++) {\n          row.push(monthsArr[index]);\n          index += 1;\n        }\n        re.push(row);\n      }\n      return re;\n    });\n    const renderYearBody = () => {\n      return (\n        <table class={cls.table.value}>\n          <tbody class={cls.tableBody.value}>\n            {yearCellsData.value.map((cell, cellIndex) => (\n              <tr class={cls.tableBodyRow.value}>\n                {cell.map((item, itemIndex) => (\n                  <CalendarCellItem\n                    key={`m-${cellIndex}-${itemIndex}`}\n                    item={item}\n                    theme={props.theme}\n                    t={t}\n                    global={globalConfig.value}\n                    cell={props.cell}\n                    cellAppend={props.cellAppend}\n                    fillWithZero={props.fillWithZero}\n                    onClick={(e: MouseEvent) => clickCell(e, item)}\n                    onDblclick={(e: MouseEvent) => doubleClickCell(e, item)}\n                    onRightclick={(e: MouseEvent) => rightClickCell(e, item)}\n                    v-slots={{ ...slots }}\n                  ></CalendarCellItem>\n                ))}\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      );\n    };\n\n    return () => {\n      return (\n        <div class={cls.body.value}>\n          {controller.visible.value && renderControl()}\n          <div class={cls.panel.value}>{state.curSelectedMode === 'month' ? renderMonthBody() : renderYearBody()}</div>\n        </div>\n      );\n    };\n  },\n});\n"], "names": ["arrayProto", "Array", "prototype", "splice", "basePullAt", "array", "indexes", "length", "lastIndex", "index", "previous", "isIndex", "call", "baseUnset", "remove", "predicate", "result", "baseIteratee", "value", "push", "_isSlot", "s", "Object", "toString", "_isVNode", "defineComponent", "name", "props", "setup", "slots", "_ref", "renderContent", "useContent", "_useConfig", "useConfig", "COMPONENT_NAME", "t", "globalConfig", "_useState", "useState", "state", "toToday", "checkDayVisible", "cls", "useCalendarClass", "_useColHeaders", "useColHeaders", "cellColHeaders", "controller", "userController", "rangeFromTo", "computed", "range", "_props2$range", "_slicedToArray", "v1", "v2", "dayjs", "isBefore", "from", "to", "checkMonthAndYearSelectedDisabled", "year", "month", "disabled", "beginYear", "endYear", "beginMon", "parseInt", "format", "endMon", "adjustMonth", "_rangeFromTo$value", "_rangeFromTo$value2", "curSelectedYear", "curSelected<PERSON>onth", "watch", "concat", "v", "onMonthChange", "emitControllerChange", "dateSelect", "yearSelectOptionList", "re", "begin", "end", "MIN_YEAR", "i", "label", "yearSelection", "isYearSelectVisible", "checkControllerVisible", "isYearSelectDisabled", "checkControllerDisabled", "monthSelectOptionList", "FIRST_MONTH_OF_YEAR", "LAST_MONTH_OF_YEAR", "monthSelection", "isMonthSelectVisible", "curSelectedMode", "isMonthSelectDisabled", "modeSelect", "optionList", "monthRadio", "yearRadio", "isVisible", "isDisabled", "weekendBtn", "text", "isShowWeekend", "hideWeekend", "showWeekend", "vBind", "c", "configData", "weekend", "hideWeekendButtonProps", "showWeekendButtonProps", "theme", "p", "currentBtn", "today", "thisMonth", "current", "currentDayButtonProps", "currentMonthButtonProps", "renderControl", "_slot", "_createVNode", "control", "title", "params", "_objectSpread", "options", "controlSection", "controlSectionCell", "TSelect", "_mergeProps", "controlSize", "selectProps", "onUpdateModelValue", "$event", "TRadioGroup", "mode", "radioGroupProps", "map", "item", "TRadioButton", "_default", "TCheckTag", "controlTag", "onClick", "TButton", "cellClickEmit", "eventPropsName", "e", "cellData", "isFunction", "cell", "clickCell", "d", "date", "multiple", "curDateList", "find", "isSame", "curDate", "doubleClickCell", "rightClickCell", "preventCellContextmenu", "preventDefault", "monthCellsData", "daysArr", "utils", "renderMonthBody", "table", "tableHead", "tableHeadRow", "num", "tableHeadCell", "isArray", "week", "defaultNode", "display", "day", "tableBody", "weekIndex", "tableBodyRow", "itemIndex", "CalendarCellItem", "cellAppend", "fillWithZero", "onDblclick", "onRightclick", "yearCellsData", "monthsArr", "rowCount", "Math", "ceil", "DEFAULT_YEAR_CELL_NUMINROW", "row", "j", "renderYearBody", "cellIndex", "body", "visible", "panel"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA,IAAIA,UAAU,GAAGC,KAAK,CAACC,SAAS,CAAA;;AAEhC;AACA,IAAIC,MAAM,GAAGH,UAAU,CAACG,MAAM,CAAA;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAClC,IAAIC,MAAM,GAAGF,KAAK,GAAGC,OAAO,CAACC,MAAM,GAAG,CAAC;IACnCC,SAAS,GAAGD,MAAM,GAAG,CAAC,CAAA;EAE1B,OAAOA,MAAM,EAAE,EAAE;AACf,IAAA,IAAIE,KAAK,GAAGH,OAAO,CAACC,MAAM,CAAC,CAAA;AAC3B,IAAA,IAAIA,MAAM,IAAIC,SAAS,IAAIC,KAAK,KAAKC,QAAQ,EAAE;MAC7C,IAAIA,QAAQ,GAAGD,KAAK,CAAA;AACpB,MAAA,IAAIE,gBAAO,CAACF,KAAK,CAAC,EAAE;QAClBN,MAAM,CAACS,IAAI,CAACP,KAAK,EAAEI,KAAK,EAAE,CAAC,CAAC,CAAA;AAC9B,OAAC,MAAM;AACLI,QAAAA,cAAS,CAACR,KAAK,EAAEI,KAAK,CAAC,CAAA;AACzB,OAAA;AACF,KAAA;AACF,GAAA;AACA,EAAA,OAAOJ,KAAK,CAAA;AACd;;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,MAAMA,CAACT,KAAK,EAAEU,SAAS,EAAE;EAChC,IAAIC,MAAM,GAAG,EAAE,CAAA;AACf,EAAA,IAAI,EAAEX,KAAK,IAAIA,KAAK,CAACE,MAAM,CAAC,EAAE;AAC5B,IAAA,OAAOS,MAAM,CAAA;AACf,GAAA;EACA,IAAIP,KAAK,GAAG,CAAC,CAAC;AACVH,IAAAA,OAAO,GAAG,EAAE;IACZC,MAAM,GAAGF,KAAK,CAACE,MAAM,CAAA;AAEzBQ,EAAAA,SAAS,GAAGE,0BAAY,CAACF,SAAY,CAAC,CAAA;AACtC,EAAA,OAAO,EAAEN,KAAK,GAAGF,MAAM,EAAE;AACvB,IAAA,IAAIW,KAAK,GAAGb,KAAK,CAACI,KAAK,CAAC,CAAA;IACxB,IAAIM,SAAS,CAACG,KAAK,EAAET,KAAK,EAAEJ,KAAK,CAAC,EAAE;AAClCW,MAAAA,MAAM,CAACG,IAAI,CAACD,KAAK,CAAC,CAAA;AAClBZ,MAAAA,OAAO,CAACa,IAAI,CAACV,KAAK,CAAC,CAAA;AACrB,KAAA;AACF,GAAA;AACAL,EAAAA,UAAU,CAACC,KAAK,EAAEC,OAAO,CAAC,CAAA;AAC1B,EAAA,OAAOU,MAAM,CAAA;AACf;;;;ACzB6B,SAAAI,QAAAC,CAAA,EAAA;AAAA,EAAA,OAAA,OAAAA,CAAA,KAAA,UAAA,IAAAC,MAAA,CAAApB,SAAA,CAAAqB,QAAA,CAAAX,IAAA,CAAAS,CAAA,CAAAG,KAAAA,iBAAAA,IAAAA,CAAAA,WAAA,CAAAH,CAAA,CAAA,CAAA;AAAA,CAAA;AAO7B,gBAAeI,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,WAAA;AACNC,EAAAA,KAAA,EAAAA,yBAAA;AACAC,EAAAA,KAAMD,WAANC,KAAMD,CAAAA,MAAAA,EAAAA,IAAAA,EAAkB;AAAA,IAAA,IAATE,KAAA,GAAAC,IAAA,CAAAD,KAAA,CAAA;AACb,IAAA,IAAME,gBAAgBC,gBAAW,EAAA,CAAA;AACjC,IAAA,IAAAC,UAAA,GAA4BC,yCAAUC,oCAAc,CAAA;MAA5CC,CAAA,GAAAH,UAAA,CAAAG,CAAA;MAAGC,YAAa,GAAAJ,UAAA,CAAbI,YAAa,CAAA;AAExB,IAAA,IAAAC,SAAA,GAA4CC,iCAASZ,MAAK,CAAA;MAAlDa,KAAO,GAAAF,SAAA,CAAPE,KAAO;MAAAC,OAAA,GAAAH,SAAA,CAAAG,OAAA;MAASC,eAAgB,GAAAJ,SAAA,CAAhBI,eAAgB,CAAA;AAGlC,IAAA,IAAAC,GAAA,GAAMC,gDAAiBjB,CAAAA,MAAAA,EAAOa,KAAK,CAAA,CAAA;AAGzC,IAAA,IAAAK,cAAA,GAA2BC,0CAAA,CAAcnB,QAAOa,KAAK,CAAA;MAA7CO,cAAA,GAAAF,cAAA,CAAAE,cAAA,CAAA;AAGF,IAAA,IAAAC,UAAA,GAAaC,2CAAetB,CAAAA,MAAAA,EAAOa,KAAK,CAAA,CAAA;AAGxC,IAAA,IAAAU,WAAA,GAAcC,aAAwB,YAAM;AAChD,MAAA,IAAI,CAACxB,MAAM,CAAAyB,KAAA,IAASzB,MAAM,CAAAyB,KAAA,CAAM7C,SAAS,CAAG,EAAA;AACnC,QAAA,OAAA,IAAA,CAAA;AACT,OAAA;AACA,MAAA,IAAA8C,aAAA,GAAAC,kCAAA,CAAiB3B,MAAM,CAAAyB,KAAA,EAAA,CAAA,CAAA;AAAhBG,QAAAA,EAAA,GAAAF,aAAA,CAAA,CAAA,CAAA;AAAIG,QAAAA,EAAE,GAAAH,aAAA,CAAA,CAAA,CAAA,CAAA;AACb,MAAA,IAAII,0BAAMF,EAAE,CAAA,CAAEG,SAASD,yBAAM,CAAAD,EAAE,CAAC,CAAG,EAAA;QAC1B,OAAA;AACLG,UAAAA,IAAM,EAAAJ,EAAA;AACNK,UAAAA,EAAI,EAAAJ,EAAAA;SACN,CAAA;AACF,OAAA;MACO,OAAA;AACLG,QAAAA,IAAM,EAAAH,EAAA;AACNI,QAAAA,EAAI,EAAAL,EAAAA;OACN,CAAA;AACF,KAAC,CAAA,CAAA;AACQ,IAAA,SAAAM,iCAAAA,CAAkCC,MAAcC,KAAwB,EAAA;MAC/E,IAAIC,QAAW,GAAA,KAAA,CAAA;AACf,MAAA,IAAId,YAAYhC,KAAS,IAAAgC,WAAA,CAAYhC,MAAMyC,IAAQ,IAAAT,WAAA,CAAYhC,MAAM0C,EAAI,EAAA;AACvE,QAAA,IAAMK,YAAYR,yBAAM,CAAAP,WAAA,CAAYhC,KAAM,CAAAyC,IAAI,EAAEG,IAAK,EAAA,CAAA;AACrD,QAAA,IAAMI,UAAUT,yBAAM,CAAAP,WAAA,CAAYhC,KAAM,CAAA0C,EAAE,EAAEE,IAAK,EAAA,CAAA;QACjD,IAAIA,SAASG,SAAW,EAAA;AAChB,UAAA,IAAAE,QAAA,GAAWC,QAAS,CAAAX,yBAAA,CAAMP,WAAY,CAAAhC,KAAA,CAAMyC,IAAI,CAAE,CAAAU,MAAA,CAAO,GAAG,CAAA,EAAG,EAAE,CAAA,CAAA;UACvEL,QAAA,GAAWD,KAAQ,GAAAI,QAAA,CAAA;AACrB,SAAA,MAAA,IAAWL,SAASI,OAAS,EAAA;AACrB,UAAA,IAAAI,MAAA,GAASF,QAAS,CAAAX,yBAAA,CAAMP,WAAY,CAAAhC,KAAA,CAAM0C,EAAE,CAAE,CAAAS,MAAA,CAAO,GAAG,CAAA,EAAG,EAAE,CAAA,CAAA;UACnEL,QAAA,GAAWD,KAAQ,GAAAO,MAAA,CAAA;AACrB,SAAA;AACF,OAAA;AACO,MAAA,OAAAN,QAAA,CAAA;AACT,KAAA;IAEA,SAASO,WAAoBA,GAAA;MAAA,IAAAC,kBAAA,EAAAC,mBAAA,CAAA;MAC3B,IAAI,CAAAD,kBAAA,GAAAtB,WAAY,CAAAhC,KAAA,MAAAsD,IAAAA,IAAAA,kBAAA,KAAZA,KAAAA,CAAAA,IAAAA,kBAAA,CAAmBb,IAAQ,KAAAc,mBAAA,GAAAvB,WAAA,CAAYhC,2CAAZuD,KAAAA,CAAAA,IAAAA,mBAAA,CAAmBb,EAAI,EAAA;AACpD,QAAA,IAAMK,YAAYR,yBAAM,CAAAP,WAAA,CAAYhC,KAAM,CAAAyC,IAAI,EAAEG,IAAK,EAAA,CAAA;AACrD,QAAA,IAAMI,UAAUT,yBAAM,CAAAP,WAAA,CAAYhC,KAAM,CAAA0C,EAAE,EAAEE,IAAK,EAAA,CAAA;AAC3C,QAAA,IAAAK,QAAA,GAAWC,QAAS,CAAAX,yBAAA,CAAMP,WAAY,CAAAhC,KAAA,CAAMyC,IAAI,CAAE,CAAAU,MAAA,CAAO,GAAG,CAAA,EAAG,EAAE,CAAA,CAAA;QACvE,IAAIR,iCAAkC,CAAArB,KAAA,CAAMkC,eAAiB,EAAAlC,KAAA,CAAMmC,gBAAgB,CAAG,EAAA;UAC9EnC,KAAA,CAAAmC,gBAAA,GACJnC,MAAMkC,eAAoB,KAAAT,SAAA,GACtBE,WACA3B,KAAM,CAAAkC,eAAA,KAAoBR,OAC1B,GAAA,CAAA,GACA1B,KAAM,CAAAmC,gBAAA,CAAA;AACd,SAAA;AACF,OAAA;AACF,KAAA;AACAC,IAAAA,SAAA,CACE,YAAM;MACG,OAAA;AACLd,QAAAA,IAAA,KAAAe,MAAA,CAASrC,KAAM,CAAAkC,eAAA,CAAA;AACfX,QAAAA,KAAA,EAAAc,EAAAA,CAAAA,MAAA,CAAUrC,KAAM,CAAAmC,gBAAA,CAAA;OAClB,CAAA;KACF,EACA,UAACG,CAAuC,EAAA;AAC3BnD,MAAAA,qBAAAA,CAAAA,MAAAA,CAAMoD,aAAa,CAAKpD,IAAAA,MAAAA,CAAMoD,4BAAmBD,EAAAA,EAAAA,EAAG,CAAA,CAAA;MAC/D9B,UAAA,CAAWgC,oBAAqB,EAAA,CAAA;AAClC,KACF,CAAA,CAAA;AACA,IAAA,IAAMC,UAAa,GAAA;MACjBC,oBAAA,EAAsB/B,aAA4B,YAAM;QACtD,IAAMgC,KAAwB,EAAC,CAAA;AAC3B,QAAA,IAAAC,KAAA,GAAgB5C,MAAMkC,eAAkB,GAAA,EAAA,CAAA;AACxC,QAAA,IAAAW,GAAA,GAAc7C,MAAMkC,eAAkB,GAAA,EAAA,CAAA;AAC1C,QAAA,IAAIxB,YAAYhC,KAAS,IAAAgC,WAAA,CAAYhC,MAAMyC,IAAQ,IAAAT,WAAA,CAAYhC,MAAM0C,EAAI,EAAA;AACvEwB,UAAAA,KAAA,GAAQ3B,yBAAM,CAAAP,WAAA,CAAYhC,KAAM,CAAAyC,IAAI,EAAEG,IAAK,EAAA,CAAA;AAC3CuB,UAAAA,GAAA,GAAM5B,yBAAM,CAAAP,WAAA,CAAYhC,KAAM,CAAA0C,EAAE,EAAEE,IAAK,EAAA,CAAA;AACzC,SAAA;QAEA,IAAIsB,QAAQE,8BAAU,EAAA;AACZF,UAAAA,KAAA,GAAAE,8BAAA,CAAA;AACV,SAAA;QACA,IAAID,MAAMC,8BAAU,EAAA;AACZD,UAAAA,GAAA,GAAAC,8BAAA,CAAA;AACR,SAAA;QAEA,KAAA,IAASC,CAAI,GAAAH,KAAA,EAAOG,CAAK,IAAAF,GAAA,EAAKE,CAAK,EAAA,EAAA;UACjCJ,EAAA,CAAGhE,IAAK,CAAA;AACND,YAAAA,KAAO,EAAAqE,CAAA;YACPC,KAAA,EAAOpD,EAAEC,YAAa,CAAAnB,KAAA,CAAMuE,eAAe;AAAE3B,cAAAA,IAAA,EAAMyB,CAAAA;AAAE,aAAC,CAAA;AACtDvB,YAAAA,QAAU,EAAA,KAAA;AACZ,WAAC,CAAA,CAAA;AACH,SAAA;AACO,QAAA,OAAAmB,EAAA,CAAA;AACT,OAAC,CAAA;MACDO,mBAAA,EAAqBvC,aAAkB,YAAM;AACpC,QAAA,OAAAH,UAAA,CAAW2C,uBAAuB,MAAM,CAAA,CAAA;AACjD,OAAC,CAAA;MACDC,oBAAA,EAAsBzC,aAAkB,YAAM;AACrC,QAAA,OAAAH,UAAA,CAAW6C,uBAAwB,CAAA,MAAA,EAAQ,aAAa,CAAA,CAAA;AACjE,OAAC,CAAA;MACDC,qBAAA,EAAuB3C,aAA4B,YAAM;AAC3CoB,QAAAA,WAAA,EAAA,CAAA;QACZ,IAAMY,KAAwB,EAAC,CAAA;QAC/B,KAAA,IAASI,CAAI,GAAAQ,yCAAA,EAAqBR,CAAK,IAAAS,wCAAA,EAAoBT,CAAK,EAAA,EAAA;UAC9D,IAAMvB,QAAW,GAAAH,iCAAA,CAAkCrB,KAAM,CAAAkC,eAAA,EAAiBa,CAAC,CAAA,CAAA;UAC3EJ,EAAA,CAAGhE,IAAK,CAAA;AACND,YAAAA,KAAO,EAAAqE,CAAA;YACPC,KAAA,EAAOpD,EAAEC,YAAa,CAAAnB,KAAA,CAAM+E,gBAAgB;AAAElC,cAAAA,KAAA,EAAOwB,CAAAA;AAAE,aAAC,CAAA;AACxDvB,YAAAA,QAAA,EAAAA,QAAAA;AACF,WAAC,CAAA,CAAA;AACH,SAAA;AACO,QAAA,OAAAmB,EAAA,CAAA;AACT,OAAC,CAAA;MACDe,oBAAA,EAAsB/C,aAAkB,YAAM;QAC5C,OAAOX,KAAM,CAAA2D,eAAA,KAAoB,OAAW,IAAAnD,UAAA,CAAW2C,uBAAuB,OAAO,CAAA,CAAA;AACvF,OAAC,CAAA;MACDS,qBAAA,EAAuBjD,aAAkB,YAAM;AACtC,QAAA,OAAAH,UAAA,CAAW6C,uBAAwB,CAAA,OAAA,EAAS,aAAa,CAAA,CAAA;OACjE,CAAA;KACH,CAAA;AAEA,IAAA,IAAMQ,UAAa,GAAA;MACjBC,UAAA,EAAYnD,aAAuB,YAAM;AAChC,QAAA,OAAA,CACL;AAAEjC,UAAAA,OAAO,OAAS;AAAAsE,UAAAA,KAAA,EAAOpD,EAAEC,YAAa,CAAAnB,KAAA,CAAMqF,UAAU,CAAA;AAAE,SAAA,EAC1D;AAAErF,UAAAA,OAAO,MAAQ;AAAAsE,UAAAA,KAAA,EAAOpD,EAAEC,YAAa,CAAAnB,KAAA,CAAMsF,SAAS,CAAA;AAAE,SAAA,CAC1D,CAAA;AACF,OAAC,CAAA;MACDC,SAAA,EAAWtD,aAAkB,YAAM;AAC1B,QAAA,OAAAH,UAAA,CAAW2C,uBAAuB,MAAM,CAAA,CAAA;AACjD,OAAC,CAAA;MACDe,UAAA,EAAYvD,aAAkB,YAAM;AAC3B,QAAA,OAAAH,UAAA,CAAW6C,uBAAwB,CAAA,MAAA,EAAQ,iBAAiB,CAAA,CAAA;OACpE,CAAA;KACH,CAAA;AAEA,IAAA,IAAMc,UAAa,GAAA;MACjBC,IAAA,EAAMzD,aAAiB,YAAM;QACpB,OAAAX,KAAA,CAAMqE,aAAgB,GAAAzE,CAAA,CAAEC,YAAa,CAAAnB,KAAA,CAAM4F,WAAW,CAAI,GAAA1E,CAAA,CAAEC,YAAa,CAAAnB,KAAA,CAAM6F,WAAW,CAAA,CAAA;AACnG,OAAC,CAAA;MACDC,KAAA,EAAO7D,aAAiB,YAAM;QACtB,IAAA8D,CAAA,GAAIjE,UAAW,CAAAkE,UAAA,CAAWhG,KAAM,CAAAiG,OAAA,CAAA;QACtC,OAAO3E,KAAM,CAAAqE,aAAA,GAAgBI,CAAE,CAAAG,sBAAA,GAAyBH,CAAE,CAAAI,sBAAA,CAAA;AAC5D,OAAC,CAAA;MACDZ,SAAA,EAAWtD,aAAkB,YAAM;AAE/BxB,QAAAA,OAAAA,MAAAA,CAAM2F,UAAU,MAChB,IAAAtE,UAAA,CAAW2C,uBAAuB,SAAS,CAAA,IAC3C3C,UAAW,CAAA2C,sBAAA,CAAuB,SAAS,CAAA,CAAA;AAE/C,OAAC,CAAA;MACDe,UAAA,EAAYvD,aAAkB,YAAM;QAC5B,IAAAoE,CAAA,GAAI/E,KAAM,CAAAqE,aAAA,GAAgB,wBAA2B,GAAA,wBAAA,CAAA;AACpD,QAAA,OAAA7D,UAAA,CAAW6C,uBAAwB,CAAA,SAAA,EAAW0B,CAAC,CAAA,CAAA;OACvD,CAAA;KACH,CAAA;AAEA,IAAA,IAAMC,UAAa,GAAA;MACjBZ,IAAA,EAAMzD,aAAiB,YAAM;QACpB,OAAAX,KAAA,CAAM2D,eAAoB,KAAA,OAAA,GAAU/D,CAAE,CAAAC,YAAA,CAAanB,KAAM,CAAAuG,KAAK,CAAI,GAAArF,CAAA,CAAEC,YAAa,CAAAnB,KAAA,CAAMwG,SAAS,CAAA,CAAA;AACzG,OAAC,CAAA;MACDV,KAAA,EAAO7D,aAAiB,YAAM;QACtB,IAAA8D,CAAA,GAAIjE,UAAW,CAAAkE,UAAA,CAAWhG,KAAM,CAAAyG,OAAA,CAAA;AACtC,QAAA,OAAOnF,KAAM,CAAA2D,eAAA,KAAoB,OAAU,GAAAc,CAAA,CAAEW,wBAAwBX,CAAE,CAAAY,uBAAA,CAAA;AACzE,OAAC,CAAA;MACDpB,SAAA,EAAWtD,aAAkB,YAAM;QACjC,OAAOxB,MAAM,CAAA2F,KAAA,KAAU,MAAU,IAAAtE,UAAA,CAAW2C,uBAAuB,SAAS,CAAA,CAAA;AAC9E,OAAC,CAAA;MACDe,UAAA,EAAYvD,aAAS,YAAM;QACzB,IAAMoE,CAAI,GAAA/E,KAAA,CAAM2D,eAAoB,KAAA,OAAA,GAAU,uBAA0B,GAAA,yBAAA,CAAA;AACjE,QAAA,OAAAnD,UAAA,CAAW6C,uBAAwB,CAAA,SAAA,EAAW0B,CAAC,CAAA,CAAA;OACvD,CAAA;KACH,CAAA;AACA,IAAA,IAAMO,gBAAgB,SAAhBA,gBAAsB;AAAA,MAAA,IAAAC,KAAA,CAAA;AAC1B,MAAA,OAAAC,eAAA,CAAA,KAAA,EAAA;QAAA,OACcrF,EAAAA,GAAA,CAAIsF,OAAQ,CAAA/G,KAAAA;AAAA,OAAA,EAAA,CAAA8G,eAAA,CAAA,KAAA,EAAA;QAAA,OACVrF,EAAAA,GAAA,CAAIuF,MAAMhH,KAAAA;AACnB,OAAA,EAAA,CAAAa,aAAA,CAAc,QAAQ,KAAW,CAAA,EAAA;AAChCoG,QAAAA,MAAQ,EAAAC,aAAA,CAAA,EAAA,EAAKpF,UAAA,CAAWqF,QAAQnH,KAAM,CAAA;OACvC;eAESyB,EAAAA,GAAA,CAAI2F,cAAe,CAAApH,KAAAA;AAAA,OAAA,EAAA,CAC5B+D,UAAW,CAAAS,mBAAA,CAAoBxE,KAC9B,IAAA8G,eAAA,CAAA,KAAA,EAAA;QAAA,OAAYrF,EAAAA,GAAA,CAAI4F,kBAAmB,CAAArH,KAAAA;AAAA,OAAA,EAAA,CAAA8G,eAAA,CAAAQ,mBAAA,EAAAC,cAAA,CAAA;QAAA,YAEtBjG,EAAAA,KAAA,CAAMkC;;iBAANlC,KAAA,CAAMkC;;cACTlC,EAAAA,KAAA,CAAMkG,WACZ;QAAA,WAAW,EAAA,IAAA;OACP1F,EAAAA,UAAA,CAAWkE,UAAW,CAAAhG,KAAA,CAAM4C,KAAK6E,WACrC,EAAA;AAAA,QAAA,UAAA,EAAU1D,UAAW,CAAAW,oBAAA,CAAqB1E;iBACjC+D,EAAAA,UAAA,CAAWC,oBAAqB,CAAAhE,KAAAA;AAAA,OAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAP5C,EAWF+D,UAAW,CAAAiB,oBAAA,CAAqBhF,KAC/B,IAAA8G,eAAA,CAAA,KAAA,EAAA;QAAA,OAAYrF,EAAAA,GAAA,CAAI4F,kBAAmB,CAAArH,KAAAA;AAAA,OAAA,EAAA,CAAA8G,eAAA,CAAAQ,mBAAA,EAAAC,cAAA,CAAA;AAAA,QAAA,WAAA,EAEpB,IAAA;QAAA,YACFjG,EAAAA,KAAM,CAAAmC,gBAAA;AAAA,QAAA,qBAAA,EAAA,SAAAiE,mBAAAC,MAAA,EAAA;AAAA,UAAA,OAANrG,KAAM,CAAAmC,gBAAA,GAAAkE,MAAA,CAAA;AAAA,SAAA;AAAA,QAAA,MAAA,EACTrG,KAAA,CAAMkG,WAAAA;OACR1F,EAAAA,UAAA,CAAWkE,UAAW,CAAAhG,KAAA,CAAM6C,MAAM4E,WACtC,EAAA;AAAA,QAAA,UAAA,EAAU1D,UAAW,CAAAmB,qBAAA,CAAsBlF;iBAClC+D,EAAAA,UAAA,CAAWa,qBAAsB,CAAA5E,KAAAA;AAAA,OAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAP7C,EAWFmF,UAAA,CAAWI,SAAU,CAAAvF,KAAA,IAAA8G,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EACRrF,GAAI,CAAA4F,kBAAA,CAAmBrH,KAAO;AAAA,QAAA,OAAA,EAAA,cAAA;AAAA,OAAA,EAAA,CAAA8G,eAAA,CAAAc,sBAAA,EAAAL,cAAA,CAAA;QAAA,YAE7BjG,EAAAA,KAAA,CAAM2D,eACf;AAAA,QAAA,qBAAA,EAAA,SAAAyC,mBAAAC,MAAA,EAAA;AAAA,UAAA,OADSrG,KAAA,CAAM2D,eACf,GAAA0C,MAAA,CAAA;AAAA,SAAA;AAAA,QAAA,SAAA,EAAA,gBAAA;AAAA,QAAA,MAAA,EACMrG,KAAM,CAAAkG,WAAAA;OACR1F,EAAAA,UAAW,CAAAkE,UAAA,CAAWhG,KAAM,CAAA6H,IAAA,CAAKC,eACrC,EAAA;AAAA,QAAA,UAAA,EAAU3C,WAAWK,UAAW,CAAAxF,KAAA;AAAA,QAAA,UAAA,EACtB8B,UAAA,CAAWgC,oBAAAA;AAEpB,OAAA,CAAA,EAAA5D,OAAA,CAAA2G,KAAA,GAAA1B,UAAA,CAAWC,WAAWpF,KAAM,CAAA+H,GAAA,CAAI,UAACC,IAAA,EAAA;QAAA,OAAAlB,eAAA,CAAAmB,uBAAA,EAAA;UAAA,KACbD,EAAAA,IAAK,CAAAhI,KAAA;AAAA,UAAA,OAAA,EAAcgI,IAAA,CAAKhI,KAAAA;AACxC,SAAA,EAAA;AAAA,UAAA,SAAA,EAAA,SAAAkI,QAAA,GAAA;YAAA,OAAAF,CAAAA,IAAA,CAAK1D,KACR,CAAA,CAAA;AAAA,WAAA;AAAA,SAAA,CAAA,CAAA;OACD,CAAA,IAAAuC,KAAA,GAAA;AAAA,QAAA,SAAA,EAAA,SAAAqB,QAAA,GAAA;AAAA,UAAA,OAAA,CAAArB,KAAA,CAAA,CAAA;AAAA,SAAA;AAAA,OAAA,CAAA,CAAA,CAbJ,EAkBFpB,UAAW,CAAAF,SAAA,CAAUvF,KACpB,IAAA8G,eAAA,CAAA,KAAA,EAAA;QAAA,OAAYrF,EAAAA,GAAI,CAAA4F,kBAAA,CAAmBrH,KAAAA;AACjC,OAAA,EAAA,CAAA8G,eAAA,CAAAqB,kBAAA,EAAAZ,cAAA,CAAA;AAAA,QAAA,OAAA,EACS9F,IAAI2G,UAAW,CAAApI,KAAA;AAAA,QAAA,OAAA,EACfsB,KAAA,CAAMqE,aAAgB,GAAA,SAAA,GAAY,SACzC;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,OAAA,EACIF,UAAA,CAAWK,KAAM,CAAA9F,KAAA,EAAA;AAAA,QAAA,UAAA,EACXyF,UAAA,CAAWD,UAAW,CAAAxF,KAAA;QAAA,SACvB,EAAA,SAAAqI,UAAM;AACP/G,UAAAA,KAAA,CAAAqE,aAAA,GAAgB,CAACrE,KAAM,CAAAqE,aAAA,CAAA;UAC7B7D,UAAA,CAAWgC,oBAAqB,EAAA,CAAA;AAClC,SAAA;;;kBAEC2B,UAAW,CAAAC,IAAA,CAAK1F,KACnB,CAAA,CAAA;AAAA,SAAA;AAAA,OAAA,CAAA,CAAA,CAbD,EAgBFsG,UAAA,CAAWf,UAAUvF,KACpB,IAAA8G,eAAA,CAAA,KAAA,EAAA;QAAA,OAAYrF,EAAAA,GAAA,CAAI4F,kBAAmB,CAAArH,KAAAA;AAAA,OAAA,EAAA,CAAA8G,eAAA,CAAAwB,mBAAA,EAAAf,cAAA,CAAA;AAAA,QAAA,MAAA,EAEzBjG,KAAM,CAAAkG,WAAAA;AAAA,OAAA,EACRlB,WAAWR,KAAM,CAAA9F,KAAA,EAAA;AAAA,QAAA,UAAA,EACXsG,UAAW,CAAAd,UAAA,CAAWxF,KAChC;QAAA,SAAS,EAAA,SAAAqI,UAAM;AACL9G,UAAAA,OAAA,EAAA,CAAA;AACV,SAAA;;;kBAEC+E,UAAW,CAAAZ,IAAA,CAAK1F,KACnB,CAAA,CAAA;AAAA,SAAA;OAVD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KAgBX,CAAA;IAEA,IAAMuI,aAAgB,GAAA,SAAhBA,aAAgBA,CAACC,cAAoC,EAAAC,CAAA,EAAeC,QAAiC,EAAA;AACrG,MAAA,IAAAC,qBAAA,CAAWlI,MAAM,CAAA+H,cAAA,CAAe,CAAG,EAAA;AACrC,QAAA,IAAMrB,OAA2B,GAAA;AAC/ByB,UAAAA,IAAM,EAAA1B,aAAA,CAAAA,aAAA,CACDwB,EAAAA,EAAAA,QAAA,CACA5G,EAAAA,WAAWqF,OAAQ,CAAAnH,KAAA,CACxB;AACAyI,UAAAA,CAAA,EAAAA,CAAAA;SACF,CAAA;AACChI,QAAAA,MAAAA,CAAM+H,gBAA6BrB,OAAO,CAAA,CAAA;AAC7C,OAAA;KACF,CAAA;IACM,IAAA0B,SAAA,GAAY,SAAZA,SAAAA,CAAaJ,CAAA,EAAeC,QAAiC,EAAA;AAC3D,MAAA,IAAAI,CAAA,GAAIvG,yBAAM,CAAAmG,QAAA,CAASK,IAAI,CAAA,CAAA;MAC7B,IAAItI,OAAMuI,QAAU,EAAA;AACd,QAAA,IAAA1H,KAAA,CAAM2H,YAAYC,IAAK,CAAA,UAAClB;iBAASA,IAAK,CAAAmB,MAAA,CAAOL,CAAC,CAAC,CAAA;AAAA,SAAA,CAAG,EAAA;UAE9CxH,KAAA,CAAA2H,WAAA,GAAcrJ,MAAO,CAAA0B,KAAA,CAAM2H,WAAa,EAAA,UAACjB;mBAAS,CAACA,IAAA,CAAKmB,MAAO,CAAAL,CAAC,CAAC,CAAA;WAAA,CAAA,CAAA;AACzE,SAAO,MAAA;AACCxH,UAAAA,KAAA,CAAA2H,WAAA,CAAYhJ,KAAK6I,CAAC,CAAA,CAAA;AAC1B,SAAA;AACF,OAAO,MAAA;QACLxH,KAAA,CAAM8H,OAAU,GAAAN,CAAA,CAAA;AAClB,OAAA;AACcP,MAAAA,aAAA,CAAA,aAAA,EAAeE,GAAGC,QAAQ,CAAA,CAAA;KAC1C,CAAA;IACM,IAAAW,eAAA,GAAkB,SAAlBA,eAAAA,CAAmBZ,CAAA,EAAeC,QAAiC,EAAA;AACzDH,MAAAA,aAAA,CAAA,mBAAA,EAAqBE,GAAGC,QAAQ,CAAA,CAAA;KAChD,CAAA;IACM,IAAAY,cAAA,GAAiB,SAAjBA,cAAAA,CAAkBb,CAAA,EAAeC,QAAiC,EAAA;MACtE,IAAIjI,OAAM8I,sBAAwB,EAAA;QAChCd,CAAA,CAAEe,cAAe,EAAA,CAAA;AACnB,OAAA;AACcjB,MAAAA,aAAA,CAAA,kBAAA,EAAoBE,GAAGC,QAAQ,CAAA,CAAA;KAC/C,CAAA;AAEM,IAAA,IAAAe,cAAA,GAAiBxH,aAA2B,YAAM;MACtD,IAAMyH,OAA4B,GAAAC,yCAA2BlJ,CAAAA,MAAAA,EAAOa,KAAK,CAAA,CAAA;AAClE,MAAA,OAAAoI,OAAA,CAAA;AACT,KAAC,CAAA,CAAA;AACD,IAAA,IAAME,kBAAkB,SAAlBA,kBAAwB;AAC5B,MAAA,OAAA9C,eAAA,CAAA,OAAA,EAAA;QAAA,OACgBrF,EAAAA,GAAA,CAAIoI,KAAM,CAAA7J,KAAAA;AAAA,OAAA,EAAA,CAAA8G,eAAA,CAAA,OAAA,EAAA;QAAA,OACRrF,EAAAA,GAAA,CAAIqI,SAAU,CAAA9J,KAAAA;AAAA,OAAA,EAAA,CAAA8G,eAAA,CAAA,IAAA,EAAA;QAAA,OACfrF,EAAAA,GAAA,CAAIsI,YAAa,CAAA/J,KAAAA;OACzB6B,EAAAA,CAAAA,eAAe7B,KAAM,CAAA+H,GAAA,CACpB,UAACC,MAAMzI,KACL,EAAA;AAAA,QAAA,OAAAiC,eAAA,CAAgBwG,KAAKgC,GAAG,CAAA,IAAAlD,eAAA,CAAA,IAAA,EAAA;UAAA,OACXrF,EAAAA,GAAA,CAAIwI,cAAcjK,KAAAA;SAC1BkK,EAAAA,CAAAA,eAAA,CAAQzJ,OAAM0J,IAAI,CAAA,GACf1J,OAAM0J,IAAK,CAAA5K,KAAA,CAAA,GACXsB,aAAc,CAAA,MAAA,EAAQ,KAAW,CAAA,EAAA;AAC/BuJ,UAAAA,WAAa,EAAAtD,eAAA,CAAA,MAAA,EAAA,IAAA,EAAA,CAAOkB,IAAA,CAAKqC,QAAX,CAAA;AACdpD,UAAAA,MAAQ,EAAA;YAAEqD,GAAK,EAAAtC,IAAA,CAAKgC,GAAAA;AAAI,WAAA;AAC1B,SAAC,EANN,CAAA,CAAA;OASP,CAAA,MAAAlD,eAAA,CAAA,OAAA,EAAA;QAAA,OAIUrF,EAAAA,IAAI8I,SAAU,CAAAvK,KAAAA;OACzByJ,EAAAA,CAAAA,eAAezJ,KAAM,CAAA+H,GAAA,CAAI,UAACoC,IAAA,EAAMK;;iBACpB/I,EAAAA,GAAI,CAAAgJ,YAAA,CAAazK,KAAAA;YACzBmK,IAAK,CAAApC,GAAA,CACJ,UAACC,IAAM,EAAA0C,SAAA,EAAA;AAAA,UAAA,OAAA,CACJpJ,KAAM,CAAAqE,aAAA,IAAiBqC,IAAK,CAAAsC,GAAA,GAAM,CACjC,KAAAxD,eAAA,CAAA6D,gCAAA,EAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAAhH,MAAA,CACY6G,SAAA,EAAA7G,GAAAA,CAAAA,CAAAA,MAAA,CAAa+G,SACvB,CAAA;AAAA,YAAA,MAAA,EAAM1C,IACN;YAAA,OAAOvH,EAAAA,MAAM,CAAA2F,KAAA;AAAA,YAAA,GAAA,EACVlF,CAAA;YAAA,QACKC,EAAAA,YAAA,CAAanB,KACrB;YAAA,MAAMS,EAAAA,MAAM,CAAAmI,IAAA;YAAA,YACAnI,EAAAA,MAAAA,CAAMmK;0BACJnK,EAAAA,MAAAA,CAAMoK,YACpB;YAAA,SAAS,EAAA,SAAAxC,QAACI,CAAA,EAAA;AAAA,cAAA,OAAkBI,SAAU,CAAAJ,CAAA,EAAGT,IAAI,CAAA,CAAA;AAAA,aAAA;YAAA,YACjC,EAAA,SAAA8C,WAACrC,CAAkB,EAAA;AAAA,cAAA,OAAAY,eAAA,CAAgBZ,CAAG,EAAAT,IAAI,CACtD,CAAA;AAAA,aAAA;YAAA,cAAc,EAAA,SAAA+C,aAACtC,CAAA,EAAA;AAAA,cAAA,OAAkBa,cAAe,CAAAb,CAAA,EAAGT,IAAI,CAAA,CAAA;AAAA,aAAA;WAAAd,EAAAA,aAAA,CACzCvG,EAAAA,EAAAA,KAAA,CACf,CAAA,CAAA;AAAA,SAEP,CACF,CAAA,CAAA,CAAA;AAAA,OACD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KAIT,CAAA;AAEM,IAAA,IAAAqK,aAAA,GAAgB/I,aAA2B,YAAM;MACrD,IAAMgC,KAAuB,EAAC,CAAA;MAC9B,IAAMgH,SAA4B,GAAAtB,wCAA0BlJ,CAAAA,MAAAA,EAAOa,KAAK,CAAA,CAAA;MACxE,IAAM4J,QAAW,GAAAC,IAAA,CAAKC,IAAK,CAAAH,SAAA,CAAU5L,SAASgM,gDAA0B,CAAA,CAAA;MACxE,IAAI9L,KAAQ,GAAA,CAAA,CAAA;MACZ,KAAA,IAAS8E,CAAI,GAAA,CAAA,EAAGA,CAAK,IAAA6G,QAAA,EAAU7G,CAAK,EAAA,EAAA;QAClC,IAAMiH,MAAsB,EAAC,CAAA;QAC7B,KAAA,IAASC,CAAI,GAAA,CAAA,EAAGA,CAAK,IAAAF,gDAAA,EAA4BE,CAAK,EAAA,EAAA;AAChDD,UAAAA,GAAA,CAAArL,IAAA,CAAKgL,UAAU1L,KAAM,CAAA,CAAA,CAAA;AAChBA,UAAAA,KAAA,IAAA,CAAA,CAAA;AACX,SAAA;AACA0E,QAAAA,EAAA,CAAGhE,KAAKqL,GAAG,CAAA,CAAA;AACb,OAAA;AACO,MAAA,OAAArH,EAAA,CAAA;AACT,KAAC,CAAA,CAAA;AACD,IAAA,IAAMuH,iBAAiB,SAAjBA,iBAAuB;AAC3B,MAAA,OAAA1E,eAAA,CAAA,OAAA,EAAA;QAAA,OACgBrF,EAAAA,IAAIoI,KAAM,CAAA7J,KAAAA;AAAA,OAAA,EAAA,CAAA8G,eAAA,CAAA,OAAA,EAAA;QAAA,OACRrF,EAAAA,GAAI,CAAA8I,SAAA,CAAUvK,KAAAA;OACzBgL,EAAAA,CAAAA,aAAc,CAAAhL,KAAA,CAAM+H,IAAI,UAACa,IAAA,EAAM6C;;iBACnBhK,EAAAA,GAAA,CAAIgJ,aAAazK,KAAAA;AACzB,SAAA,EAAA,CAAA4I,IAAA,CAAKb,IAAI,UAACC,IAAA,EAAM0C;;+BAEHe,SAAA,EAAA9H,GAAAA,CAAAA,CAAAA,MAAA,CAAa+G;oBACjB1C,IAAA;YAAA,OACCvH,EAAAA,MAAM,CAAA2F,KAAA;AAAA,YAAA,GAAA,EACVlF,CACH;YAAA,QAAQC,EAAAA,YAAa,CAAAnB,KAAA;YAAA,MACfS,EAAAA,MAAM,CAAAmI,IAAA;YAAA,YACAnI,EAAAA,MAAM,CAAAmK,UAAA;YAAA,cACJnK,EAAAA,MAAM,CAAAoK,YAAA;YAAA,SACX,EAAA,SAAAxC,QAACI,CAAA,EAAA;AAAA,cAAA,OAAkBI,UAAUJ,CAAG,EAAAT,IAAI;;wBACjC,EAAA,SAAA8C,WAACrC;qBAAkBY,eAAgB,CAAAZ,CAAA,EAAGT,IAAI,CAAA,CAAA;AAAA,aAAA;YAAA,cACxC,EAAA,SAAA+C,aAACtC,CAAA,EAAA;AAAA,cAAA,OAAkBa,eAAeb,CAAG,EAAAT,IAAI;;+BACzCrH,KAAM,CAAA,CAAA,CAAA;AAAA,SAEvB,CACH,CAAA,CAAA,CAAA;AAAA,OACD,CACH,CAAA,CAAA,CAAA,CAAA,CAAA;KAGN,CAAA;AAEA,IAAA,OAAO,YAAM;AACX,MAAA,OAAAmG,eAAA,CAAA,KAAA,EAAA;QAAA,OACcrF,EAAAA,GAAA,CAAIiK,IAAK,CAAA1L,KAAAA;OAClB8B,EAAAA,CAAAA,UAAA,CAAW6J,OAAQ,CAAA3L,KAAA,IAAS4G,aAAc,EAAA,EAAAE,eAAA,CAAA,KAAA,EAAA;QAAA,OAC/BrF,EAAAA,GAAA,CAAImK,KAAM,CAAA5L,KAAAA;AAAA,OAAA,EAAA,CAAQsB,KAAM,CAAA2D,eAAA,KAAoB,OAAU,GAAA2E,eAAA,EAAoB,GAAA4B,cAAA;KAG5F,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}