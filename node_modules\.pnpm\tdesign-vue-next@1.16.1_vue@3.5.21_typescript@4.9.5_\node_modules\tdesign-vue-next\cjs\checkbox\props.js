/**
 * tdesign v1.16.1
 * (c) 2025 tdesign
 * @license MIT
 */

'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var props = {
  checkAll: Boolean,
  checked: {
    type: Boolean,
    "default": void 0
  },
  modelValue: {
    type: Boolean,
    "default": void 0
  },
  defaultChecked: Boolean,
  "default": {
    type: [String, Function]
  },
  disabled: {
    type: Boolean,
    "default": void 0
  },
  indeterminate: Boolean,
  label: {
    type: [String, Function]
  },
  lazyLoad: Boolean,
  name: {
    type: String,
    "default": ""
  },
  readonly: {
    type: <PERSON><PERSON><PERSON>,
    "default": void 0
  },
  title: {
    type: String,
    "default": ""
  },
  value: {
    type: [String, Number, Boolean]
  },
  onChange: Function
};

exports["default"] = props;
//# sourceMappingURL=props.js.map
