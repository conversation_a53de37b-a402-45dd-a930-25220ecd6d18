import { TdCheckboxGroupProps } from '../checkbox/type';
import { PropType } from 'vue';
declare const _default: {
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    lazyLoad: BooleanConstructor;
    max: {
        type: NumberConstructor;
        default: any;
    };
    name: {
        type: StringConstructor;
        default: string;
    };
    options: {
        type: PropType<TdCheckboxGroupProps["options"]>;
    };
    readonly: {
        type: BooleanConstructor;
        default: any;
    };
    value: {
        type: PropType<TdCheckboxGroupProps["value"]>;
        default: TdCheckboxGroupProps["value"];
    };
    modelValue: {
        type: PropType<TdCheckboxGroupProps["value"]>;
        default: TdCheckboxGroupProps["value"];
    };
    defaultValue: {
        type: PropType<TdCheckboxGroupProps["defaultValue"]>;
        default: () => TdCheckboxGroupProps["defaultValue"];
    };
    onChange: PropType<TdCheckboxGroupProps["onChange"]>;
};
export default _default;
