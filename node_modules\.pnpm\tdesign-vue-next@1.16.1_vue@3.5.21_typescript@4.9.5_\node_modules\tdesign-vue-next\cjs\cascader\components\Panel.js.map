{"version": 3, "file": "Panel.js", "sources": ["../../../../components/cascader/components/Panel.tsx"], "sourcesContent": ["import { defineComponent, PropType, computed, h } from 'vue';\n\nimport Item from './Item';\nimport { TreeNode, CascaderContextType } from '../types';\nimport CascaderProps from '../props';\nimport { useConfig, usePrefixClass, useTNodeDefault } from '@tdesign/shared-hooks';\n\nimport { getDefaultNode } from '@tdesign/shared-utils';\nimport { getPanels, expendClickEffect, valueChangeEffect } from '../utils';\n\nexport default defineComponent({\n  name: 'TCascaderSubPanel',\n  props: {\n    option: CascaderProps.option,\n    empty: CascaderProps.empty,\n    trigger: CascaderProps.trigger,\n    onChange: CascaderProps.onChange,\n    loading: CascaderProps.loading,\n    loadingText: CascaderProps.loadingText,\n    cascaderContext: {\n      type: Object as PropType<CascaderContextType>,\n    },\n  },\n\n  setup(props) {\n    const renderTNodeJSXDefault = useTNodeDefault();\n    const COMPONENT_NAME = usePrefixClass('cascader');\n    const { globalConfig } = useConfig('cascader');\n\n    const panels = computed(() => getPanels(props.cascaderContext.treeNodes));\n\n    const handleExpand = (node: TreeNode, trigger: 'hover' | 'click') => {\n      const { trigger: propsTrigger, cascaderContext } = props;\n      expendClickEffect(propsTrigger, trigger, node, cascaderContext);\n    };\n\n    const renderItem = (node: TreeNode, index: number) => {\n      const optionChild = node.data.content\n        ? getDefaultNode(node.data.content(h))\n        : renderTNodeJSXDefault('option', {\n            params: { item: node.data, index },\n          });\n      return (\n        <Item\n          key={node.value}\n          node={node}\n          optionChild={optionChild}\n          cascaderContext={props.cascaderContext}\n          onClick={() => {\n            handleExpand(node, 'click');\n          }}\n          onMouseenter={() => {\n            handleExpand(node, 'hover');\n          }}\n          onChange={() => {\n            valueChangeEffect(node, props.cascaderContext);\n          }}\n        />\n      );\n    };\n\n    const renderList = (treeNodes: TreeNode[], isFilter = false, segment = true, index = 1) => (\n      <ul\n        class={[\n          `${COMPONENT_NAME.value}__menu`,\n          'narrow-scrollbar',\n          {\n            [`${COMPONENT_NAME.value}__menu--segment`]: segment,\n            [`${COMPONENT_NAME.value}__menu--filter`]: isFilter,\n          },\n        ]}\n        key={`${COMPONENT_NAME}__menu${index}`}\n      >\n        {treeNodes.map((node: TreeNode) => renderItem(node, index))}\n      </ul>\n    );\n\n    const renderPanels = () => {\n      const { inputVal, treeNodes } = props.cascaderContext;\n      return inputVal\n        ? renderList(treeNodes, true)\n        : panels.value.map((treeNodes, index: number) =>\n            renderList(treeNodes, false, index !== panels.value.length - 1, index),\n          );\n    };\n\n    return () => {\n      let content;\n      if (props.loading) {\n        content = renderTNodeJSXDefault(\n          'loadingText',\n          <div class={`${COMPONENT_NAME.value}__panel--empty`}>{globalConfig.value.loadingText}</div>,\n        );\n      } else {\n        content = panels.value.length\n          ? renderPanels()\n          : renderTNodeJSXDefault(\n              'empty',\n              <div class={`${COMPONENT_NAME.value}__panel--empty`}>{globalConfig.value.empty}</div>,\n            );\n      }\n      return (\n        <div\n          class={[\n            `${COMPONENT_NAME.value}__panel`,\n            { [`${COMPONENT_NAME.value}--normal`]: panels.value.length && !props.loading },\n          ]}\n        >\n          {content}\n        </div>\n      );\n    };\n  },\n});\n"], "names": ["defineComponent", "name", "props", "option", "CascaderProps", "empty", "trigger", "onChange", "loading", "loadingText", "cascaderContext", "type", "Object", "setup", "renderTNodeJSXDefault", "useTNodeDefault", "COMPONENT_NAME", "usePrefixClass", "_useConfig", "useConfig", "globalConfig", "panels", "computed", "getPanels", "treeNodes", "handleExpand", "node", "propsTrigger", "expendClickEffect", "renderItem", "index", "option<PERSON><PERSON>d", "data", "content", "getDefaultNode", "h", "params", "item", "_createVNode", "<PERSON><PERSON>", "value", "onClick", "onMouseenter", "valueChangeEffect", "renderList", "isFilter", "arguments", "length", "undefined", "segment", "concat", "_defineProperty", "map", "renderPanels", "_props$cascaderContex", "inputVal"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,YAAeA,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,mBAAA;AACNC,EAAAA,KAAO,EAAA;IACLC,QAAQC,yBAAc,CAAAD,MAAA;IACtBE,OAAOD,yBAAc,CAAAC,KAAA;IACrBC,SAASF,yBAAc,CAAAE,OAAA;IACvBC,UAAUH,yBAAc,CAAAG,QAAA;IACxBC,SAASJ,yBAAc,CAAAI,OAAA;IACvBC,aAAaL,yBAAc,CAAAK,WAAA;AAC3BC,IAAAA,eAAiB,EAAA;AACfC,MAAAA,IAAM,EAAAC,MAAAA;AACR,KAAA;GACF;AAEAC,EAAAA,OAAAA,SAAAA,MAAMX,KAAO,EAAA;AACX,IAAA,IAAMY,wBAAwBC,qBAAgB,EAAA,CAAA;AACxC,IAAA,IAAAC,cAAA,GAAiBC,uBAAe,UAAU,CAAA,CAAA;AAChD,IAAA,IAAAC,UAAA,GAAyBC,wCAAA,CAAU,UAAU,CAAA;MAArCC,YAAA,GAAAF,UAAA,CAAAE,YAAA,CAAA;IAER,IAAMC,SAASC,YAAS,CAAA,YAAA;AAAA,MAAA,OAAMC,gCAAUrB,KAAM,CAAAQ,eAAA,CAAgBc,SAAS,CAAC,CAAA;KAAA,CAAA,CAAA;IAElE,IAAAC,YAAA,GAAe,SAAfA,YAAAA,CAAgBC,IAAA,EAAgBpB,OAA+B,EAAA;AACnE,MAAA,IAAiBqB,YAAc,GAAoBzB,KAAA,CAA3CI,OAAA;QAAuBI,eAAA,GAAoBR,KAAA,CAApBQ,eAAA,CAAA;MACbkB,uCAAA,CAAAD,YAAA,EAAcrB,OAAS,EAAAoB,IAAA,EAAMhB,eAAe,CAAA,CAAA;KAChE,CAAA;IAEM,IAAAmB,UAAA,GAAa,SAAbA,UAAAA,CAAcH,IAAA,EAAgBI,KAAkB,EAAA;MACpD,IAAMC,WAAc,GAAAL,IAAA,CAAKM,IAAK,CAAAC,OAAA,GAC1BC,0BAAe,CAAAR,IAAA,CAAKM,IAAK,CAAAC,OAAA,CAAQE,KAAC,CAAC,CACnC,GAAArB,qBAAA,CAAsB,QAAU,EAAA;AAC9BsB,QAAAA,MAAQ,EAAA;UAAEC,IAAM,EAAAX,IAAA,CAAKM;AAAMF,UAAAA,KAAM,EAANA,KAAAA;AAAM,SAAA;AACnC,OAAC,CAAA,CAAA;MACL,OAAAQ,eAAA,CAAAC,mCAAA,EAAA;QAAA,KAESb,EAAAA,IAAA,CAAKc,KACV;AAAA,QAAA,MAAA,EAAMd,IACN;AAAA,QAAA,aAAA,EAAaK,WACb;QAAA,iBAAiB7B,EAAAA,KAAM,CAAAQ,eAAA;QAAA,SACd,EAAA,SAAA+B,UAAM;AACbhB,UAAAA,YAAA,CAAaC,MAAM,OAAO,CAAA,CAAA;SAC5B;QAAA,cACc,EAAA,SAAAgB,eAAM;AAClBjB,UAAAA,YAAA,CAAaC,MAAM,OAAO,CAAA,CAAA;SAC5B;QAAA,UACU,EAAA,SAAAnB,WAAM;AACIoC,UAAAA,uCAAA,CAAAjB,IAAA,EAAMxB,MAAMQ,eAAe,CAAA,CAAA;AAC/C,SAAA;AACF,OAAA,EAAA,IAAA,CAAA,CAAA;KAEJ,CAAA;AAEM,IAAA,IAAAkC,UAAA,GAAa,SAAbA,UAAAA,CAAcpB,SAAA,EAAA;AAAA,MAAA,IAAuBqB,QAAW,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAA,KAAA,CAAA;AAAA,MAAA,IAAOG,OAAU,GAAAH,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA;AAAA,MAAA,IAAMhB,KAAQ,GAAAgB,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AAAA,MAAA,OAAAR,eAAA,CAAA,IAAA,EAAA;AAAA,QAAA,OAAA,EAE1E,CAAAY,EAAAA,CAAAA,MAAA,CACFlC,cAAe,CAAAwB,KAAA,EAClB,QAAA,CAAA,EAAA,kBAAA,EAAAW,mCAAA,CAAAA,mCAAA,CAAAD,EAAAA,EAAAA,EAAAA,CAAAA,MAAA,CAEMlC,cAAA,CAAewB,KAAyB,EAAAS,iBAAAA,CAAAA,EAAAA,OAAA,CAAAC,EAAAA,EAAAA,CAAAA,MAAA,CACxClC,cAAA,CAAewB,KAAwB,EAAAK,gBAAAA,CAAAA,EAAAA,QAAA,CAG/C,CAAA;AAAA,QAAA,KAAA,EAAA,EAAA,CAAAK,MAAA,CAAQlC,cAAA,EAAAkC,QAAAA,CAAAA,CAAAA,MAAA,CAAuBpB;UAE9BN,SAAU,CAAA4B,GAAA,CAAI,UAAC1B,IAAA,EAAA;AAAA,QAAA,OAAmBG,UAAW,CAAAH,IAAA,EAAMI,KAAK,CAAC,CAAA;;KAX3D,CAAA;AAeH,IAAA,IAAMuB,eAAe,SAAfA,eAAqB;AACzB,MAAA,IAAAC,qBAAA,GAAgCpD,KAAM,CAAAQ,eAAA;QAA9B6C,QAAA,GAAAD,qBAAA,CAAAC,QAAA;QAAU/B,SAAU,GAAA8B,qBAAA,CAAV9B,SAAU,CAAA;AAC5B,MAAA,OAAO+B,WACHX,UAAW,CAAApB,SAAA,EAAW,IAAI,CAAA,GAC1BH,OAAOmB,KAAM,CAAAY,GAAA,CAAI,UAAC5B,UAAW,EAAAM,KAAA,EAAA;AAAA,QAAA,OAC3Bc,UAAWpB,CAAAA,UAAAA,EAAW,KAAO,EAAAM,KAAA,KAAUT,MAAO,CAAAmB,KAAA,CAAMO,MAAS,GAAA,CAAA,EAAGjB,KAAK,CAAA,CAAA;AAAA,OACvE,CAAA,CAAA;KACN,CAAA;AAEA,IAAA,OAAO,YAAM;AACP,MAAA,IAAAG,OAAA,CAAA;MACJ,IAAI/B,MAAMM,OAAS,EAAA;AACPyB,QAAAA,OAAA,GAAAnB,qBAAA,CACR,aAAA,EAAAwB,eAAA,CAAA,KAAA,EAAA;AAAA,UAAA,OAAA,EAAA,EAAA,CAAAY,MAAA,CACelC,eAAewB,KAAwB,EAAA,gBAAA,CAAA;AAAA,SAAA,EAAA,CAAApB,YAAA,CAAaoB,KAAM,CAAA/B,WAAA,EAC3E,CAAA,CAAA;AACF,OAAO,MAAA;AACLwB,QAAAA,OAAA,GAAUZ,MAAO,CAAAmB,KAAA,CAAMO,MACnB,GAAAM,YAAA,EACA,GAAAvC,qBAAA,CACE,OAAA,EAAAwB,eAAA,CAAA,KAAA,EAAA;AAAA,UAAA,OAAA,EAAA,EAAA,CAAAY,MAAA,CACelC,eAAewB,KAAwB,EAAA,gBAAA,CAAA;AAAA,SAAA,EAAA,CAAApB,YAAA,CAAaoB,KAAM,CAAAnC,KAAA,EAC3E,CAAA,CAAA;AACN,OAAA;AAEE,MAAA,OAAAiC,eAAA,CAAA,KAAA,EAAA;QAAA,OACS,EAAA,CAAA,EAAA,CAAAY,MAAA,CACFlC,cAAe,CAAAwB,KAAA,EAAAW,SAAAA,CAAAA,EAAAA,mCAAA,CAAAD,EAAAA,EAAAA,EAAAA,CAAAA,MAAA,CACZlC,cAAe,CAAAwB,KAAA,EAAA,UAAA,CAAA,EAAkBnB,OAAOmB,KAAM,CAAAO,MAAA,IAAU,CAAC7C,KAAA,CAAMM,OAAQ,CAAA,CAAA;AAC/E,OAAA,EAAA,CAECyB;KAGP,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}