{"version": 3, "file": "calendar-cell.js", "sources": ["../../../components/calendar/calendar-cell.tsx"], "sourcesContent": ["import { computed, defineComponent } from 'vue';\nimport dayjs from 'dayjs';\n\nimport { useContent, useCommonClassName } from '@tdesign/shared-hooks';\n\nimport { useCalendarCellClass } from './hooks';\n\n// 组件相关的自定义类型\nimport { CalendarCell } from './type';\n\nconst clickTypeEmitEventMap = {\n  click: 'click',\n  dblclick: 'dblclick',\n  contextmenu: 'rightclick',\n};\n\nexport default defineComponent({\n  name: `TCalendarCell`,\n  inheritAttrs: false,\n  props: {\n    item: {\n      type: Object,\n      default: (): CalendarCell => null,\n    },\n    fillWithZero: {\n      type: Boolean,\n      default: undefined,\n    },\n    theme: {\n      type: String,\n      default: (): string => null,\n    },\n    t: Function,\n    global: Object,\n    cell: [String, Function],\n    cellAppend: [String, Function],\n  },\n  emits: [...Object.values(clickTypeEmitEventMap)],\n  setup(props, { emit }) {\n    const renderContent = useContent();\n    const cls = useCalendarCellClass();\n    const { STATUS } = useCommonClassName();\n\n    const valueDisplay = computed<string>(() => {\n      if (props.item.mode === 'month') {\n        const dateNum = props.item.date.getDate();\n        const fillZero = dateNum < 10 && (props.fillWithZero ?? props.global.fillWithZero ?? true);\n        return fillZero ? `0${dateNum}` : dateNum;\n      }\n      const map = props.t(props.global.cellMonth).split(',');\n      return map[props.item.date.getMonth().toString()];\n    });\n    const allowSlot = computed<boolean>(() => {\n      return props.theme === 'full';\n    });\n    const disabled = computed<boolean>(() => {\n      return props.item.mode === 'month' && props.item.belongTo !== 0;\n    });\n    const cellCls = computed(() => {\n      const { mode, date, formattedDate, isCurrent } = props.item;\n      const now = new Date();\n      const isNow =\n        mode === 'year'\n          ? now.getMonth() === date.getMonth() && now.getFullYear() === date.getFullYear()\n          : formattedDate === dayjs().format('YYYY-MM-DD');\n      return [\n        cls.tableBodyCell.value,\n        {\n          [STATUS.value.disabled]: disabled.value,\n          [STATUS.value.checked]: isCurrent,\n          [cls.tableBodyCell4Now.value]: isNow,\n        },\n      ];\n    });\n    const clickCell = (e: MouseEvent): void => {\n      if (disabled.value) return;\n      const emitName = clickTypeEmitEventMap[e.type as keyof typeof clickTypeEmitEventMap];\n      emit(emitName, e);\n    };\n\n    const renderDefaultNode = () => (\n      <>\n        <div class={cls.tableBodyCellDisplay.value}>{valueDisplay.value}</div>\n        <div class={cls.tableBodyCellCsontent.value}>\n          {allowSlot.value &&\n            renderContent('cellAppend', undefined, {\n              params: { ...props.item },\n            })}\n        </div>\n      </>\n    );\n\n    return () => {\n      return (\n        props.item && (\n          <td class={cellCls.value} onClick={clickCell} onDblclick={clickCell} onContextmenu={clickCell}>\n            {renderContent('cell', undefined, {\n              defaultNode: renderDefaultNode(),\n              params: { ...props.item },\n            })}\n          </td>\n        )\n      );\n    };\n  },\n});\n"], "names": ["clickTypeEmitEventMap", "click", "dblclick", "contextmenu", "defineComponent", "name", "inheritAttrs", "props", "item", "type", "Object", "default", "fillWithZero", "Boolean", "theme", "String", "t", "Function", "global", "cell", "cellAppend", "emits", "values", "setup", "_ref", "emit", "renderContent", "useContent", "cls", "useCalendarCellClass", "_useCommonClassName", "useCommonClassName", "STATUS", "valueDisplay", "computed", "mode", "_ref2", "_props$fillWithZero", "dateNum", "date", "getDate", "fillZero", "concat", "map", "cellMonth", "split", "getMonth", "toString", "allowSlot", "disabled", "belongTo", "cellCls", "_props$item", "formattedDate", "isCurrent", "now", "Date", "isNow", "getFullYear", "dayjs", "format", "tableBodyCell", "value", "_defineProperty", "checked", "tableBodyCell4Now", "clickCell", "e", "emitName", "renderDefaultNode", "tableBodyCellDisplay", "tableBodyCellCsontent", "params", "_objectSpread", "_createVNode", "defaultNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAMA,qBAAwB,GAAA;AAC5BC,EAAAA,KAAO,EAAA,OAAA;AACPC,EAAAA,QAAU,EAAA,UAAA;AACVC,EAAAA,WAAa,EAAA,YAAA;AACf,CAAA,CAAA;AAEA,uBAAeC,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,eAAA;AACNC,EAAAA,YAAc,EAAA,KAAA;AACdC,EAAAA,KAAO,EAAA;AACLC,IAAAA,IAAM,EAAA;AACJC,MAAAA,IAAM,EAAAC,MAAA;MACN,SAAS,EAAA,SAATC;eAA6B,IAAA,CAAA;AAAA,OAAA;KAC/B;AACAC,IAAAA,YAAc,EAAA;AACZH,MAAAA,IAAM,EAAAI,OAAA;AACN,MAAA,SAAA,EAAS,KAAA,CAAA;KACX;AACAC,IAAAA,KAAO,EAAA;AACLL,MAAAA,IAAM,EAAAM,MAAA;MACN,SAAS,EAAA,SAATJ;eAAuB,IAAA,CAAA;AAAA,OAAA;KACzB;AACAK,IAAAA,CAAG,EAAAC,QAAA;AACHC,IAAAA,MAAQ,EAAAR,MAAA;AACRS,IAAAA,IAAA,EAAM,CAACJ,MAAA,EAAQE,QAAQ,CAAA;AACvBG,IAAAA,UAAA,EAAY,CAACL,MAAA,EAAQE,QAAQ,CAAA;GAC/B;EACAI,8CAAWX,MAAO,CAAAY,MAAA,CAAOtB,qBAAqB,CAAC,CAAA;AAC/CuB,EAAAA,KAAM,WAANA,KAAMA,CAAAhB,KAAA,EAAAiB,IAAA,EAAiB;AAAA,IAAA,IAARC,IAAA,GAAAD,IAAA,CAAAC,IAAA,CAAA;AACb,IAAA,IAAMC,gBAAgBC,gBAAW,EAAA,CAAA;AACjC,IAAA,IAAMC,MAAMC,oDAAqB,EAAA,CAAA;AAC3B,IAAA,IAAAC,mBAAA,GAAaC,0BAAmB,EAAA;MAA9BC,MAAO,GAAAF,mBAAA,CAAPE,MAAO,CAAA;AAET,IAAA,IAAAC,YAAA,GAAeC,aAAiB,YAAM;AACtC,MAAA,IAAA3B,KAAA,CAAMC,IAAK,CAAA2B,IAAA,KAAS,OAAS,EAAA;QAAA,IAAAC,KAAA,EAAAC,mBAAA,CAAA;QAC/B,IAAMC,OAAU,GAAA/B,KAAA,CAAMC,IAAK,CAAA+B,IAAA,CAAKC,OAAQ,EAAA,CAAA;AACxC,QAAA,IAAMC,WAAWH,OAAU,GAAA,EAAA,KAAA,CAAAF,KAAA,GAAA,CAAAC,mBAAA,GAAO9B,MAAMK,YAAgB,MAAA,IAAA,IAAAyB,mBAAA,KAAA,KAAA,CAAA,GAAAA,mBAAA,GAAA9B,KAAA,CAAMW,OAAON,YAAgB,MAAA,IAAA,IAAAwB,KAAA,KAAA,KAAA,CAAA,GAAAA,KAAA,GAAA,IAAA,CAAA,CAAA;AAC9E,QAAA,OAAAK,QAAA,GAAAC,GAAAA,CAAAA,MAAA,CAAeJ,OAAY,IAAAA,OAAA,CAAA;AACpC,OAAA;AACM,MAAA,IAAAK,GAAA,GAAMpC,MAAMS,CAAE,CAAAT,KAAA,CAAMW,OAAO0B,SAAS,CAAA,CAAEC,MAAM,GAAG,CAAA,CAAA;AACrD,MAAA,OAAOF,IAAIpC,KAAM,CAAAC,IAAA,CAAK+B,IAAK,CAAAO,QAAA,GAAWC,QAAS,EAAA,CAAA,CAAA;AACjD,KAAC,CAAA,CAAA;AACK,IAAA,IAAAC,SAAA,GAAYd,aAAkB,YAAM;AACxC,MAAA,OAAO3B,MAAMO,KAAU,KAAA,MAAA,CAAA;AACzB,KAAC,CAAA,CAAA;AACK,IAAA,IAAAmC,QAAA,GAAWf,aAAkB,YAAM;AACvC,MAAA,OAAO3B,MAAMC,IAAK,CAAA2B,IAAA,KAAS,OAAW,IAAA5B,KAAA,CAAMC,KAAK0C,QAAa,KAAA,CAAA,CAAA;AAChE,KAAC,CAAA,CAAA;AACK,IAAA,IAAAC,OAAA,GAAUjB,aAAS,YAAM;AAC7B,MAAA,IAAAkB,WAAA,GAAiD7C,KAAM,CAAAC,IAAA;QAA/C2B,IAAM,GAAAiB,WAAA,CAANjB,IAAM;QAAAI,IAAA,GAAAa,WAAA,CAAAb,IAAA;QAAMc,aAAe,GAAAD,WAAA,CAAfC,aAAe;QAAAC,SAAA,GAAAF,WAAA,CAAAE,SAAA,CAAA;AAC7B,MAAA,IAAAC,GAAA,GAAM,IAAIC,IAAK,EAAA,CAAA;AACrB,MAAA,IAAMC,QACJtB,IAAS,KAAA,MAAA,GACLoB,IAAIT,QAAS,EAAA,KAAMP,KAAKO,QAAS,EAAA,IAAKS,IAAIG,WAAY,EAAA,KAAMnB,KAAKmB,WAAY,EAAA,GAC7EL,kBAAkBM,yBAAM,EAAA,CAAEC,OAAO,YAAY,CAAA,CAAA;AAC5C,MAAA,OAAA,CACLhC,IAAIiC,aAAc,CAAAC,KAAA,EAAAC,mCAAA,CAAAA,mCAAA,CAAAA,mCAAA,CAAA,EAAA,EAEf/B,MAAA,CAAO8B,KAAM,CAAAb,QAAA,EAAWA,QAAS,CAAAa,KAAA,CACjC9B,EAAAA,MAAO,CAAA8B,KAAA,CAAME,OAAU,EAAAV,SAAA,CAAA,EACvB1B,GAAI,CAAAqC,iBAAA,CAAkBH,KAAQ,EAAAL,KAAA,CAEnC,CAAA,CAAA;AACF,KAAC,CAAA,CAAA;AACK,IAAA,IAAAS,SAAA,GAAY,SAAZA,SAAAA,CAAaC,CAAwB,EAAA;MACzC,IAAIlB,QAAS,CAAAa,KAAA,EAAO,OAAA;AACd,MAAA,IAAAM,QAAA,GAAWpE,sBAAsBmE,CAAE,CAAA1D,IAAA,CAAA,CAAA;AACzCgB,MAAAA,IAAA,CAAK2C,UAAUD,CAAC,CAAA,CAAA;KAClB,CAAA;AAEA,IAAA,IAAME,oBAAoB,SAApBA;;eAEUzC,EAAAA,GAAA,CAAI0C,qBAAqBR,KAAAA;AAAQ,OAAA,EAAA,CAAA7B,YAAA,CAAa6B;eAC9ClC,EAAAA,GAAI,CAAA2C,qBAAA,CAAsBT,KAAAA;OACnCd,EAAAA,CAAAA,SAAU,CAAAc,KAAA,IACTpC,aAAc,CAAA,YAAA,EAAc,KAAW,CAAA,EAAA;AACrC8C,QAAAA,MAAQ,EAAAC,aAAA,CAAKlE,EAAAA,EAAAA,KAAA,CAAMC,IAAK,CAAA;AAC1B,OAAC;KAEP,CAAA;AAGF,IAAA,OAAO,YAAM;AACX,MAAA,OACED,KAAM,CAAAC,IAAA,IAAAkE,eAAA,CAAA,IAAA,EAAA;QAAA,OACOvB,EAAAA,OAAQ,CAAAW,KAAA;AAAA,QAAA,SAAA,EAAgBI,SAAA;AAAA,QAAA,YAAA,EAAuBA,SAAW;QAAA,eAAeA,EAAAA,SAAAA;AACjF,OAAA,EAAA,CAAAxC,aAAA,CAAc,QAAQ,KAAW,CAAA,EAAA;QAChCiD,aAAaN,iBAAkB,EAAA;AAC/BG,QAAAA,MAAQ,EAAAC,aAAA,CAAKlE,EAAAA,EAAAA,KAAA,CAAMC,IAAK,CAAA;AAC1B,OAAC,EAJF,CAAA,CAAA;KAQP,CAAA;AACF,GAAA;AACF,CAAC,CAAA;;;;"}