{"version": 3, "file": "index.js", "sources": ["../../../components/checkbox/index.ts"], "sourcesContent": ["import _Checkbox from './checkbox';\nimport _Group from './group';\nimport { withInstall } from '@tdesign/shared-utils';\nimport { TdCheckboxProps, TdCheckboxGroupProps } from './type';\n\nimport './style';\n\nexport * from './type';\nexport type CheckboxProps = TdCheckboxProps;\nexport type CheckboxGroupProps = TdCheckboxGroupProps;\n\nexport const Checkbox = withInstall(_Checkbox);\nexport const CheckboxGroup = withInstall(_Group);\n\nexport default Checkbox;\n"], "names": ["Checkbox", "withInstall", "_Checkbox", "CheckboxGroup", "_Group"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWaA,QAAA,GAAWC,wBAAYC,4BAAS,EAAA;IAChCC,aAAA,GAAgBF,wBAAYG,yBAAM;;;;;;"}