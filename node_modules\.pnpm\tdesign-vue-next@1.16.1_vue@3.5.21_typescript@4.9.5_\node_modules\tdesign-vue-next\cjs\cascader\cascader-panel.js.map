{"version": 3, "file": "cascader-panel.js", "sources": ["../../../components/cascader/cascader-panel.tsx"], "sourcesContent": ["import { defineComponent } from 'vue';\nimport Panel from './components/Panel';\nimport props from './props';\n\nimport { useCascaderContext } from './hooks';\n\nexport default defineComponent({\n  name: 'TCascaderPanel',\n  props,\n  setup(props, { slots }) {\n    const { cascaderContext } = useCascaderContext(props);\n\n    return () => (\n      <Panel\n        trigger={props.trigger}\n        cascaderContext={cascaderContext.value}\n        empty={props.empty}\n        v-slots={{ empty: slots.empty, option: slots.option, loadingText: slots.loadingText }}\n      />\n    );\n  },\n});\n"], "names": ["defineComponent", "name", "props", "setup", "slots", "_ref", "_useCascaderContext", "useCascaderContext", "cascaderContext", "_createVNode", "Panel", "trigger", "value", "empty", "option", "loadingText"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,qBAAeA,mBAAgB,CAAA;AAC7BC,EAAAA,IAAM,EAAA,gBAAA;AACNC,EAAAA,KAAA,EAAAA,yBAAA;AACAC,EAAAA,KAAMD,WAANC,KAAMD,CAAAA,MAAAA,EAAAA,IAAAA,EAAkB;AAAA,IAAA,IAATE,KAAA,GAAAC,IAAA,CAAAD,KAAA,CAAA;AACb,IAAA,IAAAE,mBAAA,GAA4BC,uCAAA,CAAmBL,MAAK,CAAA;MAA5CM,eAAA,GAAAF,mBAAA,CAAAE,eAAA,CAAA;IAED,OAAA,YAAA;MAAA,OAAAC,eAAA,CAAAC,oCAAA,EAAA;QAAA,SAEMR,EAAAA,OAAMS,OACf;QAAA,iBAAiBH,EAAAA,eAAgB,CAAAI,KAAA;AAAA,QAAA,OAAA,EAC1BV,MAAAA,CAAMW,KAAAA;OACJ,EAAA;QAAEA,KAAO,EAAAT,KAAA,CAAMS,KAAO;QAAAC,MAAA,EAAQV,MAAMU,MAAQ;QAAAC,WAAA,EAAaX,KAAM,CAAAW,WAAAA;OAC1E,CAAA,CAAA;KAAA,CAAA;AAEJ,GAAA;AACF,CAAC,CAAA;;;;"}