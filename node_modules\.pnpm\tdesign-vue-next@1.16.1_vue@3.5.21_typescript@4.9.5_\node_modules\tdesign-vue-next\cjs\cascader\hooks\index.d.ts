import { Ref } from 'vue';
import { TreeNode, TreeNodeValue, TdCascaderProps, TreeNodeModel, CascaderChangeSource, CascaderValue, TreeOptionData } from '../types';
export declare const useContext: (props: TdCascaderProps, setInnerValue: TdCascaderProps["onChange"], innerPopupVisible: Ref<TdCascaderProps["popupVisible"]>, setPopupVisible: TdCascaderProps["onPopupVisibleChange"]) => {
    statusContext: {
        treeStore: any;
        inputVal: any;
        scopeVal: any;
        treeNodes: any[];
        expend: any[];
    };
    cascaderContext: import("vue").ComputedRef<{
        setTreeNodes: (nodes: TreeNode[]) => void;
        setValue: (val: CascaderValue, source: CascaderChangeSource, node?: TreeNodeModel) => void;
        setVisible: (visible: boolean, context: import("../..").PopupVisibleChangeContext) => void;
        setInputVal: (val: string) => void;
        setExpend: (val: TreeNodeValue[]) => void;
        treeStore: any;
        inputVal: any;
        scopeVal: any;
        treeNodes: any[];
        expend: any[];
        value: any;
        size: import("../..").SizeEnum;
        checkStrictly: boolean;
        lazy: boolean;
        multiple: boolean;
        filterable: boolean;
        clearable: boolean;
        checkProps: import("../..").TdCheckboxProps;
        max: number;
        disabled: boolean;
        showAllLevels: boolean;
        minCollapsedNum: number;
        valueType: "single" | "full";
        visible: boolean;
    }>;
};
export declare const useCascaderContext: (props: TdCascaderProps) => {
    cascaderContext: import("vue").ComputedRef<{
        setTreeNodes: (nodes: TreeNode[]) => void;
        setValue: (val: CascaderValue, source: CascaderChangeSource, node?: TreeNodeModel) => void;
        setVisible: (visible: boolean, context: import("../..").PopupVisibleChangeContext) => void;
        setInputVal: (val: string) => void;
        setExpend: (val: TreeNodeValue[]) => void;
        treeStore: any;
        inputVal: any;
        scopeVal: any;
        treeNodes: any[];
        expend: any[];
        value: any;
        size: import("../..").SizeEnum;
        checkStrictly: boolean;
        lazy: boolean;
        multiple: boolean;
        filterable: boolean;
        clearable: boolean;
        checkProps: import("../..").TdCheckboxProps;
        max: number;
        disabled: boolean;
        showAllLevels: boolean;
        minCollapsedNum: number;
        valueType: "single" | "full";
        visible: boolean;
    }>;
    isFilterable: import("vue").ComputedRef<boolean>;
    innerValue: Ref<CascaderValue<import("../..").TreeOptionData>>;
    getCascaderItems: (arrValue: CascaderValue[]) => TreeOptionData[];
};
